import React from 'react';
import { Badge, Tooltip } from 'antd';
import { ExclamationCircleFilled } from '@ant-design/icons';


export default (props) => {
    return (
        <Tooltip title={props.tipMessages} placement='topLeft'>
            <Badge count={props.beWarn ?
                <ExclamationCircleFilled style={{
                    color: '#E5484D',
                    borderRadius: '10px',
                    border: '1px solid #FFFFFF'
                }} /> : ''}
                size="small" offset={[-3, -5]}>
                <img src={props?.url} style={{ width: '38px', marginLeft: '-10px', marginTop: '-14px',border:'4px solid #FFFFFF' }} />
            </Badge>
        </Tooltip>


    );
}
