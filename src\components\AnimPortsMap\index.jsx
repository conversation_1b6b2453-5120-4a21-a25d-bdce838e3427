import React, { useRef, useEffect, useState, useImperativeHandle } from 'react';
import { Row, Col, Select, } from 'antd';
import './index.less';
import mapbg from '@/assets/map.png';
import { api, constant } from '@/common/utils';
import debounce from 'lodash/debounce';


const latLngToPoint = (latitude, longitude, mapWidth, mapHeight) => {
    const x = ((longitude + 180) / 360) * mapWidth;
    const y = ((90 - latitude) / 180) * mapHeight;
    return { x, y };
};
const MapComponent = ({ points, width, height, imageSrc }) => {
    const canvasRef = useRef(null);

    useEffect(() => {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        // 创建图像对象
        const image = new Image();
        image.src = imageSrc;

        // 当图像加载完成后绘制
        image.onload = () => {
            // 清空画布
            ctx.clearRect(0, 0, width, height);

            // 绘制图像
            ctx.drawImage(image, 0, 0, width, height); // 根据需要调整位置和大小

            // 绘制标记点
            [points].forEach(point => {
                const { x, y } = latLngToPoint(point.latitude, point.longitude, width, height);
                ctx.fillStyle = '#1677ff';
                ctx.beginPath();
                ctx.arc(x, y, 5, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                if (x > width / 1.5) {
                    let nameWidth = point.enName.length;
                    ctx.fillText(point.enName, x - (nameWidth * 10 + 20), y + 5);
                } else {
                    ctx.fillText(point.enName, x + 10, y + 5);
                }
            });
        };

    }, [points, width, height, imageSrc]);

    return <canvas ref={canvasRef} width={width} height={height} style={{ display: 'block' }} />;
};

export default (props) => {
    const [activePort, setActivePort] = useState({});
    const [portValue, setPortValue] = useState();
    const delayedQuery = useRef(debounce((value) => portData(value), 300)).current;
    const [points, setPoints] = useState([]);
    const [searchValue, setSearchValue] = useState(''); // 搜索值

    const current = JSON.parse(sessionStorage.getItem(constant.KEY_CURRENT_USER));
    const port_map_key = current?.id + "_port_map";

    useEffect(() => {
        if (props.label && props.label === '起运港') {
            setPoints(constant.INIT_POLS);
        } else {
            portData('');
        }

    }, [props.label]);

    const handleChange = (value) => {
        if(props.label != '起运港' && value){
            updateLocalStorageArray(port_map_key, value);
        }

        setPortValue();
        props.onChange();
        setSearchValue('');
        setOpen(false); // 关闭弹框
        if (value) {
            setPortValue(value.enName + ' ' + value.cnName);
            //组件设置值
            props.onChange(value.portEdi);
            props.getSelectItem && props.getSelectItem(value);
        }
    };

    const portData = (keywords) => {
        api.base.listPort(keywords).subscribe({
            next: (body) => {
                let storedString = localStorage.getItem(port_map_key);
                let items = storedString ? JSON.parse(storedString) : [];

                let data = [];
                data = body.map((item) => ({
                    latitude: item.latitude,
                    longitude: item.longitude,
                    enName: item.enName + "," + item.countryCode,
                    cnName: item.cnName + " | " + item.countryName,
                    portEdi: item.portEdi,
                }));

                if (items && items.length > 0 && !keywords) {
                    let temp = items.concat(data);
                    setPoints(temp);
                }else{
                    setPoints(data);
                }
            },
        });
    };

    useImperativeHandle(props.childRef, () => ({
        setValue: (value) => {
            setPortValue(value.portName);
        },
    }));

    const [open, setOpen] = useState(false);


    function updateLocalStorageArray(key, newItem) {
        let storedString = localStorage.getItem(key);
        let items = storedString ? JSON.parse(storedString) : [];
        for (let i = 0; i < items.length; i++) {
            if (items[i].portEdi === newItem.portEdi) {
                //存在 则删除
                items.splice(i, 1);
                break;
            }
        }
        //插入
        items.unshift(newItem);
        //保留5个
        if (items.length > 5) {
            items.pop();
        }
        let updatedString = JSON.stringify(items);
        //设置缓存
        localStorage.setItem(key, updatedString);
    }

    return (
        <div className="anim-port">
            <Select
                {...props}
                allowClear
                showSearch
                value={portValue}
                searchValue={searchValue}
                style={(props.labelalgin == 'right') ? { width: '100%', border: 'none', textAlign: 'right' } : { width: '100%', border: 'none' }}
                dropdownStyle={{ width: '600px', height: '160px', padding: '5px' }}
                open={open}
                onDropdownVisibleChange={(visible) => setOpen(visible)}
                dropdownRender={() => (
                    <div>
                        <Row wrap={false} style={{ width: '590px', overflow: 'hidden' }}>
                            <Col flex={'auto'} style={{ height: '150px', overflow: 'auto' }}>
                                {points.map((point, index) => <p
                                    className='portName'
                                    key={index}
                                    onMouseOver={() => { setActivePort(points[index]) }}
                                    onClick={() => { handleChange(points[index]) }}
                                    onMouseDown={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                    }}
                                >
                                    {point.enName} {point.cnName}
                                </p>)}
                            </Col>
                            <Col flex={'300px'}>
                                <MapComponent points={activePort} width={300} height={150} imageSrc={mapbg} />
                            </Col>
                        </Row>
                    </div>
                )}
                onClear={() => { handleChange(); portData(''); }}
                // mode='tags'
                onSearch={(v) => {
                    setSearchValue(v);
                    delayedQuery(v);
                }}
            />

            <label className={(props.labelalgin == 'right') ? 'right' : 'left'}>{props.label}</label>
        </div>
    );
}
