import React, { useState } from 'react';
import { Row, Col, Select, Input, InputNumber, Button, message } from 'antd';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';

export default (props) => {
    const [containerRows, setContainerRows] = useState(props.data && props.data?.length != 0 ? props.data : [{ id: 1, boxType: '', boxNum: '1', weight: '' }]);

    const editCell = (rowId, field, value) => {
        let data = containerRows.map(row => {
            if (row.id === rowId) {
                return { ...row, [field]: value };
            }
            return row;
        })
        setContainerRows(data);
        props.onChange(data);
    };

    const addCell = () => {
        if (containerRows.length === 3) {
            message.warning('最多支持三种箱型~')
            return;
        }
        const newRow = { id: Date.now(), boxType: '', boxNum: 1, weight: '' };
        let temps = [...containerRows, newRow];
        setContainerRows(temps);
        props.onChange(temps);
    };

    const deleteCell = (rowId) => {
        if (containerRows.length === 1) {
            message.warning('请至少保留一种箱型~')
            return;
        }
        let data = containerRows.filter(row => row.id !== rowId);
        setContainerRows(data);
        props.onChange(data);
    };

    return (<>

        <Row gutter={10} style={{ marginBottom: '10px' }}>
            <Col span={8}>箱型</Col>
            <Col span={8}>箱量</Col>
            <Col span={8}>货重（KGS）</Col>
        </Row>

        {containerRows.map((row) =>
            <Row gutter={10} key={row.id} style={{ marginBottom: '10px' }}>
                <Col span={8}>
                    <Select style={{ width: '100%' }} value={row.boxType} onChange={value => editCell(row.id, 'boxType', value)}>
                        <Select.Option value="20GP">20GP</Select.Option>
                        <Select.Option value="40GP">40GP</Select.Option>
                        <Select.Option value="40HC">40HC</Select.Option>
                        {/* <Select.Option value="45HC">45HC</Select.Option> */}
                    </Select>
                </Col>
                <Col span={8}>
                    <InputNumber style={{ width: '100%' }} value={row.boxNum} onChange={value => editCell(row.id, 'boxNum', value)} />
                </Col>
                <Col span={7}>
                    <Input value={row.weight} onChange={e => editCell(row.id, 'weight', e.target.value)} type='number' />
                </Col>
                <Col span={1}>
                    <Button type='text' icon={<DeleteOutlined />} onClick={() => deleteCell(row.id)}></Button>
                </Col>
            </Row>
        )}

        <Row style={{ marginBottom: '10px' }}>
            <Col span={23}>
                <Button type='dashed' icon={<PlusOutlined />} style={{ width: '100%' }} onClick={() => addCell()}>添加箱型</Button>
            </Col>
        </Row>

    </>);
}
