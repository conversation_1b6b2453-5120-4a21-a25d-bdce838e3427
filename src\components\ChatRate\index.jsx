import { api, forEach, forEachObject, isEmpty } from '@/common/utils';
import { AndroidOutlined, CloudUploadOutlined, DeleteOutlined, EditOutlined, FireOutlined, PaperClipOutlined, PlusOutlined, ReadOutlined, UserOutlined } from '@ant-design/icons';
import { Attachments, Bubble, Conversations, Prompts, Sender, Welcome, useXAgent, useXChat } from '@ant-design/x';
import { Badge, Button, Flex, Menu, Modal, Space, Spin, message } from 'antd';
import { createStyles } from 'antd-style';
import markdownit from 'markdown-it';
import React, { useEffect, useRef, useState } from 'react';
import tablemark from "tablemark";
import './index.less';

const md = markdownit({
    html: true,
    breaks: true,
});
const renderTitle = (icon, title) => (
    <Space align="start">
        {icon}
        <span>{title}</span>
    </Space>
);
const defaultConversationsItems = [
    {
        key: '0',
        label: '运价咨询',
    },
    {
        key: '1',
        label: '托书解析',
    },
    {
        key: '2',
        label: '业务知识',
    }
];
const useStyle = createStyles(({ token, css }) => {
    return {
        layout: css`
      width: 100%;
      min-width: 1000px;
      height: 822px;
      border-radius: ${token.borderRadius}px;
      display: flex;
      background: ${token.colorBgContainer};
      font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;

      .ant-prompts {
        color: ${token.colorText};
      }
    `,
        menu: css`
        border: 1px solid #e3e4e7;
        border-radius: 8px;
      background: ${token.colorBgLayout}80;
      width: 280px;
      height: 100%;
      display: flex;
      flex-direction: column;
    `,
        conversations: css`
      padding: 0px 0px;
      flex: 1;
      overflow-y: auto;
      margin-left:8px;
    `,
        chat: css`
      border: 1px solid #e3e4e7;
      height: 100%;
      width: 100%;
      border-radius: 4px;
      margin: 0 2px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding: ${token.paddingLG}px;
      gap: 8px;
    `,
        messages: css`
      flex: 1;
    `,
        placeholder: css`
      padding-top: 10px;
    `,
        sender: css`
      box-shadow: ${token.boxShadow};
    `,
        logo: css`
      display: flex;
      height: 72px;
      align-items: center;
      justify-content: start;
      padding: 0 24px;
      box-sizing: border-box;

      img {
        width: 24px;
        height: 24px;
        display: inline-block;
      }

      span {
        display: inline-block;
        margin: 0 8px;
        font-weight: bold;
        color: ${token.colorText};
        font-size: 16px;
      }
    `,
        addBtn: css`
      background: #1677ff0f;
      border: 1px solid #1677ff34;
      width: calc(100% - 12px);
      margin: 5px 0px 12px 12px;
    `,
    };
});
const placeholderPromptsItems = [
    {
        key: '1',
        label: renderTitle(
            <FireOutlined
                style={{
                    color: '#FF4D4F',
                }}
            />,
            '热门问题',
        ),
        description: '当前的咨询的热门问题?',
        children: [
            {
                key: '1-1',
                description: `青岛到迪拜的运价?`,
            },
            {
                key: '1-2',
                description: `青岛直达到特马马士基的价格?`,
            },
            {
                key: '1-3',
                description: `青岛到曼谷最短航程小柜价格?`,
            },
        ],
    },
    // {
    //     key: '2',
    //     label: renderTitle(
    //         <ReadOutlined
    //             style={{
    //                 color: '#1890FF',
    //             }}
    //         />,
    //         '使用向导',
    //     ),
    //     description: '如何来询价?',
    //     children: [
    //         {
    //             key: '2-1',
    //             icon: <HeartOutlined />,
    //             description: `起始港要告诉我哦`,
    //         },
    //         {
    //             key: '2-2',
    //             icon: <SmileOutlined />,
    //             description: `目的港也是必须的`,
    //         },
    //         {
    //             key: '2-3',
    //             icon: <CommentOutlined />,
    //             description: `箱型:20GP（小柜）、40GP（大柜）、40HC（超高、高柜）`,
    //         },
    //     ],
    // },
];
const senderPromptsItems = [
    {
        key: '1',
        description: 'Hot Topics',
        icon: (
            <FireOutlined
                style={{
                    color: '#FF4D4F',
                }}
            />
        ),
    },
    {
        key: '2',
        description: 'Design Guide',
        icon: (
            <ReadOutlined
                style={{
                    color: '#1890FF',
                }}
            />
        ),
    },
];
const roles = {
    ai: {
        placement: 'start',
        avatar: {
            icon: <AndroidOutlined />,
            style: {
                background: '#fde3cf',
            },
        },
        typing: {
            step: 5,
            interval: 20,
        },
        styles: {
            // maxWidth: 600,
            content: {
                borderRadius: 8,
                paddingTop: 7,
                margin: 0,
            },
        },
        loadingRender: () => (
            <Space>
                <Spin size="small" />
                思考中...
            </Space>
        ),
    },
    local: {
        placement: 'end',
        variant: 'shadow',
        avatar: {
            icon: <UserOutlined />,
            style: {
                background: '#87d068',
            },

        }
    },
    file: {
        placement: 'start',
        avatar: {
            icon: <AndroidOutlined />,
            style: {
                background: '#fde3cf',
            },
            // style: {
            //     visibility: 'hidden',
            // },
        },
        variant: 'borderless',
    }
};

const renderMarkdown = (content) => (


    <div className='rateResult'
        dangerouslySetInnerHTML={{
            __html: md.render(content),
        }}
    />

);
const allColumns = {
    pol: '起始港', pod: '目的港', polName: '起始港中文', podName: '目的港中文', shipCompany: '船公司', gp20Price: '20GP价格',
    gp40Price: '40GP价格', hc40Price: '40HC价格', etd: '船期', ttOrDt: '中转或直达', ttPol: '中转港', shipSchedule: '班期', shipDays: '航程'
};
let columns = [];
const keyMessage = {};
const keyModel = {};
const Independent = (props) => {
    // ==================== Style ====================
    const { styles } = useStyle();
    const [loading, setLoading] = useState(false);
    const [actionLoading, setActionLoading] = useState(false);
    // ==================== State ====================
    const [headerOpen, setHeaderOpen] = React.useState(false);
    const [content, setContent] = React.useState('');
    const [conversationsItems, setConversationsItems] = React.useState([]);
    const [activeKey, setActiveKey] = React.useState('');
    const [model, setModel] = React.useState('');
    const latestModel = useRef(model);
    const latestActiveKey = useRef(activeKey);
    const [attachedFiles, setAttachedFiles] = React.useState([]);
    const latestFiles = useRef(attachedFiles);

    // ==================== Runtime ====================

    const parsePriceChat = (data) => {
        if (!isEmpty(data.prices)) {
            columns = [];
            forEachObject((v, k, o) => {

                columns.push(allColumns[k]);

            }, data.prices[0]);
            const mean = data.chat ? data.chat + "\n" : "";
            return mean + tablemark(data.prices, {
                columns: columns
            });
        } else {
            if (data.chat) {
                return data.chat
            } else {
                return '未找到您所要求的数据，请换个查询条件试试！';
            }
        }
    }
    const [agent] = useXAgent({
        request: async ({ messages, message }, { onSuccess, onUpdate }) => {
            // console.log(latestModel.current);
            // console.log(latestFiles.current);
            setLoading(true);
            if (latestModel.current == 'pdf') {
                if (latestFiles.current && latestFiles.current.length > 0) {
                    const f = latestFiles.current[0];
                    const form = new FormData();
                    form.append("file", latestFiles.current[0].originFileObj);
                    form.append("chat", message);
                    setHeaderOpen(false);
                    setAttachedFiles([]);
                    api.ai.pdf(form).subscribe({
                        next: (resp) => {
                            const data = resp[0];
                            onUpdate(`解析文件${f.name}内容如下：`)
                            // const json = JSON.stringify(data);
                            // const markdown = "```json \n" + json + "\n```";
                            onSuccess(`解析文件[${f.name}]内容如下：\n ${data.originalData}`);
                        }
                    }).add(() => setLoading(false));
                    // setLoading(false);

                } else {
                    onSuccess('请上传一个托书文件');
                    setLoading(false);
                }

            } else {
                const id = latestActiveKey.current;
                // setLoading(false);
                api.ai.chat({ chat: message, id: id }).subscribe({
                    next: (resp) => {
                        const data = resp[0];
                        onSuccess(parsePriceChat(data));
                        // if (id == latestActiveKey.current) {
                        //     onSuccess(parsePriceChat(data));
                        // } else {
                        //     keyMessage[id] =
                        // }

                        // if (!isEmpty(data.prices)) {
                        //     columns = [];
                        //     forEachObject((v, k, o) => {

                        //         columns.push(allColumns[k]);

                        //     }, data.prices[0]);
                        //     const mean = data.mean ? data.mean + ":\n" : "";
                        //     onSuccess(mean + tablemark(data.prices, {
                        //         columns: columns
                        //     }))
                        // } else {
                        //     if (data.chat) {
                        //         onSuccess(data.chat)
                        //     } else {
                        //         onSuccess('未找到您所要求的数据，请换个查询条件试试！');
                        //     }
                        // }
                    }
                }).add(() => setLoading(false));
            }
        },
    });
    const { onRequest, messages, setMessages } = useXChat({
        agent,
    });

    const rename = (name) => {
        api.ai.rename({ id: activeKey, title: name }).subscribe({
            next: (data) => {
                setConversationsItems(conversationsItems.map(item => {
                    if (item.key == activeKey) {
                        item.label = data[0].title;
                    }
                    return item;
                }));
            }
        })
    }

    // ==================== Event ====================
    const onSubmit = (nextContent) => {
        if (!nextContent) return;
        console.log(latestModel.current);
        console.log(items);
        if (latestModel.current == 'pdf') {
            if (latestFiles.current && latestFiles.current.length > 0) {
                const f = latestFiles.current[0];
                onRequest(nextContent + ",托书文件[" + f.name + "]");
            } else {
                message.error('请上传一个托书文件');
                return;
            }
        } else {
            if (items.length == 0) {
                rename(nextContent);
            }
            onRequest(nextContent);
        }
        setContent('');
    };
    const onPromptsItemClick = (info) => {
        if (items.length == 0) {
            rename(info.data.description);
        }
        latestModel.current = 'price';
        onRequest(info.data.description);
    };
    const onAddConversation = (chat) => {
        keyModel[chat.id] = chat.model;
        setConversationsItems([
            ...conversationsItems,
            {
                key: `${chat.id}`,
                label: `${chat.title}`,
            },
        ]);
        keyMessage[activeKey] = messages || [];
        setActiveKey(`${chat.id}`);
    };

    const menuConfig = (conversation) => ({
        items: [
            {
                label: '重命名',
                key: 'rename',
                icon: <EditOutlined />,
                disabled: true,
            },
            {
                label: '删除',
                key: 'delete',
                icon: <DeleteOutlined />,
                danger: true,
            },
        ],
        onClick: (menuInfo) => {
            if (menuInfo.key == 'delete') {
                Modal.confirm({
                    title: `确认要删除改对话吗？`,
                    okText: '删除',
                    okType: 'danger',
                    cancelText: '取消',
                    onOk() {
                        api.ai.deleteChat(conversation.key).subscribe({
                            next: data => {
                                setConversationsItems([]);
                                loadHistory();
                            }
                        });
                    },
                });
            } else if (menuInfo.key == 'rename') {

            }
            // message.info(`Click ${conversation.key} - ${menuInfo.key}`);
        },
    });

    const onConversationClick = (key) => {
        keyMessage[activeKey] = messages || [];
        latestModel.current = 'price';
        latestActiveKey.current = key;
        setActiveKey(key);
    };
    const handleFileChange = (info) => {
        latestFiles.current = info.fileList
        setAttachedFiles(info.fileList);
    }

    // ==================== Nodes ====================
    const placeholderNode = (
        <Space direction="vertical" size={16} className={styles.placeholder}>
            <Welcome
                variant="borderless"
                icon="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
                title="你好, 我是小爱"
                description="我是您的运价小助手，有运价的问题就咨询我吧，仅限运价噢"
                // extra={}
            />
            <Prompts
                // title="您想要知道?"
                items={placeholderPromptsItems}
                styles={{
                    list: {
                        width: '100%',
                    },
                    item: {
                        flex: 1,
                    },
                }}
                onItemClick={onPromptsItemClick}
            />
        </Space>
    );

    const pdfPlaceholderNode = (
        <Space direction="vertical" size={16} className={styles.placeholder}>
            <Welcome
                variant="borderless"
                icon="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
                title="你好, 我是小美"
                description="我是您的托书小助手，最擅长解析托书，托书问题交给我吧！"
                // extra={
                //     <Space>
                //         <Button icon={<ShareAltOutlined />} />
                //         <Button icon={<EllipsisOutlined />} />
                //     </Space>
                // }
            />
            {/* <Prompts
                title="您想要知道?"
                items={placeholderPromptsItems}
                styles={{
                    list: {
                        width: '100%',
                    },
                    item: {
                        flex: 1,
                    },
                }}
                onItemClick={onPromptsItemClick}
            /> */}
        </Space>
    );
    const items = messages.map(({ id, message, status }) => {
        if (status === 'file') {
            return ({
                key: id,
                // loading: status === 'loading',
                role: 'file',
                content: message,
                variant: 'filled',
                styles: { margin: '0 6', padding: 0 },
                messageRender: (items) => {
                    return (<Flex vertical gap="left">
                        {items.map((item) => (
                            <Attachments.FileCard key={item.uid} item={item} />
                        ))}
                    </Flex>)
                },
            })
        } else {
            return ({
                key: id,
                // loading: status === 'loading',
                role: status === 'local' ? 'local' : 'ai',
                messageRender: renderMarkdown,
                content: message,
                variant: 'filled',
                styles: { margin: '0 6', padding: 0 }
            })
        }
    });
    const attachmentsNode = activeKey == '1' && (
        <Badge dot={attachedFiles.length > 0 && !headerOpen}>
            <Button type="text" icon={<PaperClipOutlined />} onClick={() => setHeaderOpen(!headerOpen)} />
        </Badge>
    );
    const senderHeader = (
        <Sender.Header
            title="托书识别"
            open={headerOpen}
            onOpenChange={setHeaderOpen}
            styles={{
                content: {
                    padding: 0,
                },
            }}
        >

            <Attachments
                accept=".pdf"
                beforeUpload={(file) => {
                    return new Promise((resolve, reject) => {
                        if (file.size / (1024 * 1024) < 30) {
                            resolve();
                        } else {
                            const err = '上传文件大小应小于30M';
                            message.error(err);
                            reject(new Error(err));
                        }
                    });
                }}
                items={attachedFiles}
                onChange={handleFileChange}
                placeholder={(type) =>
                    type === 'drop'
                        ? {
                            title: 'Drop file here',
                        }
                        : {
                            icon: <CloudUploadOutlined />,
                            title: '上传托书',
                            description: '仅支持PDF格式',
                        }
                }
            />
        </Sender.Header>
    );
    const logoNode = (
        <div className={styles.logo}>
            <img
                src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*eco6RrQhxbMAAAAAAAAAAAAADgCCAQ/original"
                draggable={false}
                alt="logo"
            />
            <span>DSI AI助手</span>
        </div>
    );

    const handleNewChat = (key, beForce) => {
        setActionLoading(true);
        api.ai.newChat(key).subscribe({
            next: (data) => {
                setModel(key);
                if (beForce === true) {
                    const chat = data[0];
                    setConversationsItems([{
                        key: `${chat.id}`,
                        label: `${chat.title}`,
                    }]);
                } else {
                    onAddConversation(data[0]);
                }
                setActiveKey(data[0].id);
                latestActiveKey.current = data[0].id;
            }
        }).add(() => setActionLoading(false));
    };

    const menu = (
        <Menu loading={actionLoading} onClick={(e) => handleNewChat(e.key)}>
            <Menu.Item key="price">运价咨询</Menu.Item>
            <Menu.Item key="pdf">托书解析</Menu.Item>
        </Menu>
    );

    const loadHistory = () => {
        api.ai.chatHistory().subscribe({
            next: (data) => {
                if (data && data.length > 0) {
                    const chats = [];
                    forEach((v) => {
                        chats.push({
                            key: v.id,
                            label: v.title,
                        });
                        keyModel[v.id] = v.model;
                    }, data);
                    setConversationsItems(chats);
                    setActiveKey(data[0].id);
                    latestActiveKey.current = data[0].id;
                } else {
                    // setConversationsItems([]);
                    handleNewChat('price', true);
                }

            }
        });
    }

    const loadChatDetail = (id) => {
        api.ai.chatDetail(id).subscribe({
            next: (data) => {
                if (!data) {
                    return;
                }
                const msgs = [];
                forEach((v) => {
                    const json = JSON.parse(v.content);
                    console.log(json);
                    if (v.role == 'local') {
                        msgs.push({
                            id: v.id,
                            message: json.chat,
                            status: 'local',
                        });
                    } else {
                        msgs.push({
                            id: v.id,
                            message: parsePriceChat(json),
                            status: 'ai',
                        });

                    }
                }, data)
                keyMessage[id] = msgs;
                setMessages(keyMessage[id] || []);
            }
        });
    }

    useEffect(() => {
        if (props.open == true) {
            loadHistory();
        }
        // loadHistory();
    }, [props.open]);

    useEffect(() => {
        if (activeKey) {

            if (keyMessage[activeKey] && keyMessage[activeKey].length > 0) {
                setMessages(keyMessage[activeKey] || []);
            } else {
                loadChatDetail(activeKey);
            }

        }
    }, [activeKey]);

    // ==================== Render =================
    return (<Modal
        title="DSI AI助手"
        width={1600}
        open={props.open}
        onCancel={props.onCancel}
        maskClosable={false}
        footer={null}
        centered
    >
        <div className={styles.layout}>
            <div className={styles.menu}>
                {/* 🌟 Logo */}
                {/* logoNode */}
                {/* 🌟 添加会话 */}
                {/* <Dropdown overlay={menu} trigger={['click']} placement="bottom">
                    <Button key="more">
                        添加会话 <PlusOutlined />
                    </Button>
                </Dropdown> */}
                <Button
                    onClick={() => handleNewChat('price')}
                    type="link"
                    className={styles.addBtn}
                    icon={<PlusOutlined />}
                >
                    添加会话
                </Button>
                {/* 🌟 会话管理 */}
                <Conversations
                    menu={menuConfig}
                    items={conversationsItems}
                    className={styles.conversations}
                    activeKey={activeKey}
                    onActiveChange={onConversationClick}
                />
            </div>
            <div className={styles.chat}>
                {/* 🌟 消息列表 */}
                <Bubble.List
                    items={

                        items.length > 0
                            ? (loading ? [...items,
                            {
                                key: 'ai',
                                role: 'ai',
                                loading: true,
                            }
                            ] : items)
                            : (model === 'pdf' ? [{
                                content: pdfPlaceholderNode,
                                variant: 'borderless',
                            }] : [
                                {
                                    content: placeholderNode,
                                    variant: 'borderless',
                                },
                            ])
                    }
                    roles={roles}
                    className={styles.messages}
                />

                <Prompts onItemClick={onPromptsItemClick} />
                <Sender
                    value={content}
                    header={senderHeader}
                    onSubmit={onSubmit}
                    onChange={setContent}
                    prefix={attachmentsNode}
                    // loading={agent.isRequesting()}
                    loading={loading}
                    className={styles.sender}
                />
            </div>
        </div>
    </Modal>);
};
export default Independent;