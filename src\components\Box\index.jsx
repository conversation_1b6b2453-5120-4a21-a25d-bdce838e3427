import { Select } from 'antd';

export default (props) => {

    const ctnOptions = [
        { value: '20GP', label: '20GP' },
        { value: '40GP', label: '40GP' },
        { value: '40HC', label: '40HC' },
        // { value: '45HC', label: '45HC' },
    ]

    return (
        <Select {...props} placeholder="箱型" options={ctnOptions} mode='multiple'
            allowClear maxTagCount={1}></Select>
    );
}
