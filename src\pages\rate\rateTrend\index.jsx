import React, { useState, useEffect } from 'react';
import { Modal, Col, Form, Row, Input, Spin } from 'antd';
import { Line } from '@ant-design/plots';
import NoData from '@/components/NoData';
import { api, } from '@/common/utils';

export default (props) => {

    const { current, open, onCancel, onSubmit } = props;
    const [loading, setLoading] = useState(false);
    const [datas, setDatas] = useState([]);
    const [form] = Form.useForm();
    useEffect(() => {

        if (open) {
            form.setFieldsValue(current);
            onSearch();
        } else {
            setDatas([]);
        }


    }, [current?.id, open]);


    const onSearch = () => {
        let searchData = { pol: current.pol, pod: current.pod, shipCompanys: [current.shipCompany], boxTypes: ['20GP', '40GP', '40HC'] };
        setLoading(true);
        api.freightPrice.listPriceTrend(searchData).subscribe({
            next: (data) => {
                setDatas(data);
            }
        }).add(() => {
            setLoading(false);
        });
    };

    const config = {
        data: datas,
        xField: 'etdStr',
        yField: 'price',
        colorField: 'boxType',
        legend: {
            color: {
                layout: {
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                },
                itemMarkerSize: 10,
            },
        },
        point: {
            shapeField: 'circle',
            sizeField: 2,
        },
        insetTop: 20,
        // shapeField: 'smooth',
        scale: { color: { range: ['#30BF78', '#F4664A', '#FAAD14', '#66F66A'] } },
        style: {
            lineWidth: 2,

        },
    };

    return (
        <>


            <Modal
                title='运价走势'
                className='odm-modal'
                width={800}
                open={open}
                onCancel={onCancel}
                styles={{
                    body: {
                        height: '500px',
                    }
                }}
                maskClosable={false}
                footer={null}
                centered
                modalRender={(modal) => (
                    <Spin spinning={loading} delay={10}>
                        {modal}
                    </Spin>
                )}
            >
                <Form form={form}>
                    <Row wrap={false} gutter={20}>
                        <Col flex={'auto'}>
                            <Row>
                                <Col span={9}>
                                    <Form.Item name="polName" label="起运港" labelCol={{ flex: '90px' }}>
                                        <Input readOnly />
                                    </Form.Item>
                                </Col>
                                <Col span={9}>
                                    <Form.Item name="podName" label="目的港" labelCol={{ flex: '90px' }}>
                                        <Input readOnly />
                                    </Form.Item>
                                </Col>
                                <Col span={6}>
                                    <Form.Item name="shipCompany" label="船公司" labelCol={{ flex: '90px' }}>
                                        <Input readOnly />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Col>

                    </Row>

                    <div style={{ height: '450px', }}>
                        {datas.length == 0 ?
                            <NoData />
                            :
                            <Line {...config} />
                        }

                    </div>
                </Form>


            </Modal>


        </>
    )
}