import React, { useState, useMemo, useRef, useEffect, useImperativeHandle } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { Typography, Button, Space, Drawer, Pagination, message } from 'antd';
import { SettingOutlined, SyncOutlined, FullscreenOutlined } from '@ant-design/icons';
import 'ag-grid-community/dist/styles/ag-grid.css';
import './ag-theme-alpine.css';

import './index.less';
import { forEach, constant, isFunction, api, } from '@/common/utils';

export default React.forwardRef((props, ref) => {
  const {
    gridName,
    title,
    initColumns,
    request,
    onDoubleClick,
    onClick,
    onCellValueChanged,
    cellClickedSelectRow,
    onSelectedChanged,
    rowSelection,
    pageSizeList,
    dataSource,
    toolBarRender,
    pageToolBarRender,
    pageToolBar,
    height,
    total,
    optionsHide,
    pageNo,
    pageSize,
    defaultSearch,
    showQuickJumper,
    showTotal,
    showSizeChanger,
    clearSelect
  } = props;


  const [selectedRows, setSelectedRows] = useState([]);
  //主表格API
  const [gridApi, setGridApi] = useState(null);
  //主表格列API
  const [gridColumnApi, setGridColumnApi] = useState(null);

  const [gridColumns, setGridColumns] = useState(initColumns);  // 表格列

  const [isOpen, setIsOpen] = useState(false);
  const [columnData, setColumnData] = useState([]);  // 设置列数据 


  const gridRef = useRef();
  const settingRef = useRef();

  const gridNameIndex = 'Grid_' + gridName; // localStorage 前缀

  const refresh = (params) => {
    //setBerefresh(!beRefresh);
    request && request(1, pageSize || 10, params);
  };

  const reset = () => {
    //setBerefresh(!beRefresh);
    request && request(1, pageSize || 10);
  };

  useImperativeHandle(ref, () => ({
    refresh: (params) => {
      // 这里可以加自己的逻辑哦
      refresh(params);
    },
    reset: () => {
      reset();
    },
    getGridApi: () => {
      return gridApi;
    },
  }), []);

  const NoRowsOverlay = () => {
    return <>
      <svg xmlns="http://www.w3.org/2000/svg" width="64" height="41" className="ant-empty-img-simple" viewBox="0 0 64 41">
        <g fill="none" fillRule="evenodd" transform="translate(0 1)">
          <ellipse cx="32" cy="33" className="ant-empty-img-simple-ellipse" rx="32" ry="7" />
          <g fillRule="nonzero" className="ant-empty-img-simple-g">
            <path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z" />
            <path
              d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
              className="ant-empty-img-simple-path"
            />
          </g>
        </g>
      </svg>
      <div className="empty-tips" style={{ marginTop: 8, color: 'rgba(0,0,0,.25)', fontSize: 14 }}>
        暂无数据
      </div>
    </>
  }

  const NoRowsOverlayEmpty = () => {
    return <></>
  }

  useEffect(() => {

    if (defaultSearch) {
      getRowData(pageNo, pageSize || 10);
    }

  }, []);

  useEffect(() => {
    gridApi && gridApi.deselectAll();
  }, [clearSelect]);

  const getRowData = (current, size) => {
    request && request(current, size)
  }

  const gridReady = (params) => {
    setGridApi(params.api);
    setGridColumnApi(gridRef.current?.columnApi);
    let customColumn = JSON.parse(localStorage.getItem(gridNameIndex));
    if (customColumn) {
      doSetColumns(customColumn);
    } else {
      const current = JSON.parse(sessionStorage.getItem(constant.KEY_CURRENT_USER));
      if (current && current.id) {
        api.columnSetting.getColumn(gridNameIndex).subscribe({
          next: (data) => {
            let flag = data && data.length > 0;
            if (flag) {
              doSetColumns(JSON.parse(data[0].note));
            }
          }
        }).add(() => {

        });
      }

    }
  }
  const doSetColumns = (customColumn) => {
    let initColumn = [], hideColumn = [];
    let remainNode = initColumns;
    customColumn.forEach((node) => {
      let tempNode = remainNode.filter(element => element.field == node.field)[0];
      if (tempNode) {
        remainNode = remainNode.filter(element => element.field != node.field);
        tempNode.width = node.width;
        if (node.visible == false) {
          hideColumn.push(tempNode.field);
        }
        initColumn.push(tempNode);
      }
    })
    initColumn = initColumn.concat(remainNode);

    setGridColumns(initColumn);
    hideColumn.forEach((node) => {
      gridRef.current?.columnApi.setColumnVisible(node, false);
    })
  }


  //打开Column设置面板
  const openSetting = () => {
    setIsOpen(true);
    const cols = gridColumnApi.getAllGridColumns();
    let colNames = [];
    let nodeSelect = [];
    cols.map((item) => {
      colNames.push({ "field": item.getId(), "headerName": item.getColDef().headerName, "width": item.getActualWidth(), "visible": item.isVisible() });
      if (item.isVisible()) { nodeSelect.push(item.getId()); }
    });
    setColumnData(colNames);
    setTimeout(() => {
      settingRef.current.api.forEachNodeAfterFilter(
        node => {
          let index = node.data.field;
          node.setSelected(nodeSelect.indexOf(index) >= 0);
        }
      );
    }, 100);
  }

  // 通用列属性
  const defaultCol = useMemo(() => {
    return {
      resizable: true,
      minWidth: 60,
    };
  }, []);

  // 拖拽顺序
  const settingDragEnd = (e) => {
    gridColumnApi.moveColumn(e.node.data.field, e.overIndex);
  }

  // 选择显示
  const settingChanged = () => {
    const selectedRows = settingRef.current.api.getSelectedRows();
    let settingCols = []
    selectedRows.forEach(element => { settingCols.push(element.field) });
    const cols = gridColumnApi.getAllGridColumns();
    let visibleCols = []
    cols.forEach(element => {
      if (element.isVisible()) {
        visibleCols.push(element.getId())
      }
    });
    // 隐藏列
    if (visibleCols.length > settingCols.length) {
      let actionNode = visibleCols.filter((item) => { return settingCols.indexOf(item) < 0 });
      gridColumnApi.setColumnVisible(actionNode[0], false);
      columnData.forEach(item => {
        if (item.field == actionNode[0]) { item.visible = false }
      })
    }
    // 显示列
    if (visibleCols.length < settingCols.length) {
      let actionNode = settingCols.filter((item) => { return visibleCols.indexOf(item) < 0 });
      gridColumnApi.setColumnVisible(actionNode[0], true);
      columnData.forEach(item => {
        if (item.field == actionNode[0]) { item.visible = true }
      })
    }
  }

  // 保存
  const saveSetting = () => {
    let colNames = [];
    settingRef.current.api.forEachNode((node, index) => {
      let item = node.data;
      colNames.push(item)
    })
    localStorage.setItem(gridNameIndex, JSON.stringify(colNames));
    let param = { columnName: gridNameIndex, note: JSON.stringify(colNames) }
    saveOrUpdateColumn(param);
  }

  // 清空还原
  const clearSetting = () => {
    localStorage.removeItem(gridNameIndex);
    setColumnData(initColumns);
    setTimeout(() => {
      gridRef.current.api.setColumnDefs(initColumns);
      settingRef.current.api.forEachNodeAfterFilter(
        node => { node.setSelected(true) }
      )

      saveSetting();
    }, 100);
  }

  //双击
  const onCellDoubleClicked = (param) => {
    onDoubleClick && onDoubleClick(param.data);
  };

  //点击
  const onCellClicked = (param) => {
    onClick && onClick(param);
    if (cellClickedSelectRow) {
      param.node.setSelected(true);
    }
  };

  // Setting-列属性
  const columnCols = [
    {
      headerName: '自定义列属性',
      field: 'headerName',
      lockPosition: 'left',
      checkboxSelection: true,
      with: 100,
      rowDrag: true
    }
  ];


  const saveOrUpdateColumn = (param) => {
    api.columnSetting.saveColumn(param).subscribe({
      next: (data) => {
        setIsOpen(false);
        message.success("操作成功!")
      },
    }).add(() => {
      setLoading(false);
    });
  }

  return (
    // <div className="ag-container" style={{  }}>
    // <div className="ag-container" style={{ height:  'calc(100vh - (56px + 50px + 60px))'}}> 
    <div className="ag-container" style={{ maxHeight: 'calc(100vh - (56px + 50px + 60px))', height: pageSize * 42 + 91 }}>
      <div className="ag-tools" style={{ display: optionsHide?.topTool ? 'none' : '', }}>
        <div className='ag-tools-left'>
          <Typography.Text>{title}</Typography.Text>
        </div>
        <div className='ag-tools-right'>
          <Space>
            {toolBarRender && toolBarRender.map((obj) => obj)}
            {/* {topToolBar && topToolBar()} */}
            <Space.Compact block>
              {/* <Button size='samll' type="text" icon={<SyncOutlined />} /> */}
              {/* <Button size='samll' type="text" icon={<FullscreenOutlined />} /> */}
              <Button size='samll' type="text" icon={<SettingOutlined />} onClick={openSetting} />
            </Space.Compact>
          </Space>
        </div>
      </div>

      <div className="ag-body ag-theme-alpine">
        <AgGridReact
          ref={gridRef}
          rowData={dataSource} // 表格数据
          columnDefs={gridColumns} // 列数据
          defaultColDef={defaultCol} // 列属性设置
          rowSelection={rowSelection || 'multiple'} // 行选择设置
          onSelectionChanged={(e) => {
            const nodes = e.api.getSelectedNodes() || [];
            const datas = [];
            forEach((v) => {
              datas.push(v.data);
            }, nodes);
            if (isFunction(onSelectedChanged)) {
              onSelectedChanged(datas);
            }
            setSelectedRows(datas);
          }} // 行选择数据
          onCellDoubleClicked={onCellDoubleClicked}
          onCellClicked={onCellClicked}
          onCellValueChanged={onCellValueChanged}
          singleClickEdit={true}
          rowMultiSelectWithClick={true}
          onGridReady={gridReady}
          //suppressDragLeaveHidesColumns={true} // 拖拽隐藏
          rowDragManaged={true} // 允许拖动
          animateRows={true} // 行动画
          noRowsOverlayComponent={optionsHide?.noDatasEmpty ? NoRowsOverlayEmpty : NoRowsOverlay}
          overlayLoadingTemplate={`<span></span>`}
        />
        <Drawer
          onClose={() => setIsOpen(false)}
          closable={false}
          open={isOpen}
          width={220}
          getContainer={false}
          styles={{ body: { padding: '5px' }, footer: { textAlign: 'center' } }}
          footer={
            <Space>
              <Button size="small" onClick={clearSetting}>还原</Button>
              <Button size="small" type="primary" onClick={saveSetting}>保存</Button>
            </Space>
          }
          className='ag-setting'
        >
          <AgGridReact
            ref={settingRef}
            rowData={columnData} // 设置表格数据
            columnDefs={columnCols} // 设置列数据
            rowSelection="multiple"
            rowDragManaged={true}
            animateRows={true}
            suppressRowClickSelection={true}
            onRowDragEnd={settingDragEnd}
            onSelectionChanged={settingChanged}
          />
        </Drawer>
      </div>

      <div className="ag-pagination" style={{ display: optionsHide?.pagination ? 'none' : '', height: 25 }}>
        <Pagination
          className='pagination-content'
          showSizeChanger={showSizeChanger === false ? false : true}
          showQuickJumper={showQuickJumper === false ? false : true}
          size="small"
          defaultPageSize={pageSize || 10}
          current={pageNo || 0}
          total={total || 0}
          showTotal={showTotal === false ? false : (total, range) => `第${range[0]}-${range[1] || 0}条/共${total}条`}
          pageSizeOptions={pageSizeList || [10, 25, 50, 100, 500, 1000]}
          onChange={(page, pageSize) => {
            getRowData(page, pageSize);
          }}
        />
        <div className="pagination-setting">
          <Button size='samll' type="text" icon={<SettingOutlined />} onClick={openSetting} />
        </div>
      </div>

    </div>
  );
});