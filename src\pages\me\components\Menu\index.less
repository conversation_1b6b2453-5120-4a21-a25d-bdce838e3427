.me-content {
    width: 1200px;
    margin: 0 auto;
    padding: 20px 0;
    display: flex;


    .me-menu {
        width: auto;
        position: relative;

        .me-menu-toggle {
            position: absolute;
            top: 36px;
            right: -10px;
        }

        .user-info {
            background: #fff;
            border-right: 1px solid rgba(5, 5, 5, 0.06);
            padding: 10px;

            .user-info-expand {
                height: 100px;
                border-radius: 6px;
                overflow: hidden;
                background: url("../../../../assets/me-bg.png") no-repeat center bottom;

                .user-avatar {
                    margin: 12px 16px;
                    display: flex;
                    overflow: hidden;

                    img {
                        width: 32px;
                        height: 32px;
                        display: block;
                    }

                    .user-name {
                        font-size: 14px;
                        line-height: 32px;
                        margin-left: 10px;
                        width: 100px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        word-break: keep-all;
                        white-space: nowrap;
                    }
                }

                .user-company {
                    font-size: 12px;
                    margin: 0px 16px;
                    color: #8C7676;
                    white-space: nowrap;
                    overflow: hidden;
                }
            }

            .user-info-collapsed {
                margin: 0 -2px;

                .user-avatar {

                    img {
                        width: 30px;
                        height: 30px;
                        display: block;
                    }

                    .user-name {
                        display: none;
                    }
                }

                .user-company {
                    display: none;
                }
            }
        }
    }

    .me-main {
        width: 100%;
        min-height: 720px;
        background: #fff;
        margin-left: 20px;
        border-radius: 6px;

        .me-search {
            margin: 24px;
            border-bottom: 1px solid #f0f0f0;
        }

        .me-shipment-tabs {
            display: flex;
            margin: 0 24px;

            img {
                width: 48px;
                height: 48px;
                border: 1px solid #f0f0f0;
                border-radius: 6px;
                margin: 0 12px;
            }
        }

        .me-trend {
            margin: 24px;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
        }
    }
}