import { useEffect } from 'react';

const ChatwootWidget = () => {
  useEffect(() => {
    // 检查是否已经加载过Chatwoot脚本
    if (window.chatwootSDK) {
      console.log('Chatwoot SDK already loaded');
      return;
    }

    console.log('Loading Chatwoot SDK...');

    // 使用原始的Chatwoot集成方式
    const BASE_URL = "http://************:3000";

    // 创建脚本标签
    const script = document.createElement('script');
    script.innerHTML = `
      (function(d,t) {
        var BASE_URL="${BASE_URL}";
        var g=d.createElement(t),s=d.getElementsByTagName(t)[0];
        g.src=BASE_URL+"/packs/js/sdk.js";
        g.async = true;
        s.parentNode.insertBefore(g,s);
        g.onload=function(){
          console.log('Chatwoot SDK loaded via inline script');
          if (window.chatwootSDK) {
            window.chatwootSDK.run({
              websiteToken: 'KNGYN9tZffVKpCVV6BCAT86e',
              baseUrl: BASE_URL
            });
            console.log('Chatwoot widget initialized');
            console.log('Available methods:', Object.keys(window.chatwootSDK));
          }
        }
        g.onerror=function(){
          console.error('Failed to load Chatwoot SDK');
        }
      })(document,"script");
    `;

    document.head.appendChild(script);

    // 清理函数
    return () => {
      // 如果需要在组件卸载时清理Chatwoot，可以在这里添加清理逻辑
      // 注意：通常不需要移除Chatwoot，因为它应该在整个应用中保持可用
    };
  }, []);

  // 这个组件不渲染任何可见内容
  return null;
};

export default ChatwootWidget;
