import { useEffect } from 'react';

const ChatwootWidget = () => {
  useEffect(() => {
    // 检查是否已经加载过Chatwoot脚本
    if (window.chatwootSDK) {
      return;
    }

    // 创建并加载Chatwoot脚本
    const BASE_URL = "http://************:3000";
    const script = document.createElement('script');
    script.src = `${BASE_URL}/packs/js/sdk.js`;
    script.async = true;
    
    script.onload = function() {
      window.chatwootSDK.run({
        websiteToken: 'KNGYN9tZffVKpCVV6BCAT86e',
        baseUrl: BASE_URL
      });
    };

    document.head.appendChild(script);

    // 清理函数
    return () => {
      // 如果需要在组件卸载时清理Chatwoot，可以在这里添加清理逻辑
      // 注意：通常不需要移除Chatwoot，因为它应该在整个应用中保持可用
    };
  }, []);

  // 这个组件不渲染任何可见内容
  return null;
};

export default ChatwootWidget;
