import { useEffect } from 'react';

const ChatwootWidget = () => {
  useEffect(() => {
    // 检查是否已经加载过Chatwoot脚本
    if (window.chatwootSDK) {
      console.log('Chatwoot SDK already loaded');
      return;
    }

    console.log('Loading Chatwoot SDK...');

    // 直接使用你提供的原始脚本
    (function(d,t) {
      var BASE_URL="http://192.168.1.58:3000";
      var g=d.createElement(t),s=d.getElementsByTagName(t)[0];
      g.src=BASE_URL+"/packs/js/sdk.js";
      g.async = true;
      s.parentNode.insertBefore(g,s);
      g.onload=function(){
        console.log('Chatwoot SDK loaded successfully');
        window.chatwootSDK.run({
          websiteToken: 'KNGYN9tZffVKpCVV6BCAT86e',
          baseUrl: BASE_URL
        });
        console.log('Chatwoot widget initialized');
      }
      g.onerror=function(){
        console.error('Failed to load Chatwoot SDK');
      }
    })(document,"script");

    // 清理函数
    return () => {
      // 如果需要在组件卸载时清理Chatwoot，可以在这里添加清理逻辑
      // 注意：通常不需要移除Chatwoot，因为它应该在整个应用中保持可用
    };
  }, []);

  // 这个组件不渲染任何可见内容
  return null;
};

export default ChatwootWidget;
