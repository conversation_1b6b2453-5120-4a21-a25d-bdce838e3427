import { useEffect } from 'react';
import { setChatwootUser, resetChatwoot } from '@/common/chatwootUtils';

const ChatwootWidget = () => {
  useEffect(() => {
    // 检查是否已经加载过Chatwoot脚本
    if (window.chatwootSDK) {
      console.log('Chatwoot SDK already loaded');
      // 如果已经加载，直接设置用户信息
      setChatwootUser();
      return;
    }

    console.log('Loading Chatwoot SDK...');

    // 直接使用你提供的原始脚本，不添加额外延迟
    (function(d,t) {
      var BASE_URL="http://192.168.1.58:3000";
      var g=d.createElement(t),s=d.getElementsByTagName(t)[0];
      g.src=BASE_URL+"/packs/js/sdk.js";
      g.async = true;
      s.parentNode.insertBefore(g,s);
      g.onload=function(){
        console.log('Chatwoot SDK loaded successfully');

        window.chatwootSDK.run({
          websiteToken: 'KNGYN9tZffVKpCVV6BCAT86e',
          baseUrl: BASE_URL
        });
        console.log('Chatwoot widget initialized');

        // 监听Chatwoot的ready事件
        window.addEventListener('chatwoot:ready', () => {
          console.log('Chatwoot is ready, setting user info');
          setChatwootUser();
        });

        // 如果没有ready事件，延迟一点设置用户信息
        setTimeout(() => {
          if (window.$chatwoot && window.$chatwoot.hasLoaded) {
            setChatwootUser();
          }
        }, 2000);
      }
      g.onerror=function(){
        console.error('Failed to load Chatwoot SDK');
      }
    })(document,"script");

    // 监听用户登录状态变化
    const handleStorageChange = (e) => {
      if (e.key === '_CURRENT_USER' && window.chatwootSDK) {
        console.log('User login status changed, updating Chatwoot user info');
        setTimeout(() => {
          if (e.newValue) {
            // 用户登录了
            const currentUser = JSON.parse(e.newValue);
            setChatwootUser(currentUser);
          } else {
            // 用户登出了，重置Chatwoot
            resetChatwoot();
          }
        }, 500);
      }
    };

    // 添加storage事件监听器
    window.addEventListener('storage', handleStorageChange);

    // 清理函数
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // 这个组件不渲染任何可见内容
  return null;
};

export default ChatwootWidget;
