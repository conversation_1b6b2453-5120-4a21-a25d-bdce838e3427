import { useEffect } from 'react';

const ChatwootWidget = () => {
  useEffect(() => {
    // 检查是否已经加载过Chatwoot脚本
    if (window.chatwootSDK) {
      console.log('Chatwoot SDK already loaded');
      return;
    }

    console.log('Loading Chatwoot SDK...');

    // 创建并加载Chatwoot脚本
    const BASE_URL = "http://************:3000";
    const script = document.createElement('script');
    script.src = `${BASE_URL}/packs/js/sdk.js`;
    script.async = true;

    script.onload = function() {
      console.log('Chatwoot SDK loaded successfully');
      if (window.chatwootSDK) {
        window.chatwootSDK.run({
          websiteToken: 'KNGYN9tZffVKpCVV6BCAT86e',
          baseUrl: BASE_URL,
          // 确保聊天气泡显示
          hideMessageBubble: false,
          // 设置位置
          position: 'right',
          // 设置语言
          locale: 'zh'
        });
        console.log('Chatwoot widget initialized');
      } else {
        console.error('Chatwoot SDK not found after script load');
      }
    };

    script.onerror = function() {
      console.error('Failed to load Chatwoot SDK');
    };

    document.head.appendChild(script);

    // 清理函数
    return () => {
      // 如果需要在组件卸载时清理Chatwoot，可以在这里添加清理逻辑
      // 注意：通常不需要移除Chatwoot，因为它应该在整个应用中保持可用
    };
  }, []);

  // 这个组件不渲染任何可见内容
  return null;
};

export default ChatwootWidget;
