import { useEffect } from 'react';

const ChatwootWidget = () => {
  useEffect(() => {
    // 检查是否已经加载过Chatwoot脚本
    if (window.chatwootSDK) {
      console.log('Chatwoot SDK already loaded');
      return;
    }

    console.log('Loading Chatwoot SDK...');

    // 直接使用你提供的原始脚本
    (function(d,t) {
      var BASE_URL="http://192.168.1.58:3000";
      var g=d.createElement(t),s=d.getElementsByTagName(t)[0];
      g.src=BASE_URL+"/packs/js/sdk.js";
      g.async = true;
      s.parentNode.insertBefore(g,s);
      g.onload=function(){
        console.log('Chatwoot SDK loaded successfully');
        console.log('window.chatwootSDK:', window.chatwootSDK);

        window.chatwootSDK.run({
          websiteToken: 'KNGYN9tZffVKpCVV6BCAT86e',
          baseUrl: BASE_URL
        });
        console.log('Chatwoot widget initialized');

        // 调试：检查DOM中的Chatwoot元素
        setTimeout(() => {
          console.log('=== Chatwoot Debug Info ===');
          const bubbleHolder = document.getElementById('cw-bubble-holder');
          const widgetHolder = document.getElementById('cw-widget-holder');
          const iframe = document.getElementById('chatwoot_live_chat_widget');
          const wootElements = document.querySelectorAll('[class*="woot"]');
          const chatwootElements = document.querySelectorAll('[id*="chatwoot"]');

          console.log('Bubble holder:', bubbleHolder);
          console.log('Widget holder:', widgetHolder);
          console.log('Chat iframe:', iframe);
          console.log('All woot elements:', wootElements);
          console.log('All chatwoot elements:', chatwootElements);
          console.log('window.$chatwoot:', window.$chatwoot);

          // 检查元素的样式
          if (bubbleHolder) {
            console.log('Bubble holder styles:', window.getComputedStyle(bubbleHolder));
            console.log('Bubble holder innerHTML:', bubbleHolder.innerHTML);
          }

          // 检查是否有气泡按钮
          const bubbleButton = document.querySelector('.woot-widget-bubble');
          console.log('Bubble button:', bubbleButton);
          if (bubbleButton) {
            console.log('Bubble button styles:', window.getComputedStyle(bubbleButton));
            console.log('Bubble button classes:', bubbleButton.className);
          }
        }, 2000);
      }
      g.onerror=function(){
        console.error('Failed to load Chatwoot SDK from:', BASE_URL + '/packs/js/sdk.js');
        console.error('Please check:');
        console.error('1. Is Chatwoot server running at', BASE_URL);
        console.error('2. Is the websiteToken correct: KNGYN9tZffVKpCVV6BCAT86e');
        console.error('3. Check network connectivity');
      }
    })(document,"script");

    // 清理函数
    return () => {
      // 如果需要在组件卸载时清理Chatwoot，可以在这里添加清理逻辑
      // 注意：通常不需要移除Chatwoot，因为它应该在整个应用中保持可用
    };
  }, []);

  // 这个组件不渲染任何可见内容
  return null;
};

export default ChatwootWidget;
