import { DatePicker } from 'antd';
import '../AnimPorts/index.less';

export default (props) => {


    return (
        <div className="anim-port">
            {props.beRange ? <>
                <DatePicker.RangePicker {...props} allowClear style={{ width: '100%', border: 'none' }} placeholder='' />
                <label className={(props.labelAlgin == 'right') ? 'right' : 'left'}>{props.label}</label>
            </> : <>
                <DatePicker {...props} allowClear style={{ width: '100%', border: 'none' }} placeholder='' />
                <label className={(props.labelAlgin == 'right') ? 'right' : 'left'}>{props.label}</label>
            </>}
        </div>
    );
}
