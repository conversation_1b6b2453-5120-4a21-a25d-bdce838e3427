import React, { useState, useEffect, useCallback } from 'react';
import { Row, Col, Typography, Form, Button, Input, InputNumber, Card, Divider, Space, Table, message, Select, Spin, Tooltip, Switch } from 'antd';
import { ReloadOutlined, InfoCircleOutlined, SwapOutlined } from '@ant-design/icons';
import { GridContent } from '@ant-design/pro-layout';
import { formatMoney } from '@/common/utils';
import Texty from 'rc-texty';
import { getExchangeRate, CURRENCIES } from './service';
import './index.less';

const { Title, Text } = Typography;
const { Option } = Select;

// 智能格式化数字，去掉无意义的小数位
const smartFormatNumber = (num) => {
  if (!num || num === 0) return '0';

  // 如果是整数，直接返回
  if (num % 1 === 0) return num.toString();

  // 保留有效小数位，最多6位
  let formatted = parseFloat(num.toFixed(6)).toString();

  // 去掉末尾的0
  formatted = formatted.replace(/\.?0+$/, '');

  return formatted;
};

export default () => {
  const [form] = Form.useForm();
  const [chargeItems, setChargeItems] = useState([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [tax1Amount, setTax1Amount] = useState(0);
  const [tax2Amount, setTax2Amount] = useState(0);
  const [finalTotal, setFinalTotal] = useState(0);
  const [calculatedResults, setCalculatedResults] = useState([]); // 存储计算后的结果
  const [exchangeRateLoading, setExchangeRateLoading] = useState({}); // 汇率加载状态

  // 单位选项
  const unitOptions = [
    { value: 'RT', label: 'RT' },
    { value: '方', label: '方' },
    { value: '重量', label: '重量' },
    { value: 'BL', label: 'BL' }
  ];

  // 计算单项费用
  const calculateCharge = (rate, unit, min, tons, containers, exchangeRate = 1) => {
    if (!rate || rate <= 0) return 0;

    let baseAmount = 0;

    switch (unit) {
      case 'RT':
        // RT取重量和方中的较大值
        const rt = Math.max(tons || 0, containers || 0);
        baseAmount = rate * rt;
        break;
      case 'BL':
        // BL固定为1
        baseAmount = rate * 1;
        break;
      case '方':
        baseAmount = rate * (containers || 0);
        break;
      case '重量':
        baseAmount = rate * (tons || 0);
        break;
      default:
        baseAmount = rate;
    }

    // 应用最小值限制
    if (min > 0) {
      baseAmount = Math.max(baseAmount, min);
    }

    // 应用汇率
    return baseAmount * exchangeRate;
  };

  // 添加费用项
  const addChargeItem = () => {
    const newItem = {
      id: Date.now(),
      rate: null,
      unit: 'RT',
      min: null,
      enableExchangeRate: false, // 是否启用汇率转换
      fromCurrency: 'USD', // 基础币种
      toCurrency: 'CNY',   // 目标币种
      exchangeRate: 1,     // 汇率值，默认为1
      exchangeRateInfo: null, // 汇率信息
      amount: 0
    };
    setChargeItems([...chargeItems, newItem]);
  };

  // 删除费用项
  const removeChargeItem = (id) => {
    setChargeItems(chargeItems.filter(item => item.id !== id));
  };

  // 获取汇率
  const fetchExchangeRate = async (itemId, fromCurrency, toCurrency) => {
    if (!fromCurrency || !toCurrency || fromCurrency === toCurrency) {
      // 相同货币或缺少参数时，设置汇率为1
      onChargeItemChange(itemId, 'exchangeRate', 1);
      onChargeItemChange(itemId, 'exchangeRateInfo', {
        rate: 1,
        lastUpdate: new Date().toISOString(),
        cached: false
      });
      return;
    }

    setExchangeRateLoading(prev => ({ ...prev, [itemId]: true }));

    try {
      const rateInfo = await getExchangeRate(fromCurrency, toCurrency);
      onChargeItemChange(itemId, 'exchangeRate', rateInfo.rate);
      onChargeItemChange(itemId, 'exchangeRateInfo', rateInfo);

      if (rateInfo.error) {
        message.warning(`${rateInfo.error}`);
      } else if (rateInfo.cached) {
        message.info('使用缓存汇率数据');
      } else {
        message.success('汇率更新成功');
      }
    } catch (error) {
      message.error(`获取汇率失败: ${error.message}`);
      console.error('获取汇率失败:', error);
    } finally {
      setExchangeRateLoading(prev => ({ ...prev, [itemId]: false }));
    }
  };



  // 手动计算
  const handleCalculate = () => {
    const formValues = form.getFieldsValue();
    const { tons, containers, customCharge, tax1Rate, tax2Rate } = formValues;
    let subtotal = 0;

    // 计算用户输入的费用项
    const calculatedItems = chargeItems.map(item => {
      const amount = calculateCharge(
        item.rate,
        item.unit,
        item.min,
        tons,
        containers,
        item.exchangeRate || 1
      );
      subtotal += amount;
      return { ...item, amount };
    });

    // 更新chargeItems状态
    setChargeItems(calculatedItems);

    // 添加自定义费用
    if (customCharge && parseFloat(customCharge) > 0) {
      subtotal += parseFloat(customCharge);
    }

    setTotalAmount(subtotal);

    // 计算税费
    const tax1 = tax1Rate ? subtotal * (tax1Rate / 100) : 0;
    setTax1Amount(tax1);

    const tax2 = tax2Rate ? (subtotal + tax1) * (tax2Rate / 100) : 0;
    setTax2Amount(tax2);

    setFinalTotal(subtotal + tax1 + tax2);

    // 生成计算结果数据
    const results = [
      ...calculatedItems.filter(item => item.amount > 0).map((item, index) => {
        const displayRate = smartFormatNumber(item.rate || 0);
        const displayExchangeRate = item.exchangeRate || 1;
        const minText = item.min ? ` MIN ${smartFormatNumber(item.min)}` : '';

        // 汇率显示文本 - 只有启用汇率转换且汇率不为1时才显示
        let exchangeText = '';
        if (item.enableExchangeRate && displayExchangeRate !== 1 && item.fromCurrency && item.toCurrency) {
          exchangeText = ` × ${smartFormatNumber(displayExchangeRate)} (${item.fromCurrency}→${item.toCurrency})`;
        }

        return {
          key: item.id,
          name: `费用项 ${index + 1}`,
          calculation: `${displayRate} ${item.unit}${minText}${exchangeText}`,
          amount: smartFormatNumber(item.amount)
        };
      }),
      ...(formValues.customCharge && parseFloat(formValues.customCharge) > 0 ? [{
        key: 'customCharge',
        name: '自定义费用',
        calculation: '自定义',
        amount: smartFormatNumber(parseFloat(formValues.customCharge))
      }] : [])
    ];

    setCalculatedResults(results);
  };

  // 清空所有数据
  const handleClear = () => {
    form.resetFields();
    setChargeItems([{
      id: Date.now(),
      rate: null,
      unit: 'RT',
      min: null,
      enableExchangeRate: false,
      fromCurrency: 'USD',
      toCurrency: 'CNY',
      exchangeRate: 1,
      exchangeRateInfo: null,
      amount: 0
    }]);
    setTotalAmount(0);
    setTax1Amount(0);
    setTax2Amount(0);
    setFinalTotal(0);
    setCalculatedResults([]); // 清空计算结果
    setExchangeRateLoading({}); // 清空加载状态
  };

  // 费用项输入变化（不自动计算）
  const onChargeItemChange = useCallback((id, field, value) => {
    setChargeItems(prevItems =>
      prevItems.map(item =>
        item.id === id ? { ...item, [field]: value } : item
      )
    );

    // 当启用汇率转换开关打开时，自动获取汇率
    if (field === 'enableExchangeRate' && value === true) {
      const item = chargeItems.find(item => item.id === id);
      if (item && item.fromCurrency && item.toCurrency) {
        setTimeout(() => {
          fetchExchangeRate(id, item.fromCurrency, item.toCurrency);
        }, 100);
      }
    }

    // 当货币选择改变时，如果已启用汇率转换，则自动获取汇率
    if ((field === 'fromCurrency' || field === 'toCurrency')) {
      const item = chargeItems.find(item => item.id === id);
      if (item && item.enableExchangeRate) {
        const fromCurrency = field === 'fromCurrency' ? value : item.fromCurrency;
        const toCurrency = field === 'toCurrency' ? value : item.toCurrency;

        if (fromCurrency && toCurrency) {
          setTimeout(() => {
            fetchExchangeRate(id, fromCurrency, toCurrency);
          }, 100);
        }
      }
    }

    // 当关闭汇率转换时，重置汇率为1
    if (field === 'enableExchangeRate' && value === false) {
      setTimeout(() => {
        onChargeItemChange(id, 'exchangeRate', 1);
        onChargeItemChange(id, 'exchangeRateInfo', null);
      }, 50);
    }
  }, [chargeItems]);

  // 初始化表单默认值
  useEffect(() => {
    // 添加一个默认的费用项
    addChargeItem();
  }, []);

  // 使用计算后的结果数据
  const resultTableData = calculatedResults;

  const resultColumns = [
    {
      title: '费用项目',
      dataIndex: 'name',
      key: 'name',
      width: '40%'
    },
    {
      title: '计算方式',
      dataIndex: 'calculation',
      key: 'calculation',
      width: '35%'
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: '25%',
      align: 'right'
    }
  ];

  return (
    <div className="za-content port-charge-calculator">
      <div className="calculator-content">
        <div className="calculator-slogan">
          <div className="slogan-title">
            <Texty delay={1000}>目的港收费计算器</Texty>
          </div>
          <div className="slogan-sub-title">
            <Texty delay={2000} type="flash">精确计算各项港口费用，支持多种计费单位和汇率转换。</Texty>
          </div>
        </div>

        <div className="calculator-container">
          <Row gutter={24}>
          <Col lg={24} xl={13}>
            <Card
              title="费用计算参数"
              className="paramCard"
            >
              <Form
                form={form}
                layout="vertical"
                className="calculatorForm"
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="重量" name="tons">
                      <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入重量" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="方" name="containers">
                      <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入方" />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider style={{ margin: '16px 0 12px 0' }} />
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                  <Title level={5} style={{ margin: 0 }}>费用项目</Title>
                  <Space>
                    <Button size="small" onClick={addChargeItem}>
                      添加费用项
                    </Button>
                  </Space>
                </div>

                <div className="chargeItemsContainer">
                  {chargeItems.map((item, index) => (
                    <div key={item.id} className="chargeItem">
                      <div className="chargeItemHeader">
                        <Text strong>费用项 {index + 1}</Text>
                        <Button
                          type="text"
                          danger
                          size="small"
                          onClick={() => removeChargeItem(item.id)}
                          disabled={chargeItems.length === 1}
                        >
                          删除
                        </Button>
                      </div>
                      <Row gutter={8}>
                        <Col span={7}>
                          <InputNumber
                            placeholder="费用金额"
                            min={0}
                            style={{ width: '100%' }}
                            value={item.rate}
                            onChange={(value) => onChargeItemChange(item.id, 'rate', value)}
                          />
                        </Col>
                        <Col span={5}>
                          <Select
                            style={{ width: '100%' }}
                            value={item.unit}
                            onChange={(value) => onChargeItemChange(item.id, 'unit', value)}
                          >
                            {unitOptions.map(option => (
                              <Option key={option.value} value={option.value}>
                                {option.label}
                              </Option>
                            ))}
                          </Select>
                        </Col>
                        <Col span={6}>
                          <InputNumber
                            placeholder="MIN值"
                            min={0}
                            style={{ width: '100%' }}
                            value={item.min}
                            onChange={(value) => onChargeItemChange(item.id, 'min', value)}
                          />
                        </Col>
                        <Col span={6}>
                          <Space style={{ width: '100%' }}>
                            <Switch
                              size="small"
                              checked={item.enableExchangeRate}
                              onChange={(checked) => onChargeItemChange(item.id, 'enableExchangeRate', checked)}
                            />
                            <span style={{ fontSize: '12px', color: '#666' }}>汇率转换</span>
                          </Space>
                        </Col>
                      </Row>

                      {/* 汇率选择区域 - 只有启用时才显示 */}
                      {item.enableExchangeRate && (
                        <Row gutter={8} style={{ marginTop: 8 }}>
                          <Col span={8}>
                            <Select
                              placeholder="基础币种"
                              style={{ width: '100%' }}
                              value={item.fromCurrency}
                              onChange={(value) => onChargeItemChange(item.id, 'fromCurrency', value)}
                              showSearch
                              filterOption={(input, option) =>
                                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                              }
                            >
                              {CURRENCIES.map(currency => (
                                <Option key={currency.value} value={currency.value}>
                                  {currency.label}
                                </Option>
                              ))}
                            </Select>
                          </Col>
                          <Col span={2} style={{ textAlign: 'center', lineHeight: '32px' }}>
                            <SwapOutlined />
                          </Col>
                          <Col span={8}>
                            <Select
                              placeholder="目标币种"
                              style={{ width: '100%' }}
                              value={item.toCurrency}
                              onChange={(value) => onChargeItemChange(item.id, 'toCurrency', value)}
                              showSearch
                              filterOption={(input, option) =>
                                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                              }
                            >
                              {CURRENCIES.map(currency => (
                                <Option key={currency.value} value={currency.value}>
                                  {currency.label}
                                </Option>
                              ))}
                            </Select>
                          </Col>
                          <Col span={6}>
                            <Button
                              style={{ width: '100%' }}
                              icon={<ReloadOutlined />}
                              loading={exchangeRateLoading[item.id]}
                              onClick={() => fetchExchangeRate(item.id, item.fromCurrency, item.toCurrency)}
                              disabled={!item.fromCurrency || !item.toCurrency}
                            >
                              获取汇率
                            </Button>
                          </Col>
                        </Row>
                      )}

                      {/* 汇率信息显示 - 只有启用汇率转换时才显示 */}
                      {item.enableExchangeRate && item.exchangeRateInfo && (
                        <div style={{ marginTop: 8, fontSize: '12px', color: '#666', paddingLeft: 8, borderLeft: '2px solid #1890ff' }}>
                          <Space>
                            <span>
                              汇率: 1 {item.fromCurrency} = {smartFormatNumber(item.exchangeRateInfo.rate)} {item.toCurrency}
                            </span>
                            {item.exchangeRateInfo.lastUpdate && (
                              <Tooltip title={`更新时间: ${new Date(item.exchangeRateInfo.lastUpdate).toLocaleString()}`}>
                                <InfoCircleOutlined />
                              </Tooltip>
                            )}
                            {item.exchangeRateInfo.cached && (
                              <span style={{ color: '#faad14' }}>(缓存)</span>
                            )}
                            {item.exchangeRateInfo.error && (
                              <span style={{ color: '#ff4d4f' }}>({item.exchangeRateInfo.error})</span>
                            )}
                          </Space>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <Divider style={{ margin: '8px 0 4px 0' }} />
                <Title level={5} style={{ marginBottom: 8,marginTop: 8 }}>其他费用</Title>
                
                <Form.Item label="自定义费用" name="customCharge">
                  <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入自定义费用" />
                </Form.Item>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="税费1 (%)" name="tax1Rate">
                      <InputNumber min={0} max={100} style={{ width: '100%' }} placeholder="按合计收税" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="税费2 (%)" name="tax2Rate">
                      <InputNumber min={0} max={100} style={{ width: '100%' }} placeholder="按合计+税费1收税" />
                    </Form.Item>
                  </Col>
                </Row>

                <div style={{ marginTop: 24, paddingTop: 16, borderTop: '1px solid #f0f0f0' }}>
                  <div style={{ textAlign: 'center' }}>
                    <Space size="middle">
                      <Button type="primary" size="large" onClick={handleCalculate}>
                        计算费用
                      </Button>
                      <Button size="large" onClick={handleClear}>
                        清空重置
                      </Button>
                    </Space>
                  </div>
                </div>
              </Form>
            </Card>
          </Col>

          <Col lg={24} xl={11}>
            <Card
              title="计算结果"
              className="resultCard"
            >
              <div>
                <Table
                  columns={resultColumns}
                  dataSource={resultTableData}
                  pagination={false}
                  size="small"
                  className="resultTable"
                />
              </div>

              <Divider style={{ margin: '16px 0' }} />

              <div className="summarySection">
              <Row justify="space-between" className="summaryRow">
                <Col><Text strong>小计:</Text></Col>
                <Col><Text strong>{smartFormatNumber(totalAmount)}</Text></Col>
              </Row>

              {tax1Amount > 0 && (
                <Row justify="space-between" className="summaryRow">
                  <Col><Text>税费1 ({smartFormatNumber(form.getFieldValue('tax1Rate'))}%):</Text></Col>
                  <Col><Text>{smartFormatNumber(tax1Amount)}</Text></Col>
                </Row>
              )}

              {tax2Amount > 0 && (
                <Row justify="space-between" className="summaryRow">
                  <Col><Text>税费2 ({smartFormatNumber(form.getFieldValue('tax2Rate'))}%):</Text></Col>
                  <Col><Text>{smartFormatNumber(tax2Amount)}</Text></Col>
                </Row>
              )}

              <Divider />

              <Row justify="space-between" className="totalRow">
                <Col><Title level={4}>总计:</Title></Col>
                <Col><Title level={4} type="danger">{smartFormatNumber(finalTotal)}</Title></Col>
              </Row>
              </div>
            </Card>
          </Col>
          </Row>
        </div>
      </div>
    </div>
  );
};
