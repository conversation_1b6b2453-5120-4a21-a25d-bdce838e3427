.rateHistory-search {
    margin: 24px 0;
    border-bottom: 1px solid #f0f0f0;
}

.rateHistory-list {
    padding: 0 24px;

    .rateHistory-tools {
        display: flex;
        justify-content: space-between;

        .rateHistory-tools-btn {
            width: 200px;
            display: flex;
            justify-content: end;
        }
    }

    .rateHistory-radio {
        padding: 0 10px;
    }

    .rateHistory-expand {
        padding: 0 30px;

        p {
            margin: 0;
            line-height: 32px;

            span {
                color: #999;
                margin-left: 30px;
            }
        }
    }

    .rateHistory-table {
        .ant-table-thead {
            position: sticky;
            top: 56px;
            z-index: 99;
        }

    }

    .card-header {
        // width: 100%;
        background: #FAFAFA;
        border: 1px solid #E8E8E8;
        margin-top: 16px;
        border-radius: 6px 6px 0 0;
        display: flex;
        justify-content: start;
        position: sticky;
        top: 56px;
        z-index: 99;

        .header-item {
            font-family: 'Arial';
            font-weight: 600;
            color: rgba(0, 0, 0, 0.88);
            line-height: 35px;
            margin: 10px 0;
            padding: 0 10px 0 9px;
            border-left: 1px solid #f0f0f0;
            text-align: center;
        }

    }

    .card-list {
        // width: 100%;
        border: 1px solid #E8E8E8;
        border-radius: 6px;
        margin-top: 20px;

        .card-item-top {
            display: flex;
            justify-content: start;
            align-items: center;
            padding: 16px 0;

            p {
                margin: 0;
                line-height: 24px;
            }

            .card-item-cus {
                width: 200px;
                margin-left: 20px;
            }

            .card-item-logo {
                width: 45px;
                height: 45px;
                border-radius: 4px;
                border: 1px solid #E8E8E8;
                overflow: hidden;

                img {
                    width: 100%;
                }
            }

            .card-item-ports {
                width: 240px;
                padding: 0 16px;

            }

            .card-item-price {
                width: 110px;
                text-align: center;
                font-weight: bold;
            }

            .card-item-option {
                width: 120px;
                text-align: center;
                padding: 6px 0;
            }
        }

        .card-item-bottom {
            border-top: 1px solid #E8E8E8;
            background: #fafafa;
            overflow: hidden;
            position: relative;

            p {
                margin: 12px 0;
                line-height: 20px;
                color: #999;

                span {
                    color: #AAA;
                    margin-left: 30px;
                }
            }

            .card-expand-btn {
                position: absolute;
                right: 20px;
                bottom: 8px;
                color: #999;
            }

        }
    }

    .card-list:hover {
        background: #fffbfb;

        .card-item-bottom {
            background: transparent;
        }
    }
}







.rateHistory-pagination {
    display: flex;
    justify-content: flex-end;
    margin: 24px;
}