{"private": true, "author": "<PERSON> <<EMAIL>>", "scripts": {"start": "max dev", "build": "max build", "start:test": "cross-env REACT_ENV=test max dev"}, "dependencies": {"@ant-design/plots": "^2.3.2", "@ant-design/x": "^1.0.3", "@microsoft/fetch-event-source": "^2.0.1", "@umijs/max": "^4.3.17", "ag-grid-community": "28.0.0", "ag-grid-react": "28.0.0", "antd": "^5.21.0", "antd-style": "^3.7.1", "axios": "^1.7.7", "immer": "^10.1.1", "js-md5": "^0.8.3", "lodash": "^4.17.21", "lscache": "^1.3.2", "markdown-it": "^14.1.0", "moment": "^2.30.1", "object-assign": "^4.1.1", "pubsub-js": "^1.9.5", "ramda": "^0.30.1", "rc-queue-anim": "^2.0.0", "rc-scroll-anim": "^2.7.6", "rc-texty": "^0.2.0", "react-draggable": "^4.4.3", "react-helmet": "^6.1.0", "react-photo-view": "^0.5.7", "rxjs": "^7.8.1", "tablemark": "^3.1.0", "umi-request": "^1.4.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^4.1.2"}}