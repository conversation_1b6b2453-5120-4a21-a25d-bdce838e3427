import './index.less';
import React, { useState, useEffect } from 'react';
import { api, } from '@/common/utils';

export default (props) => {
    const apiUrl = 'https://yxb-trace-position.yxb56.com/shipTrack';
    const domain = 'vip.dsi-log.cn';
    const vessel = props.vessel;
    const [token, setToken] = useState('');

    useEffect(() => {
        api.base.getYidaToken().subscribe({
            next: (data) => {
                setToken(data.accessToken);
            },
        });
    }, []);

    return (<>
        {
            token &&

            <iframe className="positionFrame" src={apiUrl + "?searchValue=" + vessel + "&token=" + token + "&domain=" + domain} />
        }
    </>


    );
}
