// 订单
.book {
    background: url('@/assets/booking-bg.png');
    background-repeat: no-repeat;
    background-position: top center;

    .book-content {
        width: 1200px;
        margin: 0 auto;

        .book-slogan {
            height: 190px;
            overflow: hidden;

            .slogan-title {
                font-weight: bold;
                font-size: 38px;
                color: #482828;
                line-height: 53px;
                margin-top: 50px;
            }

            .slogan-sub-title {
                font-weight: 400;
                font-size: 20px;
                color: #482828;
                line-height: 28px;
                margin-top: 14px;
            }
        }

        .book-search {
            width: 1160px;
            min-height: 100px;
            margin: 24px auto;
            background: #fff;
            border-radius: 10px 10px;
            box-shadow: 0px 0px 20px #ddd;
            padding: 20px;

            .search-default {
                display: flex;
                margin-bottom: 24px;

                .search-ports {
                    width: 300px;
                    height: 60px;
                    border: 1px solid #D9D9D9;
                    border-radius: 4px;
                    display: flex;
                    margin: '0 0 0 15px';

                    .search-pol,
                    .search-pod {
                        width: 100%;
                        margin: 0 0 0 15px
                    }

                    .ports-btn {
                        width: 60px;

                        img {
                            width: 36px;
                            height: 36px;
                            margin: 12px;
                            border-radius: 50%;
                            box-shadow: 0 0 5px #ccc;
                        }
                    }
                }

                .search-etd {
                    width: 300px;
                    height: 60px;
                    border: 1px solid #D9D9D9;
                    border-radius: 4px;
                    margin: 0 15px;
                }

                .search-btn {
                    width: 230px;
                    height: 60px;
                    background: rgba(180, 20, 27, 1);
                    color: #fff;
                    border-radius: 4px;
                    font-size: 22px;
                    line-height: 60px;
                    text-align: center;
                    user-select: none;
                }

                .search-btn:hover {
                    cursor: pointer;
                    background: rgba(180, 20, 27, 0.9);
                }
            }

            .search-expand {
                margin: 0;
                padding: 0;
            }

            .search-tool {
                display: flex;
                justify-content: flex-end;
                margin: 0;

                .tool-btn {
                    width: 150px;
                    display: flex;
                    justify-content: end;

                }

            }
        }

        .book-list {
            min-height: 500px;
            .book-tools {
                display: flex;
                justify-content: space-between;

                .book-tools-btn {
                    width: 200px;
                    display: flex;
                    justify-content: end;
                }
            }

            .book-expand {
                padding: 0 80px;

                p {
                    margin: 0;
                    line-height: 32px;

                    span {
                        color: #999;
                        margin-left: 30px;
                    }
                }
            }

            .book-table {
                .ant-table-thead {
                    position: sticky;
                    top: 56px;
                    z-index: 99;
                }

            }

            .card-header {
                width: 100%;
                background: #FAFAFA;
                border: 1px solid #E8E8E8;
                margin-top: 16px;
                border-radius: 6px 6px 0 0;
                display: flex;
                justify-content: start;
                position: sticky;
                top: 56px;
                z-index: 99;

                .header-item {
                    font-family: 'Arial';
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.88);
                    line-height: 35px;
                    margin: 10px 0;
                    padding: 0 10px 0 9px;
                    border-left: 1px solid #f0f0f0;
                    text-align: center;
                }

            }

            .card-list {
                width: 100%;
                border: 1px solid #E8E8E8;
                border-radius: 6px;
                margin-top: 20px;

                .card-item-top {
                    display: flex;
                    justify-content: start;
                    padding: 16px 0;

                    p {
                        margin: 0;
                        line-height: 24px;
                    }

                    .card-item-checkbox {
                        width: 16px;
                        margin: 12px 10px;

                    }

                    .card-item-logo {
                        width: 45px;
                        height: 45px;
                        border-radius: 4px;
                        border: 1px solid #E8E8E8;
                        overflow: hidden;

                        img {
                            width: 100%;
                        }
                    }

                    .card-item-ports {
                        width: 248px;
                        padding: 0 16px;

                    }

                    .card-item-transfer {
                        width: 160px;
                        text-align: center;
                    }
                    .card-item-price {
                        width: 140px;
                        text-align: center;
                        font-weight: bold;
                    }
                    .card-item-book {
                        width: 120px;
                        color: #333;
                        font-size: 16px;
                        font-weight: bold;
                        line-height: 45px;
                        text-align: center;
                    }

                    .card-item-option {
                        width: 120px;
                        text-align: center;
                        padding: 6px 0;
                    }
                }

                .card-item-bottom {
                    border-top: 1px solid #E8E8E8;
                    background: #fafafa;
                    overflow: hidden;
                    position: relative;

                    p {
                        margin: 12px 0;
                        line-height: 20px;
                        color: #999;

                        span {
                            color: #AAA;
                            margin-left: 30px;
                        }
                    }

                    .card-expand-btn {
                        position: absolute;
                        right: 20px;
                        bottom: 8px;
                        color: #999;
                    }

                }
            }

            .card-list:hover {
                background: #fffbfb;

                .card-item-bottom {
                    background: transparent;
                }
            }
        }

        .book-pagination {
            display: flex;
            justify-content: flex-end;
            margin: 20px 0 0 0;
        }

        .book-radio {
            padding: 0 10px;
        }

        .book-cargo {
            color: #999999;
        }
    }
}