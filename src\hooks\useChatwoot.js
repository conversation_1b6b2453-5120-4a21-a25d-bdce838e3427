import { useCallback } from 'react';
import { setChatwootUser, resetChatwoot, updateChatwootCustomAttributes } from '@/common/chatwootUtils';

/**
 * Chatwoot操作Hook
 */
export const useChatwoot = () => {
  
  // 更新用户信息
  const updateUser = useCallback((user = null) => {
    setChatwootUser(user);
  }, []);

  // 重置Chatwoot
  const reset = useCallback(() => {
    resetChatwoot();
  }, []);

  // 更新自定义属性
  const updateCustomAttributes = useCallback((attributes) => {
    updateChatwootCustomAttributes(attributes);
  }, []);

  // 打开聊天窗口
  const openChat = useCallback(() => {
    if (window.chatwootSDK && window.$chatwoot) {
      window.$chatwoot.toggle('open');
    }
  }, []);

  // 关闭聊天窗口
  const closeChat = useCallback(() => {
    if (window.chatwootSDK && window.$chatwoot) {
      window.$chatwoot.toggle('close');
    }
  }, []);

  // 设置用户语言
  const setLocale = useCallback((locale) => {
    if (window.chatwootSDK) {
      window.chatwootSDK.setLocale(locale);
    }
  }, []);

  return {
    updateUser,
    reset,
    updateCustomAttributes,
    openChat,
    closeChat,
    setLocale
  };
};
