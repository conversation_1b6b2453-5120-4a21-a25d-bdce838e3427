.resourceContent {
    margin: 20px;


    
        .basic-info-icon{
            margin-top: 2px;
        }
    
        .basic-info-title {
            width: 105px;
            display: block;
            text-align: justify;
            height: 24px;
            margin: 0 0 0 10px;
        }
    
        .basic-info-title:after {
            content: '';
            display: inline-block;
            width: 100%;
            height: 0;
        }
    

    .resource-title {
        margin: 20px;
        color: rgba(0, 0, 0, 0.88);
        font-weight: 600;
        font-size: 16px;
        line-height: 40px;
    }

    .certStatus {
        position: absolute;
        width: 40px;
        height: 40px;
        right: -15px;
        top: -15px;
        z-index: 1;
    }

    .status01 {
        background: url("../../../assets/status01.png") no-repeat;
    }

    .status02 {
        background: url("../../../assets/status02.png") no-repeat;
    }

    .status03 {
        background: url("../../../assets/status03.png") no-repeat;
    }

    .certStatusText {
        display: flex;
        position: absolute;
        left: 15px;
        top: 5px;
        width: 190px;
        height: 290px;
        border-radius: 2px;
        background: rgba(0, 0, 0, 0.45);
        backdrop-filter: blur(4px);
        color: #fff;
        justify-content: center;
        align-items: center;
    }

}