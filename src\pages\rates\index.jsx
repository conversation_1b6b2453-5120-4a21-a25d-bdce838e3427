import { useEffect, useState } from 'react';
import { Splitter, Form, Row, Col, Input, Button, Space } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import QueueAnim from 'rc-queue-anim';
import './index.less';

export default () => {

  const [leftSpan, setLeftSpan] = useState([24, 12, 12]);
  const [rightSpan, setRightSpan] = useState([24, 12, 12]);
  const [leftExpand, setLeftExpand] = useState(false);
  const [rightExpand, setRightExpand] = useState(false);

  const rateResize = (size) => {
    // 左侧宽度
    if (size[0] < 760) { setLeftSpan([24, 12, 12]) }
    if (size[0] >= 760 && size[0] < 1000) { setLeftSpan([16, 8, 0]) }
    if (size[0] >= 1000) { setLeftSpan([12, 6, 6]) }
    // 右侧宽度
    if (size[1] < 760) { setRightSpan([24, 12, 12]) }
    if (size[1] >= 760 && size[1] < 1000) { setRightSpan([16, 8, 0]) }
    if (size[1] >= 1000) { setRightSpan([12, 6, 6]) }
  }

  return (<Splitter className='rate-splitter' onResize={rateResize}>
    <Splitter.Panel style={{ overflow: 'auto' }} collapsible>
      <div className='all-rates'>
        <div className='rates-title'> 线下运价 </div>

        <div className="search-form">
          <Form
            name="leftSearch"
            onFinish={{}}
          >
            <Row gutter={[10, 16]}>
              <Col span={leftSpan[0]}><div className="item">POL - POD</div></Col>
              <Col span={leftSpan[1]}><div className="item">箱型</div></Col>
              <Col span={leftSpan[2]}><div className="item">船公司</div></Col>
            </Row>

            <QueueAnim>
              {leftExpand ? [
                <Row key="a" gutter={[10, 16]} style={{ marginTop: '16px' }}>
                  <Col span={leftSpan[1]}>
                    <Form.Item name="line" label="航线" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择航线" />
                    </Form.Item>
                  </Col>
                  <Col span={leftSpan[1]}>
                    <Form.Item label="航线代码" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择航线代码" />
                    </Form.Item>
                  </Col>
                  <Col span={leftSpan[1]}>
                    <Form.Item label="班期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择班期" />
                    </Form.Item>
                  </Col>
                  <Col span={leftSpan[1]}>
                    <Form.Item label="卸货港" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择卸货港" />
                    </Form.Item>
                  </Col>
                  <Col span={leftSpan[1]}>
                    <Form.Item label="开航日期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择开航日期" />
                    </Form.Item>
                  </Col>
                  <Col span={leftSpan[1]}>
                    <Form.Item label="生效日期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择生效日期" />
                    </Form.Item>
                  </Col>
                  <Col span={leftSpan[1]}>
                    <Form.Item label="失效日期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择失效日期" />
                    </Form.Item>
                  </Col>
                  <Col span={leftSpan[1]}>
                    <Form.Item label="更新日期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择更新日期" />
                    </Form.Item>
                  </Col>
                </Row>
              ] : null}
            </QueueAnim>

            <Row style={{ marginTop: '16px' }}>
              <Col span={14}>
                <div className="rate-history">
                  <span>QINGDAO,CN - LOS ANGELES,CA</span>
                  <span>清空记录</span>
                </div>
              </Col>
              <Col span={10} style={{ textAlign: 'right' }}>
                <Space>
                  <Button type="primary" htmlType="submit" >
                    查询
                  </Button>
                  <Button type="default" htmlType="reset" >
                    重置
                  </Button>
                  {!leftExpand ?
                    <Button type="text" icon={<DownOutlined />} onClick={() => setLeftExpand(true)}>展开</Button>
                    :
                    <Button type="text" icon={<UpOutlined />} onClick={() => setLeftExpand(false)}>收起</Button>
                  }
                </Space>
              </Col>
            </Row>
          </Form>
        </div>

      </div>
    </Splitter.Panel>
    <Splitter.Panel style={{ overflow: 'auto' }} collapsible>
      <div className='all-rates'>
        <div className='rates-title'> 线上运价 </div>

        <div className="search-form">
          <Form
            name="rightSearch"
            onFinish={{}}
          >
            <Row gutter={[10, 16]}>
              <Col span={rightSpan[0]}><div className="item">POL - POD</div></Col>
              <Col span={rightSpan[1]}><div className="item">箱型</div></Col>
              <Col span={rightSpan[2]}><div className="item">船公司</div></Col>
            </Row>

            <QueueAnim>
              {rightExpand ? [
                <Row key="a" gutter={[10, 16]} style={{ marginTop: '16px' }}>
                  <Col span={rightSpan[1]}>
                    <Form.Item name="line" label="航线" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择航线" />
                    </Form.Item>
                  </Col>
                  <Col span={rightSpan[1]}>
                    <Form.Item label="航线代码" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择航线代码" />
                    </Form.Item>
                  </Col>
                  <Col span={rightSpan[1]}>
                    <Form.Item label="班期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择班期" />
                    </Form.Item>
                  </Col>
                  <Col span={rightSpan[1]}>
                    <Form.Item label="卸货港" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择卸货港" />
                    </Form.Item>
                  </Col>
                  <Col span={rightSpan[1]}>
                    <Form.Item label="开航日期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择开航日期" />
                    </Form.Item>
                  </Col>
                  <Col span={rightSpan[1]}>
                    <Form.Item label="生效日期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择生效日期" />
                    </Form.Item>
                  </Col>
                  <Col span={rightSpan[1]}>
                    <Form.Item label="失效日期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择失效日期" />
                    </Form.Item>
                  </Col>
                  <Col span={rightSpan[1]}>
                    <Form.Item label="更新日期" labelCol={{ flex: '80px' }}>
                      <Input placeholder="请选择更新日期" />
                    </Form.Item>
                  </Col>
                </Row>
              ] : null}
            </QueueAnim>

            <Row style={{ marginTop: '16px' }}>
              <Col span={14}>
                <div className="rate-history">
                  <span>QINGDAO,CN - LOS ANGELES,CA</span>
                  <span>清空记录</span>
                </div>
              </Col>
              <Col span={10} style={{ textAlign: 'right' }}>
                <Space>
                  <Button type="primary" htmlType="submit" >
                    查询
                  </Button>
                  <Button type="default" htmlType="reset" >
                    重置
                  </Button>
                  {!rightExpand ?
                    <Button type="text" icon={<DownOutlined />} onClick={() => setRightExpand(true)}>展开</Button>
                    :
                    <Button type="text" icon={<UpOutlined />} onClick={() => setRightExpand(false)}>收起</Button>
                  }
                </Space>
              </Col>
            </Row>
          </Form>
        </div>

      </div>
    </Splitter.Panel>
  </Splitter>
  )
};
