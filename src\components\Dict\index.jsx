import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
const { Option } = Select;
import { api, handleCopy } from '@/common/utils';


class Dict extends React.Component {
  constructor(props) {
    super(props);
    this.state = { data: [] };
  }

  componentDidMount() {
    api.dict.listChildByParentCode(this.props.dictCode).subscribe({
      next: (data) => this.setState({ data }),
    });
  }

  copyToClipboard = () => {
    handleCopy(this.props.value);
  }

  onChange = (data, option) => {
    this.props.onChange && this.props.onChange(data);
    this.props.getItem && this.props.getItem(option && option['data-item'] || {})
  }

  render() {
    const { data } = this.state;
    return (
      <Row wrap={false}>
        <Col flex="auto">
          <Select
            allowClear
            showSearch
            optionFilterProp="children"
            value={this.props.value}
            onChange={this.onChange}
            placeholder={this.props.placeholder}
            disabled={this.props.disabled}
            style={this.props.style || { width: '100%' }}
            defaultValue={this.props.initialValue}>
            {this.props.isAll &&
              <Option key={"all"} value={"all"}>
                {"全部"}
              </Option>
            }
            {data &&
              data.map((item) => (
                <Option data-item={item} key={item.dictCode} value={item.dictCode}>
                  {item.dictName}
                </Option>
              ))}
          </Select>
        </Col>
        {
          this.props.copyBtn &&
          <Col flex="24px">
            <Button icon={<CopyOutlined />} onClick={this.copyToClipboard} style={{ color: '#1890FF' }} />
          </Col>
        }
      </Row>
    );
  }
}

export default Dict;
