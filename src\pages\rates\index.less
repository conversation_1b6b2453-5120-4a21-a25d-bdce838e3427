// 运价对比
.rate-splitter {
    height: calc(100vh - 155px);
    width: 1200px;
    margin: 20px auto;
    margin-top: 80px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    background: #FFF;

}

.all-rates {
    padding: 15px;
    min-width: 560px;

    .rates-title {
        background: #FAFAFA;
        font-size: 16px;
        text-align: center;
        font-weight: bold;
        line-height: 50px;
        border-radius: 6px 6px 0px 0px;
        margin-bottom: 10px;
    }

    .search-form {

        .ant-form-item {
            margin-bottom: 0;
        }

        .item {
            border: 1px solid #ddd;
            height: 60px;
            border-radius: 5px;
        }

        .rate-history {
            display: flex;

            span {
                font-weight: 400;
                font-size: 12px;
                color: #BFBFBF;
                line-height: 32px;
                margin-right: 16px;
                cursor: pointer;
            }
        }
    }

}