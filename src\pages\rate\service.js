import { constant, ipost, ipostNoFilter, isearch, ispost, idownload } from '@/common/utils';

//线下运价查询
export function listBySearch(params) {
  return ipost(constant.API_FREIGHT_PRICE + '/listByCtm', params);
}
export function searchOffline(params) {
  return isearch(constant.API_FREIGHT_PRICE + '/searchByCtm', params);
}


//线上运价-创建任务
export function createTask(params) {
  return ipostNoFilter(constant.API_FREIGHT_PRICE_ONLINE + '/createTask', params);
}

//线上运价-轮询
export function getByTaskId(params) {
  return ipostNoFilter(constant.API_FREIGHT_PRICE_ONLINE + '/getByTaskId', params);
}
//运价详情
export function getPriceInfoOnline(params) {
  return ispost(constant.API_FREIGHT_PRICE_ONLINE + '/getPriceInfo', params);
}

//运价趋势
export function listPriceTrend(params) {
  return ipost(constant.API_FREIGHT_PRICE + '/listPriceTrend', params);
}

//实时船期
export function listShipSchedules(params) {
  return ipost(constant.API_FREIGHT_PRICE_ONLINE + '/listShipSchedules', params);
}

//报价方案
export function searchPlan(params) {
  return isearch(constant.API_FREIGHT_PRICE_PLAN + '/search', params);
}

//报价方案
export function savePlan(params) {
  return ipost(constant.API_FREIGHT_PRICE_PLAN, params);
}

//运价查询历史
export function searchHistory(params) {
  return isearch(constant.API_FREIGHT_PRICE_SEARCH_HISTORY + '/search', params);
}

//导出
export function exports(params) {
  return idownload(constant.API_FREIGHT_PRICE + '/export', params);
}