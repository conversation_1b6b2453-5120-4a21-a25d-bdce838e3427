import React, { useRef, useEffect, useState } from 'react';
import { Select } from 'antd';
import './index.less';
import { api, } from '@/common/utils';
import debounce from 'lodash/debounce';


export default (props) => {

    const [value, setValue] = useState();
    const delayedQuery = useRef(debounce((value, beOnline) => searchData(value, beOnline), 300)).current;
    const [points, setPoints] = useState([]);


    useEffect(() => {
        searchData('', props.beOnline);
    }, [props.beOnline]);

    const handleChange = (selectItem) => {
        console.log(props,'props-船')
        if (selectItem) {
            const values = selectItem.map((item) => item.value);
            props.onChange(values || []);
        } else {
            props.onChange('');
        }
    };

    const searchData = (keywords, beOnline) => {
        api.base.listShipCompany({ keyword: keywords, beOnline: beOnline }).subscribe({
            next: (body) => {
                let data = [];
                data = body.map((item) => ({
                    value: item.code,
                    label: item.code,
                }));
                setPoints(data);
            },
        });
    };


    return (
        <div className="anim-Carrier">
            <Select
                {...props}
                value={value}
                mode='multiple' maxTagCount={2} allowClear style={{ width: '100%', border: 'none', paddingLeft: '8px' }}
                showSearch
                labelInValue
                filterOption={false}
                onClear={() => { handleChange() }}
                onChange={(selectItem) => { handleChange(selectItem) }}
                onSearch={(v) => {
                    delayedQuery(v, props.beOnline);
                }}
            >
                {points.map((item) => (
                    <Select.Option key={item.value}>{item.label}</Select.Option>
                ))}
            </Select>
            <label>{props.label}</label>
        </div>
    );
}
