import React, { useState } from 'react';
import { Form, Row, Col, message, Button, Pagination, Table, Select, Tag } from 'antd';
import './index.less';
import Menu from '../../components/Menu';
import Port from '@/components/Port';
import { api, dateFormat } from '@/common/utils';

export default () => {
  const [searchForm] = Form.useForm();
  const [datas, setDatas] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const columns = [
    { title: '起运港', dataIndex: 'polName', width: 180, ellipsis: true, },
    { title: '目的港', dataIndex: 'podName', width: 200, ellipsis: true, },
    { title: '船公司', dataIndex: 'shipCompany', width: 120, ellipsis: true, },
    { title: '运价类型', dataIndex: 'rateType', width: 80, render: (text, record) => <Tag color={text === '线下' ? 'success' : 'error'}>{text}</Tag> },
    { title: '查询时间', dataIndex: 'createTime', width: 120, render: (text, record) => <>{dateFormat(record.createTime, 'yyyy-MM-dd hh:mm')}</> },

  ]


  const onSearch = (pageNo, pageSize) => {
    setSearchLoading(true);
    setPageNo(pageNo);
    setPageSize(pageSize);
    let searchData = searchForm.getFieldValue();
    let param = { dto: searchData, pageNo: pageNo, pageSize: pageSize };
    api.freightPrice.searchHistory(param).subscribe({
      next: (data) => {
        setTotal(data.total);
        setDatas(data.data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };


  const onShowSizeChange = (current, pageSize) => {
    onSearch(current, pageSize);
  };

  const onSelectChange = (selectedKeys, selectedRows) => {
    setSelectedRowKeys(selectedKeys);
  };


  return (
    <Menu selectKeys={["searchHistory"]} openKeys={["business"]} >
      <>
        <div className="rateSearchHistory-search">
          <Form form={searchForm}>
            <Row wrap={false} gutter={20}>
              <Col flex={'auto'}>
                <Row>
                  <Col span={8}>
                    <Form.Item name="pol" label="起运港" labelCol={{ flex: '90px' }}>
                      <Port placeholder="请选择" label="起运港" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="pod" label="目的港" labelCol={{ flex: '90px' }}>
                      <Port placeholder="请选择" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="rateType" label="运价类型" labelCol={{ flex: '90px' }}>
                      <Select allowClear>
                        <Option key='全部' value=''>全部</Option>
                        <Option key='线下' value='线下'>线下</Option>
                        <Option key='线上' value='线上'>线上</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Col>

              <Col flex={'180px'}>
                <Row>
                  <Button type="default" htmlType="reset"> 重 置 </Button>
                  <Button type="primary" htmlType="submit" onClick={() => onSearch()} loading={searchLoading} style={{ marginLeft: '10px' }}> 查 询 </Button>
                </Row>
              </Col>

            </Row>
          </Form>
        </div>
        <Table
          className='rateSearchHistory-table'
          rowKey='id'
          pagination={false}
          style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
          rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
          columns={columns}
          dataSource={datas}

        />
        <div className='rateSearchHistory-pagination'>
          <Pagination
            pageSizeOptions={[10, 20, 50]}
            showSizeChanger
            onShowSizeChange={onShowSizeChange}
            onChange={onShowSizeChange}
            // defaultCurrent={p}
            total={total || 0}
            current={pageNo || 0}
            pageSize={pageSize || 10}
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`}
          />

        </div>
      </>
    </Menu>
  );
};
