import { getCache, setCache } from '@/common/utils';

// 免费汇率API配置
const EXCHANGE_API_BASE = 'https://api.exchangerate-api.com/v4/latest';

// 常用货币列表
export const CURRENCIES = [
  { value: 'USD', label: 'USD - 美元', symbol: '$' },
  { value: 'CNY', label: 'CNY - 人民币', symbol: '¥' },
  { value: 'EUR', label: 'EUR - 欧元', symbol: '€' },
  { value: 'GBP', label: 'GBP - 英镑', symbol: '£' },
  { value: 'JPY', label: 'JPY - 日元', symbol: '¥' },
  { value: 'HKD', label: 'HKD - 港币', symbol: 'HK$' },
  { value: 'SGD', label: 'SGD - 新加坡元', symbol: 'S$' },
  { value: 'AUD', label: 'AUD - 澳元', symbol: 'A$' },
  { value: 'CAD', label: 'CAD - 加元', symbol: 'C$' },
  { value: 'KRW', label: 'KRW - 韩元', symbol: '₩' },
  { value: 'THB', label: 'THB - 泰铢', symbol: '฿' },
  { value: 'MYR', label: 'MYR - 马来西亚林吉特', symbol: 'RM' },
  { value: 'INR', label: 'INR - 印度卢比', symbol: '₹' },
  { value: 'VND', label: 'VND - 越南盾', symbol: '₫' },
  { value: 'PHP', label: 'PHP - 菲律宾比索', symbol: '₱' },
  { value: 'IDR', label: 'IDR - 印尼盾', symbol: 'Rp' },
];

// 获取汇率缓存key
const getExchangeRateCacheKey = (fromCurrency, toCurrency) => {
  return `exchange_rate_${fromCurrency}_${toCurrency}`;
};

// 获取汇率数据
export const getExchangeRate = async (fromCurrency, toCurrency) => {
  // 如果是相同货币，直接返回1
  if (fromCurrency === toCurrency) {
    return {
      rate: 1,
      fromCurrency,
      toCurrency,
      lastUpdate: new Date().toISOString(),
      cached: false
    };
  }

  // 检查缓存
  const cacheKey = getExchangeRateCacheKey(fromCurrency, toCurrency);
  const cachedData = getCache(cacheKey);
  
  if (cachedData && cachedData.lastUpdate) {
    const cacheTime = new Date(cachedData.lastUpdate);
    const now = new Date();
    const diffHours = (now - cacheTime) / (1000 * 60 * 60);
    
    // 如果缓存时间小于1小时，使用缓存
    if (diffHours < 1) {
      return {
        ...cachedData,
        cached: true
      };
    }
  }

  try {
    // 调用免费汇率API
    const response = await fetch(`${EXCHANGE_API_BASE}/${fromCurrency}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.rates || !data.rates[toCurrency]) {
      throw new Error(`汇率数据不可用: ${fromCurrency} to ${toCurrency}`);
    }
    
    const rate = data.rates[toCurrency];
    const result = {
      rate,
      fromCurrency,
      toCurrency,
      lastUpdate: data.date || new Date().toISOString(),
      cached: false
    };
    
    // 缓存结果，有效期60分钟
    setCache(cacheKey, result, 60);
    
    return result;
    
  } catch (error) {
    console.error('获取汇率失败:', error);
    
    // 如果有缓存数据，即使过期也返回
    if (cachedData) {
      return {
        ...cachedData,
        cached: true,
        error: '网络错误，使用缓存数据'
      };
    }
    
    // 没有缓存数据时抛出错误
    throw new Error(`获取汇率失败: ${error.message}`);
  }
};

// 批量获取多个汇率
export const getBatchExchangeRates = async (fromCurrency, toCurrencies) => {
  const promises = toCurrencies.map(toCurrency => 
    getExchangeRate(fromCurrency, toCurrency)
      .then(result => ({ success: true, ...result }))
      .catch(error => ({ success: false, error: error.message, fromCurrency, toCurrency }))
  );
  
  return Promise.all(promises);
};

// 清除汇率缓存
export const clearExchangeRateCache = (fromCurrency, toCurrency) => {
  const cacheKey = getExchangeRateCacheKey(fromCurrency, toCurrency);
  localStorage.removeItem(cacheKey);
};

// 清除所有汇率缓存
export const clearAllExchangeRateCache = () => {
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.startsWith('exchange_rate_')) {
      localStorage.removeItem(key);
    }
  });
};
