import { useState, useEffect, useRef } from 'react';
import { useIntl, useLocation, history } from 'umi';
import { Dropdown, message } from 'antd';
import logo from '@/assets/logo.png';
import Language from '@/components/Language';
import { api, constant, } from '@/common/utils';
import { Helmet } from 'react-helmet';
import { LogoutOutlined } from '@ant-design/icons';
import avatar from '@/assets/avatar.png';

export default () => {
    const intl = useIntl(); //国际化
    const location = useLocation();
    const [abortController, setAbortController] = useState(new AbortController());
    // 菜单数据
    const menuData = [
        { key: 1, title: intl.formatMessage({ id: 'menu.workspace' }), path: '/workspace' },
        { key: 2, title: intl.formatMessage({ id: 'menu.tools' }), path: '/tools' },
        { key: 3, title: intl.formatMessage({ id: 'menu.rate' }), path: '/rate' },
        { key: 4, title: intl.formatMessage({ id: 'menu.booking' }), path: '/booking' },
        { key: 5, title: intl.formatMessage({ id: 'menu.order' }), path: '/order' },
        // { key: 6, title: intl.formatMessage({ id: 'menu.finance' }), path: '/finance' },
        { key: 7, title: intl.formatMessage({ id: 'menu.me' }), path: '/me' },
    ]
    // 子标题
    const subTitle = menuData.filter((item) => { return item.path == location.pathname })[0]?.title;

    const token = sessionStorage.getItem(constant.KEY_USER_TOKEN);
    const isLogin = token || false;
    const [currentUser, setCurrentUser] = useState({});

    useEffect(() => {
        getCurrentUser();
    }, []);

    const logout = () => {
        api.user.logout().subscribe({
            next: () => {
                sessionStorage.clear();
                history.push(`/login`);
            },
        });
    }

    const getCurrentUser = () => {
        const current = JSON.parse(sessionStorage.getItem(constant.KEY_CURRENT_USER));
        setCurrentUser(current);
    };

    const avatarItems = {
        items:
            [
                {
                    key: 'logout',
                    icon: <LogoutOutlined onClick={logout} />,
                    label: <span onClick={logout}>退出登录</span>,
                },
            ]
    }

    return (<>
        <Helmet>
            {subTitle && <title>{subTitle} - 客户服务平台</title>}
            <meta name="keywords" content="运价、订舱、舱单、对账" />
            <meta name="description" content="客户服务平台是一个专业化的在线运价服务平台，旨在为国际物流行业提供实时的运输成本信息和物流服务。" />
        </Helmet>
        <div className="za-header">
            <div className="za-header-container">
                <div className="za-header-logo">
                    <img src={logo} />
                </div>
                <div className="za-header-menu">
                    {
                        menuData.map(item => <div className="menu-item" key={item.key} >
                            <a href={item.path} className={item.path == ('/' + location.pathname.split("/")[1]) ? 'active' : ''} target='_blank'> {item.title}</a>
                        </div>)
                    }
                </div>
                <div className="za-header-user">
                    <div className="lang-item">
                        <Language />
                    </div>

                    {isLogin ?
                        <div >
                            <Dropdown menu={avatarItems} trigger={['click']}>
                                <div className="user-item">
                                    <img src={currentUser?.avatar ? constant.FILE_URL + currentUser?.avatar : avatar} />
                                    <span>{currentUser?.name}</span>
                                </div>
                            </Dropdown>
                        </div>
                        :
                        <div className="user-item">
                            <span onClick={() => history.push('/login')}>
                                登录
                            </span>
                        </div>
                    }
                </div>
            </div>
        </div>
    </>);
}
