import React, { useState, useEffect } from 'react';
import { Table, Tag, Button, message, Spin } from 'antd';
import { DownOutlined, RightOutlined } from '@ant-design/icons';
import { api, dateFormat, formatMoney, formatNumber } from '@/common/utils';
import * as R from 'ramda';
import DICTIONARY from '@/common/dictionary';

export default (props) => {

    const { orderId, } = props;
    const { clientWidth, clientHeight } = window?.document?.documentElement;
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [fees, setFees] = useState([]);
    const [feeData, setFeeData] = useState({});
    const [loading, setLoading] = useState({});
    const [unitCodeDic, setUnitCodeDic] = useState({});

    useEffect(() => {
        setFees([]);
        getFeeInfo(props.orderId);
        getCode();
    }, [props.orderId]);

    const getCode = (id) => {
        api.dict.listMapByParentCode(DICTIONARY.DICT_CHARGE_TYPE_TAG).subscribe({
            next: (data) => {
                setUnitCodeDic(data);
            }
        })
    }

    const columns = [
        {
            title: 'No.',
            width: 40,
            align: 'center',
            dataIndex: '',
            render: (text, record, index) => <>{index + 1}</>,
        },
        {
            title: '结算单位',
            dataIndex: 'customerName',
            width: 120,
            ellipsis: true,
        },
        {
            title: '费用名称',
            align: 'center',
            dataIndex: 'feeItem',
            width: 80,
            ellipsis: true,
        },
        {
            title: '计费单位',
            align: 'center',
            dataIndex: 'baseUnit',
            width: 80,
            render: (text) => <>{unitCodeDic[text]}</>
        },
        {
            title: '单价',
            align: 'center',
            dataIndex: 'price',
            width: 80,
            render: (text) => <>{formatNumber(text, 4)}</>
        },
        {
            title: '数量',
            align: 'center',
            width: 80,
            dataIndex: 'quantity',
        },
        {
            title: '币别',
            align: 'center',
            width: 80,
            dataIndex: 'currency',
        },
        {
            title: '金额',
            align: 'center',
            width: 80,
            dataIndex: 'money',
            render: (text) => <>{formatNumber(text, 2)}</>
        },
        {
            title: '是否开票',
            align: 'center',
            width: 80,
            dataIndex: 'invoiceState',
            render: (text) => <>{text === 'ALL_INVOICED' ? <Tag color="#87d068">是</Tag> : <Tag color="#f50">否</Tag>}</>
        },
        {
            title: '是否付费',
            align: 'center',
            width: 80,
            dataIndex: 'settleState',
            render: (text) => <>{text === 'ALL_SETTLE' ? <Tag color="#87d068">是</Tag> : <Tag color="#ff5500">否</Tag>}</>
        },
        {
            title: '创建时间',
            align: 'center',
            width: 80,
            dataIndex: 'createTime',
            render: (text) => <>{dateFormat(text)}</>
        },
    ];

    const getFeeInfo = (id) => {
        setLoading(true);
        api.order.listFees(id).subscribe({
            next: (data) => {
                let rmb = 0, usd = 0;
                R.forEach((v) => {
                    if (v.currency === 'USD') {
                        usd = parseFloat(usd) + parseFloat(v.money);
                    } else if (v.currency === 'RMB') {
                        rmb = parseFloat(rmb) + parseFloat(v.money);
                    }
                }, data)
                setFeeData({ rmbTotal: rmb, usdTotal: usd, });
                setFees(data);
            }
        }).add(() => {
            setLoading(false);
        });
    };

    const onSelectChange = (selectedKeys, selectedRows) => {
        setSelectedRowKeys(selectedKeys);
        setSelectedRows(selectedRows);
    };

    //添加对账
    const addBill = () => {
        if (selectedRowKeys.length === 0) {
            message.error("请至少选择一条数据!");
            return
        }
        let flag = true;
        R.forEach((v) => {
            if (v.beBill && flag) {
                message.error('费用 ' + v.feeItem + ' 已加入对账单,不可重复添加!');
                flag = false;
            }
        }, selectedRows);
        if (flag) {
            setLoading(true);
            api.order.addBill({ fees: selectedRows }).subscribe({
                next: (data) => {
                    message.success('操作成功!');
                    getFeeInfo(orderId);
                }
            }).add(() => {
                setLoading(false);
            });
        }
    };
    const addInvoice = () => {
        message.warning('正在开发中....');
    }



    return <>
        <Spin spinning={loading}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '10px 0' }}>
                <div>合计费用 RMB:<span style={{ color: '#ff5500' }}>{formatMoney(feeData.rmbTotal || 0.00)}</span>  USD:<span style={{ color: '#ff5500' }}>{formatMoney(feeData.usdTotal || 0.00)}</span></div>
                {/* <div>
                    <Button type='primary' style={{ width: '100px' }} onClick={() => addBill()}>加入对账</Button>
                    <Button type='primary' style={{ width: '100px', marginLeft: '10px' }} onClick={() => addInvoice()}>开票申请</Button>
                </div> */}

            </div>
            <Table
                rowKey='id'
                size='small'
                columns={columns}
                dataSource={fees}
                scroll={{ y: (clientHeight - 335) }}
                pagination={false}
                style={{ margin: '20px 0', border: '1px solid #f0f0f0', borderRadius: '5px' }}
                expandable={{
                    expandedRowRender: (record) => (
                        <p><span>备注：</span>{record.note} </p>
                    ),
                    expandIcon: ({ expanded, onExpand, record }) =>
                        expanded ?
                            (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                            :
                            (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                }}
                // rowSelection={{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }}
            />
        </Spin>
    </>
}