import { useState, useRef, useEffect } from 'react';
import { Layout, Input, Card, Avatar, Button, Form, Row, Col, Spin } from 'antd';
import { CommentOutlined, UserOutlined, SlackOutlined } from '@ant-design/icons';
import { api, copyObject, dateFormat, constant, ipost, produce } from '@/common/utils';

import './index.less';
import { set } from 'lscache';
import me from '@/pages/me';
const { Footer, Content } = Layout;
const { TextArea } = Input;

export default (props) => {

    const [form] = Form.useForm();
    const [chatList, setChatList] = useState([]);
    const [loading, setLoading] = useState(false);

    const send = (message) => {
        api.nav.send(message).subscribe({
            next: (data) => {

            }
        })
    }

    const subscribe = (event) => {
        api.nav.subscribe([event]).subscribe({
            next: (data) => {

            }
        });
    };

    useEffect(() => {
        setChatList([]);
        form.resetFields();
        subscribe(props.id);
        listChats(props.id);
        // return () => {
        //     doUnSub(props.id);
        // };
    }, [props.id, props.activeKey, props.open]);

    useEffect(() => {
        // Fix 2: Store subscription token in sub variable
        let sub = PubSub.subscribe(props.id, (msg, data) => {
            let param = { role: 'system', content: data, bookOrderId: props.id };
            const d = produce(chatList, (draft) => {
                draft.push(param);
            })
            setChatList(d);
            setTimeout(() => {
                scrollToBottom();
            }, 100);
        });

        return () => { // Fix 3: Unsubscribe after re-rendered
            PubSub.unsubscribe(sub);
        };
    }, [chatList]);

    const listChats = (e) => {
        api.bookOrder.listChats(e).subscribe({
            next: (data) => {
                setChatList(data);
                setTimeout(() => {
                    scrollToBottom();
                }, 100);
            },
        }).add(() => {
            // setLoading(false);
        });
    }

    const onFinish = (e) => {
        let param = { role: 'user', content: e.question, bookOrderId: props.id };
        setLoading(true);
        api.bookOrder.sendChat(param).subscribe({
            next: (data) => {
                setChatList(chatList.concat([param]));
                setTimeout(() => {
                    scrollToBottom();
                }, 100);
                form.resetFields();
                send({ subject: props.id, message: e.question });
                // setTimeout(() => {
                //     listChats(props.id);
                // }, 1000);
            }
        }).add(() => {
            setLoading(false);
        });
    };

    function scrollToBottom() {
        var chatContainer = document.getElementById('chat-container');
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }

    return (
        <Layout style={{ background: '#fff' }}>
            <Content style={props.style} id="chat-container">
                <div className='chat-result'>
                    {chatList.map((item, index) =>
                        item.role == 'user' ? <div className='list-item question' key={index}>
                            <Card className='content'>
                                {item.content}
                            </Card>
                            <Avatar
                                className='header'
                                icon={<UserOutlined />}
                            />
                        </div> : <div className='list-item answer'>
                            <Avatar
                                className='header'
                                icon={<SlackOutlined />}
                            />
                            <Card className='content'>
                                <p dangerouslySetInnerHTML={{ __html: item.content }}></p>
                            </Card>
                        </div>
                    )}
                </div>
            </Content>
            <Footer style={{ padding: '0' }}>
                <div className='chat-ask'>
                    <Form
                        className='chat-form'
                        onFinish={onFinish}
                        form={form}
                    >
                        <Row wrap="no">
                            <Col flex="auto">
                                <Form.Item label="" name="question">
                                    <TextArea className='question' placeholder="请输入您的问题" bordered={false} />
                                </Form.Item>
                            </Col>
                            <Col flex="100px">
                                <Button className='send' type="primary" size='large' htmlType="submit" loading={loading}>发送</Button>
                            </Col>
                        </Row>
                    </Form>
                </div>
            </Footer>
        </Layout>)
}