import React, { useState } from 'react';
import { Button, Row, Form, Tag, Divider, Table, Pagination, Spin, message } from 'antd';
import { RedoOutlined, DownOutlined, RightOutlined, } from '@ant-design/icons';
import Texty from 'rc-texty';
import AnimDate from '@/components/AnimDate';
import AnimInput from '@/components/AnimInput';
import AnimSelect from '@/components/AnimSelect';
import './index.less';
import { api, dateFormat, formatMoney } from '@/common/utils';
import FileViewer from '@/components/FileViewer';
import InvoiceView from '@/components/InvoiceView';


export default () => {

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchForm] = Form.useForm();
  const [datas, setDatas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [fileUrl, setFileUrl] = useState('');
  const [invoiceVisible, setInvoiceVisible] = useState(false);
  const [record, setRecord] = useState({});

  const tags = {
    'UNCOMMITED': { color: 'default', text: '未申请' },
    'APPROVED': { color: 'processing', text: '已申请' },
    'INVOICED': { color: 'success', text: '已开票' },
    'DISCARDED': { color: 'error', text: '已作废' },
  };

  const columns = [
    { title: '状态', dataIndex: 'state', width: 75, render: (text, record) => <> <Tag bordered={false} color={tags[text]?.color}>{tags[text]?.text}</Tag></> },
    { title: '发票抬头', dataIndex: 'toTitle', width: 143, ellipsis: true, },
    { title: '应付RMB', dataIndex: 'totalRmb', width: 90, render: (text, record) => <>{formatMoney(text)}</> },
    { title: '应付USD', dataIndex: 'totalUsd', width: 90, render: (text, record) => <>{formatMoney(text)}</> },
    { title: '开票币别', dataIndex: 'invoiceCurrency', width: 80 },
    { title: '发票号', dataIndex: 'invoiceNo', width: 100 ,ellipsis: true,},
    { title: '开票金额', dataIndex: 'invoiceMoney', width: 100, render: (text, record) => <>{formatMoney(text)}</> },
    { title: '开票日期', dataIndex: 'invoiceDate', width: 100, render: (text, record) => <>{dateFormat(text)}</> },
    {
      title: '操作', dataIndex: 'option', width: 80, align: 'center', render: (text, record) =>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', color: '#B4141B' }}>
          <div onClick={() => previewFile(record)}>预览</div>
          <div onClick={() => download(record)}>下载</div>
        </div>
    },
  ]

  const onSelectChange = (selectedKeys, selectedRows) => {
    setSelectedRowKeys(selectedKeys);
  };

  const onSearch = (pageNo, pageSize) => {
    setPageNo(pageNo);
    setPageSize(pageSize);
    setSearchLoading(true);
    let searchData = searchForm.getFieldValue();
    let param = { dto: searchData, pageNo: pageNo, pageSize: pageSize };
    api.invoiceApply.searchInvoice(param).subscribe({
      next: (data) => {
        setTotal(data.total);
        setDatas(data.data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };

  const onShowSizeChange = (current, pageSize) => {
    onSearch(current, pageSize);
  };

  //下载发票
  const download = (record) => {
    let fileName =
      record.invoiceNo + record.invoiceUrl?.substring(record.invoiceUrl?.lastIndexOf('.'));
    const params = {
      fileUrl: record.invoiceUrl,
      fileName: fileName
    };
    setLoading(true);
    api.base.download(params).subscribe({
      next: (res) => {
        console.log(res)
        const blob = new Blob([res], {
          type: 'application/octet-stream',
        });
        if (window.navigator.msSaveOrOpenBlob) {
          navigator.msSaveBlob(blob, fileName);
        } else {
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
          // 释放内存
          window.URL.revokeObjectURL(link.href);
        }
      }
    }).add(() => {
      setLoading(false);
    });
  };

  //预览
  const previewFile = (record) => {
    if (record.invoiceState != 'OFFLINE') {
      setInvoiceVisible(true);
      setRecord(record);
    } else {
      setFileUrl(record.invoiceUrl);
      setPreviewVisible(true);
    }
  }


  return (
    <>
      <Spin spinning={loading}>
        <div className="za-content bill">
          <div className="bill-content">
            <div className="bill-slogan">
              <div className="slogan-title">
                <Texty delay={1000}>财务管理</Texty>
              </div>
              <div className="slogan-sub-title">
                <Texty delay={2000} type="flash">支持在线对账、在线发票申请、在线付款、在线申请放单。</Texty>
              </div>
            </div>

            <div className="bill-search">
              <Form
                form={searchForm}
                name="searchRef"
                onFinish={() => onSearch(1, pageSize)}
              >
                <div className='search-default'>
                  <Row>
                    <div className='search-code'>
                      <Form.Item name="state" >
                        <AnimSelect label="发票状态" options={[
                          { value: 'INVOICED', label: '已开票' },
                          { value: 'DISCARDED', label: '已作废' },
                        ]} />
                      </Form.Item>
                    </div>
                    <div className='search-code'>
                      <Form.Item name="blNo" >
                        <AnimInput label="主提单号" />
                      </Form.Item>
                    </div>
                    <div className="search-checkUserName">
                      <Form.Item name="invoiceNo" >
                        <AnimInput label="发票号" />
                      </Form.Item>
                    </div>
                  </Row>
                  <Row style={{ marginTop: '15px' }}>
                    <div className="search-atd">
                      <Form.Item name="invoiceCurrency" >
                        <AnimSelect label="开票币别" options={[
                          { value: 'RMB', label: 'RMB' },
                          { value: 'USD', label: 'USD' },
                        ]} />
                      </Form.Item>
                    </div>
                    <div className="search-billDate">
                      <Form.Item name="invoiceDateStart" >
                        <AnimDate label="从开票日" />
                      </Form.Item>
                    </div>
                    <div className="search-billDate">
                      <Form.Item name="invoiceDateEnd" >
                        <AnimDate label="到开票日" />
                      </Form.Item>
                    </div>
                    <Button htmlType="submit" className="search-btn" loading={searchLoading}>查 询</Button>
                  </Row>
                </div>

                <div className='search-tool'>
                  <div className="tool-btn">
                    <Button type="text" icon={<RedoOutlined />} size="small" htmlType="reset">
                      重置
                    </Button>
                  </div>
                </div>
              </Form>
              <Divider style={{ margin: '16px 0' }} />
              <div className='bill-list'>

                {/* {selectedRowKeys.length > 0 && <div className='bill-alert'>
                <span> {'已选择 ' + selectedRowKeys.length + ' 项'}</span>
                <span style={{ color: '#B4141B' }} onClick={() => setSelectedRowKeys([])}>取消选择</span></div>} */}
                <Table
                  className='bill-table'
                  rowKey='id'
                  pagination={false}
                  style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
                  rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
                  columns={columns}
                  dataSource={datas}
                  expandable={{
                    expandedRowRender: (record) => (
                      <div className='bill-expand'>
                        <p><span>备注：</span>{record.note} </p>
                      </div>
                    ),
                    expandIcon: ({ expanded, onExpand, record }) =>
                      expanded ?
                        (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                        :
                        (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                  }}
                />
              </div>
              <div className='bill-pagination'>
                <Pagination
                  pageSizeOptions={[10, 20, 50]}
                  showSizeChanger
                  onShowSizeChange={onShowSizeChange}
                  onChange={onShowSizeChange}
                  // defaultCurrent={p}
                  total={total || 0}
                  current={pageNo || 0}
                  pageSize={pageSize || 10}
                  showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`}
                />

              </div>
            </div>
          </div>
        </div>


      </Spin>
      <FileViewer
        visible={previewVisible}
        fileUrl={fileUrl}
        onClose={() => setPreviewVisible(false)}
      />
      <InvoiceView
        visible={invoiceVisible}
        record={record}
        onClose={() => setInvoiceVisible(false)}
      />

    </>


  );
};
