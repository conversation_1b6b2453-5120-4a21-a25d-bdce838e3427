import { Dropdown } from 'antd';
import langCn from '@/assets/lang-cn.png';
import langEn from '@/assets/lang-en.png';
import { getLocale, setLocale } from 'umi';

export default () => {
    const intlMap = [
        { label: '简体中文', key: 'zh-CN' ,pic: langCn},
        { label: 'ENGLISH', key: 'en'  ,pic: langEn}
    ];

    const doChange = (value) => {
        setLocale(value.key, false);
    }

    return (<Dropdown menu={{ items: intlMap,onClick: doChange}}>
        <div style={{ display: 'flex'}}>
            <img src={intlMap.filter((item) => { return item.key == (getLocale("umi_locale") || "zh-CN"); })[0]?.pic} style={{ width:'28px',height:'28px' }} />
        </div>
    </Dropdown>);
}

