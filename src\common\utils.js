import { produce } from 'immer';
import * as _ from 'lodash';
import objectAssign from 'object-assign';
import { parse } from 'querystring';
import * as R from 'ramda';
import { useEffect, useState, useRef } from 'react';

import md5 from 'js-md5';


import { tap } from 'rxjs';


import moment from 'moment';


import { idelete, idownload, iget, isget, ilogin, ipost, ispost, iput, isearch, iupload, opost, ipostNoFilter, idict, iuploads } from './iaxios';

import api from './service';

import constant from './constant';

import lscache from 'lscache';

export const setCache = (key, value, expireInMinutes) => {
  let iexpire = expireInMinutes || Math.round(10 * 100);
  lscache.set(key, value, iexpire);
};

export const getCache = (key) => {
  return key && lscache.get(key);
};

export const hasCache = (key) => {
  return !!getCache(key);
};

export const removeCache = (key) => {
  lscache.remove(key);
};

export const useInterval = (callback, delay) => {
  const savedCallback = useRef();

  // Remember the latest callback.
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  // Set up the interval.
  useEffect(() => {
    function tick() {
      savedCallback.current();
    }
    if (delay !== null) {
      let id = setInterval(tick, delay);
      return () => clearInterval(id);
    }
  }, [delay]);
};

export const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    clientWidth: window.innerWidth,
    clientHeight: window.innerHeight,
  });
  useEffect(() => {
    const updateSize = () =>
      setWindowSize({ clientWidth: window.innerWidth, clientHeight: window.innerHeight });
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);
  return windowSize;
};

//==========================================
// const fn = () => {}
// isFunction(fn) //=> true
//==========================================
export const isFunction = (value) => {
  return _.isFunction(value);
};

//==========================================
// const arr = []
// isArray(arr) //=> true
//==========================================
export const isArray = (value) => {
  return _.isArray(value);
};

//==========================================
// has({name: 'alice'},'name')   //=> true
// has({},'name') //=> false
//==========================================
export const has = (obj, prop) => {
  return _.has(obj, prop);
};

//==========================================
// isNil(undefined)   //=> true
// isNil('111') //=> false
//==========================================
export const isNil = (data) => {
  return _.isNil(data);
};


export const cloneDeep = (data) => {
  return _.cloneDeep(data);
};

//==========================================
// const App = (props) => {
//   const [onChange, value] = useObservableAutoCallback((event$) =>
//     event$.pipe(pluck("currentTarget", "value"))
//   );
//
//   return <>
//     <input type="text" onChange={onChange} />
//      <span>{value}</span>
//    <>;
// };
//==========================================
// import { useObservableAutoCallback } from './reactor';
// export { useObservableAutoCallback };
export const useObservableAutoCallback = (init, callback) => {
  const [data, setData] = useState();

  const [trggerFn, callbackEvent] = useObservableCallback(init);
  useEffect(() => {
    const sub = callbackEvent.subscribe({ next: (result) => setData(result) });
    sub.add(() => {
      if (callback && isFunction(callback)) {
        callback();
      }
    });
    return () => {
      sub.unsubscribe();
    };
  }, [init]);

  // const subRef = useSubscription(callbackEvent, setData);
  // console.log(subRef);
  // if (callback) {
  //   subRef.current.add(() => {
  //     callback();
  //   });
  // }
  // useEffect(() => {}, []);
  return [trggerFn, data, setData];
};

//==========================================
// const App = (props) => {
//   const [onClick, loading] = useAutoObservableEvent(
//        filter((keys) => !isEmpty(keys)),
//        switchMap((keys) => api.user.activeUser(keys)),
//        );
//
//   return <>
//     <button onClikec={() => onClick(values)} loading={loading} />
//
//    <>;
// };
//==========================================
export const useObservableAutoLoadingEvent = (...operations) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState();
  const [trggerFn, callbackEvent] = useObservableCallback((event) =>
    event.pipe(
      // debounceTime(300),
      // distinctUntilChanged(),
      tap(() => setLoading(true)),
      operations,
      shareReplay(1),
    ),
  );
  useEffect(() => {
    const sub = callbackEvent.subscribe({ next: (result) => setData(result) });
    sub.add(() => {
      setLoading(false);
    });
    return () => {
      sub.unsubscribe();
    };
  }, []);
  return [trggerFn, loading, data, setData];
};



//==========================================
// const arr = [{name: 'fred', age: 29}, {name: 'wilma', age: 27}]
// pluk('age', arr);  //=> [29, 27]
//==========================================
export const pluck = (p, list) => {
  return R.pluck(p, list);
};

//==========================================
// const arr = [{name: 'fred', age: 29, sex:'male'}, {name: 'wilma', age: 27, sex:'female'}]
// project(['age','name'], arr);  //=> [{name: 'fred', age: 29}, {name: 'wilma', age: 27}]
//==========================================
export const project = (props, list) => {
  return R.project(props, list);
};

export const includes = (item, list) => {
  return R.includes(item, list);
}

//==========================================
// forEach((v)=>{}, array);
//==========================================
export const forEach = (fn, list) => {
  return R.forEach(fn, list);
};

//==========================================
// map((v)=>{}, array); return the new list;
//==========================================
export const rmap = (fn, list) => {
  return R.map(fn, list);
};

//==========================================
// forEachIndex((v, index, array)=>{}, array);
//==========================================
export const forEachIndex = (fn, list) => {
  const mapIndexed = R.addIndex(R.map);
  return mapIndexed(fn, list);
};

//==========================================
// const arr = [1,2,3,4]
// remove(1, -1, arr); //=> [1,2,3]
//==========================================
export const remove = (index, number, list) => {
  return R.remove(index, number, list);
};

//==========================================
// const arr = [1,2,3,4]
// insert(1, [5,6], arr); //=> [1,2,5,6,3,4]
//==========================================
export const insert = (index, arr, list) => {
  return R.insertAll(index, arr, list);
};

//==========================================
// const arr = [1,2,3,4]
// filter((v) => v%2 === 0, arr); //=> [2,4]
//==========================================
export const filter = (fn, list) => {
  return R.filter(fn, list);
};

//==========================================
// const arr = [1,2,3,4]
// reject((v) => v%2 === 0, arr); //=> [1,3]
//==========================================
export const reject = (fn, list) => {
  return R.reject(fn, list);
};

//==========================================
// const arr = [1,2,3,4]
// sort((a,b) => a -b, arr); //=> [1,2,3,4]
//==========================================
export const sort = (fn, list) => {
  return R.sort(fn, list);
};

//==========================================
// const arr = [{name: 'fred', age: 29, sex:'male'}, {name: 'wilma', age: 27, sex:'female'}]
// groupBy((v) => v.sex, arr) //=>{'male':[{name:'fred', age:29, sex:'male'}], 'female':[{name:'wilma', age:27, sex}]}
//==========================================
export const groupBy = (fn, list) => {
  return R.groupBy(fn, list);
};

//==========================================
// const arr = {x: 1, y: 2}
// props(['x', 'y'], arr) //=>[1,2]
//==========================================
export const props = (props, list) => {
  return R.props(props, list);
};

//==========================================
// const obj = {x: 1, y: 2}
// invert(obj) //=>{1:x,2:y}
//==========================================
export const invert = (obj) => {
  return _.invert(obj);
};

//==========================================
// const arr = {x: 1, y: 2}
// assoc('x', 2, arr) //=> {x: 2, y: 2}
// assoc('z', 2, arr) //=> {x: 1, y: 2, z:2}
//==========================================
export const assoc = (prop, value, obj) => {
  return R.assoc(prop, value, obj);
};

//==========================================
// const arr = {x: 1, y: 2, z:3, a:4}
// pick(['x', 'y'], arr) //=> {x: 2, y: 2}
//==========================================
export const pick = (arr, obj) => {
  return R.pick(arr, obj);
};

//==========================================
// const arr = []
// isEmpty(arr) //=> true
//==========================================
export const isEmpty = (objOrArr) => {
  if (R.isNil(objOrArr)) return true;
  return R.isEmpty(objOrArr);
};

//==========================================
// const arr = ['a','b','c']
// contains('a',arr) //=> true
//==========================================
export const contains = (item, array) => {
  return array.includes(item);
};

//==========================================
// const arr = ['a','b','c']
// join(arr) //=> 'a,b,c'
//==========================================
export const join = (x, arr) => {
  return R.join(x, arr);
};

//==========================================
// const arr = {x: 1, y: 2, z:3, a:4}
// keys(arr) //=> [x,y,z,a]
//==========================================
export const keys = (obj) => {
  return R.keys(obj);
};

//==========================================
// const arr = {x: 1, y: 2, z:3, a:4}
// values(arr) //=> [1,2,3,4]
//==========================================
export const values = (obj) => {
  return R.values(obj);
};

//==========================================
// forEachObject((value, key, obj)=>{}, obj);
//==========================================
export const forEachObject = (fn, obj) => {
  return R.forEachObjIndexed(fn, obj);
};

//==========================================
// mapObjIndexed((value, key, obj)=>{}, obj);
//==========================================
export const mapObjIndexed = (fn, obj) => {
  return R.mapObjIndexed(fn, obj);
}

//==========================================
// const obj = {x:1,y:2};
// copyObject(obj, {z:3});
//==========================================
export const copyObject = (target, source, source2) => {
  if (source2) {
    return objectAssign(target, source, source2);
  }
  return objectAssign(target, source);

};

//==========================================
// startsWith('a', 'abc')       //=> true
//==========================================
export const startsWith = (x, list) => {
  return R.startsWith(x, list)
}

// //==========================================
// // product((action)=>{state[p] = 1}, state)
// //==========================================
// export const product = (state, fn) => {
//   return produce(state, fn);
// };

//==========================================
// split('/usr/local/bin/node','/') => ['usr', 'local', 'bin', 'node']
//==========================================
export const split = (value, s) => {
  let spChar = ',';
  if (s) {
    spChar = s;
  }
  return R.split(spChar, value);
};

// 检查是否为数字
const isNumber = (value) => typeof value === 'number' && !isNaN(value);

// 为数字添加前导零
const padLeftZero = (str, length = 2) => {
  return (Array(length).join('0') + str).slice(-length);
};

// 日期格式化函数
export const dateFormat = (d, fmt) => {
  // 默认格式
  if (!fmt) {
    fmt = 'yyyy-MM-dd';
  }

  // 处理输入的日期
  const theDate = isNumber(d) ? new Date(d) : new Date(d);
  let format = fmt;

  if (theDate instanceof Date && !isNaN(theDate.getTime())) {
    // 年份处理
    if (/(y+)/.test(format)) {
      format = format.replace(RegExp.$1, `${theDate.getFullYear()}`.substr(4 - RegExp.$1.length));
    }

    // 其他部分处理
    const o = {
      'M+': theDate.getMonth() + 1, // 注意：月份从0开始，所以需要加1
      'd+': theDate.getDate(),
      'H+': theDate.getHours(),
      'h+': theDate.getHours(), // 24小时制
      'm+': theDate.getMinutes(),
      's+': theDate.getSeconds(),
    };

    for (const k in o) {
      if (new RegExp(`(${k})`).test(format)) {
        const str = o[k] + '';
        format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str));
      }
    }
  } else {
    format = ''; // 如果日期无效，则返回空字符串
  }

  return format;
};


//===========================================
//  const cabinStatus = {
//    UNSEND: { color: 'Default', text: '未发送' },
//    FAILURE: { color: 'Error', text: '发送失败' },
//    SENDED: { color: 'Success', text: '已发送' },
//  };
//  state2Option(cabinStatus) =>
//  [{value:UNSEND, label:'未发送'},{value:FAILURE, label:'发送失败'},{value:SENDED, label:'已发送'}]
//===========================================
export const state2Option = (state) => {
  const options = [];
  forEachObject((v, k, obj) => {
    options.push({ value: k, label: v.text });
  }, state);
  return options;
};

//===========================================
//  const cabinOptons =
//  [{value:UNSEND, label:'未发送',color: 'red'},
//   {value:FAILURE, label:'发送失败',color: 'blue'},
//   {value:SENDED, label:'已发送',color: 'green'}]
//  option2States(cabinStatus) =>
//  {
//    UNSEND: { color: 'red', text: '未发送' },
//    FAILURE: { color: 'blue', text: '发送失败' },
//    SENDED: { color: 'green', text: '已发送' },
//  };
//===========================================
export const option2States = (options) => {
  const state = {};
  options &&
    forEach((v) => {
      state[v.value] = { text: v.label, color: v.color || 'default' };
    }, options);
  return state;
};

//===========================================
//  const cabinOptons =
//  {id: '1403176084383465473', dictCode: 'FREIGHT_COLLECTED', dictName: 'FREIGHT COLLECTED'}
//  {id: '1403176192315490305', dictCode: 'FREIGHT_PREPAID', dictName: 'FREIGHT PREPAID'}
//  data2States(cabinStatus) =>
//  {
//    FREIGHT_COLLECTED: { color: 'Default', text: 'FREIGHT COLLECTED' },
//    FREIGHT_PREPAID: { color: 'Default', text: 'FREIGHT PREPAID' },
//  };
//===========================================
export const data2States = (valueProp, labelProp, options) => {
  const state = {};
  options &&
    forEach((v) => {
      state[v[valueProp]] = { text: v[labelProp], color: v.color || 'default' };
    }, options);
  return state;
};
//===========================================
//  const cabinOptons =
//  [{value:UNSEND, label:'未发送',color: 'red'},
//   {value:FAILURE, label:'发送失败',color: 'blue'},
//   {value:SENDED, label:'已发送',color: 'green'}]
//  option2States(cabinStatus) =>
//  {
//    UNSEND: '未发送',
//    FAILURE: '发送失败',
//    SENDED: '已发送',
//  };
//===========================================
export const option2TextObject = (options) => {
  const state = {};
  options &&
    forEach((v) => {
      state[v.value] = v.label;
    }, options);
  return state;
};

export const data2TextObject = (keyProp, valueProp, data) => {
  const state = {}
  forEach((v) => {
    state[v[keyProp]] = v[valueProp];
  }, data);
  return state;
}

//===========================================
// const data = [{id:12343453, name:'经理', age:23},{id:12332453, name:'专员', age:26}]
// data2Option（'id','name',data) => [{label:'经理', value:12343453},{label:'专员',value:12332453}]
//===========================================
export const data2Option = (valueProp, labelProp, data) => {
  const results = [];
  forEach((v) => {
    results.push({ label: v[labelProp], value: v[valueProp] });
  }, data);
  return results;
};

//===========================================
// Get The Query String Of URL
//===========================================
export const getQueryString = (name) => {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
  const search = window.location.search.split('?')[1] || '';
  const r = search.match(reg) || [];
  return r[2];
};

//===========================================
// Get The Query String Of URL
//===========================================
export const getPageQuery = () => parse(window.location.href.split('?')[1]);

//===========================================
// check all the row prop value equals some value
// const students = [{name:'one', sex:'male'},{name:'two',sex:male}]
// beAllRowsPropEqual('sex', 'male', students) => true
//===========================================
export const beAllRowsPropEqual = (prop, value, rows) => {
  const values = pluck(prop, rows);
  let valueSets = [...new Set(values)];
  return valueSets.length == 1 && valueSets[0] === value;
};

//===========================================
// check has any row prop value  not equals a value
// const students = [{name:'one', sex:'male'},{name:'two',sex:male},{name:'three',sex:'female'}]
// beHasRowsPropNotEqual('sex', 'male', students) => true
//===========================================
export const beHasRowsPropNotEqual = (prop, value, rows) => {
  const values = pluck(prop, rows);
  let valueSets = [...new Set(values)];
  return valueSets.length > 1 || valueSets[0] !== value;
};

//===========================================
//useage:
//INewWindow({
// url: '/new/group/company',
// title: '编辑XX',
// width: 600,
// height: 300,
// callback: () => search(pageNo, pageSize),
// callparam: () => param,
//});
//in the window components: window.close(); window.opener.onSuccess();
//===========================================
const sWidth = window.screen.width;
const sHeight = window.screen.height;
export const INewWindow = (props) => {
  const { url, title, width, height, callback, callparam, features } = props;
  const iwidth = width || sWidth;
  const iheight = height || sHeight

  var itop = (window.screen.height - 30 - (height || 0)) / 2;       //获得窗口的垂直位置;
  var ileft = (window.screen.width - 10 - (width || 0)) / 2;
  let browser = window.self;
  let popup = null;

  browser = window.self
  browser.onSuccess = (message) => {
    if (callback && _.isFunction(callback)) {
      callback(message);
    }
  }

  browser.onGetParams = () => {
    if (callparam && _.isFunction(callparam)) {
      return callparam();
    }
  }

  browser.onError = (error) => {
    if (callback && _.isFunction(callback)) {
      callback(error);
    }
  }

  browser.onOpen = (message) => {
    if (callback && _.isFunction(callback)) {
      callback(message);
    }
  }

  browser.onClose = (message) => {
    if (callback && _.isFunction(callback)) {
      callback(message);
    }
  }
  const opts = features || ('location=no,menubar=no,toolbar=no,resizable=no,status=no,width=' + (iwidth) + ',  height=' + (iheight) + ',top=' + itop + ',left=' + ileft);


  let settings = localStorage.getItem("settings");
  console.log(settings);
  if (settings) {
    browser.localStorage.setItem("settings", settings);
  }
  const getPageQuery = () => parse(url.split('?')[1]);
  window.getPageQuery = getPageQuery;
  popup = browser.open(url, title, opts)

  setTimeout(function () { popup.document.title = title }, 200);

};

//===========================================
// format time
// const n = formatTime(1727574691625, "y-m-d h:i") => 2024-09-29 09:52
//===========================================
export const formatTime = (d, str) => {
  let date = new Date(d),
    year = date.getFullYear(), //年
    month = date.getMonth() + 1, //月
    day = date.getDate(), //日
    hour = date.getHours(), //时
    minute = date.getMinutes(), //分
    second = date.getSeconds(); //秒

  month >= 1 && month <= 9 ? (month = "0" + month) : "";
  day >= 0 && day <= 9 ? (day = "0" + day) : "";
  hour >= 0 && hour <= 9 ? hour : "";
  minute >= 0 && minute <= 9 ? (minute = "0" + minute) : "";
  second >= 0 && second <= 9 ? (second = "0" + second) : "";

  if (str.indexOf('y') != -1) {
    str = str.replace('y', year)
  }
  if (str.indexOf('m') != -1) {
    str = str.replace('m', month)
  }
  if (str.indexOf('d') != -1) {
    str = str.replace('d', day)
  }
  if (str.indexOf('h') != -1) {
    str = str.replace('h', hour)
  }
  if (str.indexOf('i') != -1) {
    str = str.replace('i', minute)
  }
  if (str.indexOf('s') != -1) {
    str = str.replace('s', second)
  }
  return str;
}

//===========================================
// format a number equals to formatNumber
// const n = toFix(3.141592653589793, 2) => 3.14
//===========================================
function toFixed(n, d) {
  var s = n + "";
  if (!d) d = 0;
  if (s.indexOf(".") == -1) s += ".";
  s += new Array(d + 1).join("0");
  if (new RegExp("^(-|\\+)?(\\d+(\\.\\d{0," + (d + 1) + "})?)\\d*$").test(s)) {
    var s = "0" + RegExp.$2, pm = RegExp.$1, a = RegExp.$3.length, b = true;
    if (a == d + 2) {
      a = s.match(/\d/g);
      if (parseInt(a[a.length - 1]) > 4) {
        for (var i = a.length - 2; i >= 0; i--) {
          a[i] = parseInt(a[i]) + 1;
          if (a[i] == 10) {
            a[i] = 0;
            b = i != 1;
          } else break;
        }
      }
      s = a.join("").replace(new RegExp("(\\d+)(\\d{" + d + "})\\d$"), "$1.$2");

    }
    if (b) s = s.substr(1);
    return (pm + s).replace(/\.$/, "");
  }
  return this + "";
};

//格式化moeny
export const formatMoney = (v) => {
  return v ? toFixed(v, 2) : '0.00';
}

export { produce };
export { md5 };
export { parse }
export { moment };

export { isearch, ipost, ispost, iput, iget, isget, idelete, iupload, idownload, ilogin, opost, ipostNoFilter, idict, iuploads };
export { api };
export { constant };


//格式化数字
export const formatNumber = (v, fixNum) => {
  if (parseFloat(v) == 0) {
    return v;
  }
  if (!v) {
    return '';
  }
  if (!fixNum) {
    fixNum = 2;
  }
  if (fixNum === '0') {
    return v;
  }
  let suffix = '';
  switch (fixNum) {
    case 1:
      suffix = '0.0';
      break;
    case 2:
      suffix = '0.00';
      break;
    case 3:
      suffix = '0.000';
      break;
    case 4:
      suffix = '0.0000';
      break;
    default:
      suffix = '';
  }
  return v ? parseFloat(toFixed(v, fixNum)) : suffix;
}


//复制剪切板
export const handleCopy = (v) => {
  let oInput = document.createElement('input')
  oInput.value = v;
  document.body.appendChild(oInput);
  oInput.select();
  document.execCommand("Copy");
  message.success("复制成功");
  oInput.remove()
};


/**
 * 判断两个时间的间隔属于哪个区间
 * @param {Date|string|number} time1 时间1
 * @param {Date|string|number} time2 时间2
 * @returns {string} "10分钟内" | "1小时内" | "12小时内" | "1天内" | "3天内" | "1周内" | "超过一周"
 */
export const getTimeInterval = (time1, time2) => {
  // 统一转为 Date 对象
  const date1 = new Date(time1);
  const date2 = new Date(time2);

  // 计算时间差（绝对值，单位：毫秒）
  const diff = Math.abs(date1 - date2);

  // 定义时间区间阈值（单位：毫秒）
  const thresholds = [
    { label: "刚刚", value: 10 * 60 * 1000 },
    { label: "1小时内", value: 60 * 60 * 1000 },
    { label: "12小时内", value: 12 * 60 * 60 * 1000 },
    { label: "1天内", value: 24 * 60 * 60 * 1000 },
    { label: "3天内", value: 3 * 24 * 60 * 60 * 1000 },
    { label: "1周内", value: 7 * 24 * 60 * 60 * 1000 },
  ];

  // 遍历判断区间
  for (const { label, value } of thresholds) {
    if (diff <= value) return label;
  }
  return "超过一周";
}


//日期天数差
export const dateDifferenceInDays = (time1, time2) => {
  const date1 = new Date(time1);
  const date2 = new Date(time2);

  // 获取两个日期的毫秒数
  const millisecondsPerDay = 1000 * 60 * 60 * 24;
  const diffInMilliseconds = date2.getTime() - date1.getTime();
  // 计算天数差
  const diffInDays = Math.ceil(diffInMilliseconds / millisecondsPerDay);
  return diffInDays;
}
