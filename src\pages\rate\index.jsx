import ChatRate from '@/components/ChatRate';
import { MergeCellsOutlined, VerticalAlignTopOutlined } from '@ant-design/icons';
import { Badge, FloatButton } from 'antd';
import { useState,useEffect } from 'react';
import './index.less';
import Offline from './offline';
import Online from './online';
import { useNavigate } from 'react-router-dom';

import cs from '@/assets/cs.png';

export default () => {
  const tabs = ["线下运价", "线上运价"];
  const [activeKey, setActiveKey] = useState(0);
  const navigate = useNavigate();

  // 对比数量 <5  
  const [compareCount, setCompareCount] = useState(0);

  const [isChatOpen, setIsChatOpen] = useState(false);

  useEffect(() => {
    const num = localStorage.getItem('compareCount');
    console.log(num,'num')
    if (num) {
      setCompareCount(Number(num));
    }
  })

  return (
    <div className="za-content rate">
      <div className="rate-content">
        {/* <div className="rate-slogan">
          <div className="slogan-title">
            <Texty delay={1000}>实时运价查询</Texty>
          </div>
          <div className="slogan-sub-title">
            <Texty delay={2000} type="flash">支持10+船公司，16种箱型，一键实时查询，掌握真实运价。</Texty>
          </div>
        </div> */}
        <div style={{ height: '20px', width: '100%' }}></div>
        <div></div>
        <div className="rate-tabs">
          {tabs.map((item, index) => <div className={activeKey == index ? "tab-item active" : "tab-item unactive"} key={index} onClick={() => setActiveKey(index)}>
            <span> {item} </span>
          </div>)}
        </div>

        <Offline onCompare={(num) => setCompareCount(num)} style={{ display: activeKey === 0 ? 'block' : 'none' }} />
        <Online onCompare={(num) => setCompareCount(num)} style={{ display: activeKey === 1 ? 'block' : 'none' }} />
      </div>

      <FloatButton.Group
        shape="circle"
        style={{
          insetInlineEnd: 32,
          right: 5
        }}
      >
        <FloatButton.BackTop visibilityHeight={200} icon={<VerticalAlignTopOutlined style={{ color: '#b4141b' }} />} />
        <FloatButton icon={<img src={cs} style={{ width: '32px', marginLeft: '-7px' }} />} onClick={() => { setIsChatOpen(true) }} />
        {/* 对比 */}
        <Badge count={compareCount} size='small' offset={[-5, 5]}>
          <FloatButton id="basket" icon={<MergeCellsOutlined style={{ color: '#b4141b' }} />} onClick={() => { navigate('/rate/contrast'); }} />
        </Badge>
      </FloatButton.Group>

      <ChatRate
        open={isChatOpen}
        onCancel={() => setIsChatOpen(false)}
      />
    </div>
  );
};
