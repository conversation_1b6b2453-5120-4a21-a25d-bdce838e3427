import React, { useState, useEffect } from 'react';
import { Button, Descriptions, Form, Input, Space, Modal, Typography, Tooltip, message, Spin } from 'antd';
import { api, useInterval } from '@/common/utils';
import Company from '../components/Company';
import Menu from '../components/Menu';
import './index.less';
import objectAssign from 'object-assign';

export default () => {
  const [isEdit, setIsEdit] = useState(false);
  const [showMobile, setShowMobile] = useState(false);
  const [isBindWx, setBindWx] = useState(false);
  const [currentUser, setCurrentUser] = useState({});
  const [form] = Form.useForm();
  const [phoneForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);

  const [qrUrl, setQrUrl] = useState('');
  const [count, setCount] = useState(0);
  const [disabled, setDisabled] = useState(false);
  const [countNew, setCountNew] = useState(0);
  const [disabledNew, setDisabledNew] = useState(false);
  const [unionId, setUnionId] = useState('');
  const [isRunning, setIsRunning] = useState(false);


  useEffect(() => {
    getCurrentUser();
  }, []);

  const getCurrentUser = () => {
    api.user.getCurrentUser().subscribe({
      next: (data) => {
        setShowMobile(false);
        setIsEdit(false);
        setCurrentUser(data);
      }
    });
  };

  const items = [
    {
      key: '1',
      label: '绑定手机',
      children: <>{currentUser.userMobile} <span className='change' onClick={() => changeMobile()}>更换手机</span></>,
    },
    {
      key: '2',
      label: '绑定微信',
      children: <>{currentUser.openId ? '已经绑定' :
        <>尚未绑定<Tooltip title={<img src={qrUrl} style={{ width: '200px', height: '200px' }} />}
          color="#FFF" key="wx" trigger="click"><span className='binding' onClick={() => fetchQRTicket()}>立即绑定</span></Tooltip></>}</>,
    },

    {
      key: '9',
      label: ' ',
      labelStyle: { color: '#fff' },
      children: <Button type='default' onClick={() => {
        form.resetFields();
        setIsEdit(true);
      }}>修改密码</Button>
    },
  ]

  // 保存修改
  const saveInfo = (data) => {
    if (!data.oldPasswd) {
      message.warning('请填写原始密码!');
      return false;
    }
    if (!data.newPassword || !data.checkPassword) {
      message.warning('请填写新密码!');
      return false;
    }
    if (data.newPassword != data.checkPassword) {
      message.warning('新密码两次输入不一致!');
      return false;
    }
    let param = { id: currentUser.id, userPasswd: data.oldPasswd, newPassword: data.newPassword }
    setLoading(true);
    api.me.changePassword(param).subscribe({
      next: (data) => {
        message.success("操作成功!");
        setIsEdit(false);
        // getCurrentUser();
      }
    }).add(() => {
      setLoading(false);
    });
  }

  // 更换手机号
  const changeMobile = () => {
    phoneForm.setFieldsValue({ mobile: currentUser.userMobile });
    setShowMobile(true);
  }

  // 保存手机号
  const saveMobile = (data) => {
    if (!data.verifyCode) {
      message.warning('请填写原始验证码!');
      return false;
    }
    if (!data.newMobile) {
      message.warning('请填写新手机号!');
      return false;
    }
    if (!validatePhone(data.newMobile)) {
      message.warning('请填写正确的新手机号!');
      return false;
    }
    if (data.mobile === data.newMobile) {
      message.warning('新手机号不能与原始手机号相同!');
      return false;
    }
    if (!data.newVerifyCode) {
      message.warning('请填写新验证码!');
      return false;
    }
    objectAssign(data, { id: currentUser.id });
    setModalLoading(true);
    api.me.changeMobile(data).subscribe({
      next: (e) => {
        message.success("操作成功!");
        getCurrentUser();
      }
    }).add(() => {
      setModalLoading(false);
    });
  }

  const fetchQRTicket = () => {
    clearLoopQRResult();
    setLoading(true);
    api.user.fetchQRTicket().subscribe({
      next: (data) => {
        const t = data[0];
        setUnionId(t.scene);
        setQrUrl(t.qrUrl);
      },
    }).add(() => setLoading(false));
  };

  useEffect(() => {
    clearLoopQRResult();
    return () => {
      clearLoopQRResult();
    };
  }, []);

  useEffect(() => {
    if (unionId) {
      setIsRunning(true);
    }
    return () => {
      clearLoopQRResult();
    };
  }, [unionId]);

  const clearLoopQRResult = () => {
    setIsRunning(false);
  };

  useInterval(
    () => {
      fetchQRResult(unionId);
    },
    isRunning ? 1000 : null,
  );

  const fetchQRResult = (unionId) => {
    api.user.fetchQRResult(unionId).subscribe({
      next: (data) => {
        const result = data[0];
        if (!result) {
          return;
        }

        if (result.scene !== unionId) {
          return;
        }

        if (result.beScan) {
          // console.log('已扫码。。。。');
        }

        if (result.state === 'CREATED') {
          // console.log('已创建，待扫描。。。');
        }

        if (result.state === 'EXPIRED') {
          console.log('二维码已过期。。。重新生成。。。');
          clearLoopQRResult();
          fetchQRTicket();
          return;
        }

        if (result.state === 'NOT_BIND') {
          console.log('未绑定。。。请绑定');
          clearLoopQRResult();
          bindWx();
          return;
        }

        if (result.state === 'SUCCESS') {
          console.log('登陆成功。。。');
          clearLoopQRResult();
          return;
        }
      },
    });
  };

  const bindWx = () => {
    let param = { id: currentUser.id, unionId: unionId };
    setLoading(true);
    api.me.bindWx(param).subscribe({
      next: (e) => {
        message.success("操作成功!");
        getCurrentUser();
      }
    }).add(() => {
      setLoading(false);
    });
  }


  const getCaptcha = (v) => {
    setCount(30); // 设置倒计时秒数
    setDisabled(true);
    setModalLoading(true);
    api.user.sendSms(v).subscribe({
      next: (data) => {
        message.info('验证码已经发送至' + v + '，请注意查收');
      }
    }).add(() => {
      setModalLoading(false);
    });
  }
  const getCaptchaNew = () => {
    let formData = phoneForm.getFieldValue();
    let v = formData.newMobile;
    if (!v || !validatePhone(v)) {
      message.warning("请填写正确的新手机号!");
      return;
    }
    setCountNew(30); // 设置倒计时秒数
    setDisabledNew(true);
    setModalLoading(true);
    api.user.sendSms(v).subscribe({
      next: (data) => {
        message.info('验证码已经发送至' + v + '，请注意查收');
      }
    }).add(() => {
      setModalLoading(false);
    });
  }

  const validatePhone = (phoneNumber) => {
    const regex = /^1[3-9]\d{9}$/;
    return regex.test(phoneNumber);
  }


  useEffect(() => {
    if (count > 0) {
      const intervalId = setInterval(() => {
        setCount((prevCount) => prevCount - 1);
      }, 1000);
      return () => clearInterval(intervalId);
    } else {
      setDisabled(false);
    }
  }, [count]);

  useEffect(() => {
    if (countNew > 0) {
      const intervalId = setInterval(() => {
        setCountNew((prevCount) => prevCount - 1);
      }, 1000);
      return () => clearInterval(intervalId);
    } else {
      setDisabledNew(false);
    }
  }, [countNew]);


  return (<>

    <Spin spinning={loading}>

      <Menu selectKeys={["security"]} openKeys={[]}>
        <div className='securityContent'>

          <Company verified={true} />

          {isEdit ?

            <Form onFinish={saveInfo} form={form}>
              <div className='security-title'>
                修改密码
              </div>
              <Form.Item label="原始密码" name="oldPasswd" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                <Input.Password />
              </Form.Item>
              <Form.Item label="新密码" name="newPassword" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                <Input.Password />
              </Form.Item>
              <Form.Item label="确认密码" name="checkPassword" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                <Input.Password />
              </Form.Item>

              <Form.Item wrapperCol={{ offset: 3 }}>
                <Space>
                  <Button onClick={() => setIsEdit(false)}>
                    取消
                  </Button>
                  <Button type="primary" htmlType="submit">
                    保存
                  </Button>
                </Space>
              </Form.Item>
            </Form>
            :
            <Descriptions
              title="安全设置"
              column={1}
              items={items}
              style={{ margin: '30px 20px' }}
              labelStyle={{ width: '90px' }}
            />
          }
        </div>
      </Menu>
    </Spin>

    <Modal
      className="odm-modal"
      title="绑定手机"
      open={showMobile}
      width={500}
      centered
      footer={null}
      onCancel={() => setShowMobile(false)}
      modalRender={(modal) => (
        <Spin spinning={modalLoading} delay={10} size="large">
          {modal}
        </Spin>
      )}
    >
      <Form onFinish={saveMobile} form={phoneForm}>
        <Form.Item label="原始手机" name="mobile" labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
          <Input disabled />
        </Form.Item>
        <Form.Item label="原始验证码" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          <Space>
            <Form.Item name="verifyCode" noStyle>
              <Input />
            </Form.Item>
            <Typography.Link
              disabled={!validatePhone(phoneForm.getFieldValue().mobile) || disabled}
              onClick={() => getCaptcha(phoneForm.getFieldValue().mobile)}
            >
              {count > 0 ? `重新获取(${count})` : '获取验证码'}
            </Typography.Link>
          </Space>
        </Form.Item>

        <Form.Item label="新手机号" name="newMobile" labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
          <Input />
        </Form.Item>
        <Form.Item label="新验证码" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          <Space>
            <Form.Item name="newVerifyCode" noStyle>
              <Input />
            </Form.Item>
            <Typography.Link
              disabled={disabledNew}
              onClick={() => getCaptchaNew()}
            > {countNew > 0 ? `重新获取(${countNew})` : '获取验证码'}
            </Typography.Link>
          </Space>
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 6 }}>
          <Space>
            <Button onClick={() => setShowMobile(false)}>
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              保存
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  </>);
};
