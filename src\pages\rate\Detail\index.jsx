import React, { useState, useEffect } from 'react';
import { Tabs, Row, Col, Table, Radio, Modal, Descriptions, Tag } from 'antd';
import { dateFormat } from '@/common/utils';
import './index.less';
import * as R from 'ramda';
import { shipmentIcon } from '@/common/common';

export default (props) => {
    const { open, current, onCancel } = props;
    const [details, setDetails] = useState([]);
    const [containers, setContainers] = useState([]);
    const [boxInfo, setBoxInfo] = useState({});
    const [boxFees, setBoxFees] = useState([]);
    const [feeType, setFeeType] = useState('a');
    const [expandedRowKeys, setExpandedRowKeys] = useState([]);
    const [rateItems, setRateItems] = useState([]);

    useEffect(() => {
        setFeeType('a');
        setExpandedRowKeys([]);
        setDetails([]);
        setContainers([]);
        setRateItems([]);
        if (open) {
            setRateItems([
                { label: '起运港', children: current.polName, },
                { label: '中转直达', children: (current.ttOrDt === '中转' ? <span title={current.ttPol}>中转</span> : '直达'), },
                { label: 'ETD', children: dateFormat(current.etd), },
                { label: '目的港', children: current.podName, },
                { label: '航程', children: current.shipDays + ' 天', },
                { label: 'ETA', children: current.eta ? current.eta.substring(0, 10) : "" },

            ]);
            if (current.details && current.details.length > 0) {
                setDetails(current.details);
                let temp = [];
                R.forEach((v) => {
                    temp.push({
                        key: v.boxType,
                        label: <span className='ctnTitle'>{v.boxType}
                            {v.havePosition ? <Tag size="small" color="#87d068">充足</Tag> : <Tag size="small" color="#f50f50">爆仓</Tag>}</span>
                    });
                }, current.details);
                setContainers(temp);
                changeContainer(temp[0].key, current.details);
            }
        }
    }, [current?.id, open]);


    const changeContainer = (e, containers) => {
        setBoxInfo({});
        if (!containers || containers.length === 0) {
            return;
        }
        R.forEach((v) => {
            if (v.boxType === e) {
                setBoxInfo(v);
                changeFees(feeType, v);
                return;
            }
        }, containers);
    }
    const changeFees = (e, v) => {
        setFeeType(e)
        setBoxFees([]);
        let datas = [], keys = [];
        switch (e) {
            case 'a':
                datas = v.costList;
                break;
            case 'b':
                datas = v.demurrageList;
                break;
            case 'c':
                datas = v.kcList;
                break;
            case 'd':
                datas = v.qtList;
                break;
            default:
                break;
        }
        R.forEach((v) => {
            keys.push(v.key)
        }, datas);
        setBoxFees(datas);
        setExpandedRowKeys(keys);

    }

    const onExpand = (e, v) => {
        let temp = expandedRowKeys;
        if (e) {
            temp.push(v.key);
        } else {
            temp = temp.filter((e) => e != v.key);
        }
        setExpandedRowKeys(temp);
    }

    // const containers = [
    //     { key: 'a', label: <>20GP <Tag color="success" style={{ marginLeft: '5px' }}>充足</Tag></>, },
    //     { key: 'b', label: <>40GP <Tag color="processing" style={{ marginLeft: '5px' }}>单确</Tag></>, },
    //     { key: 'c', label: <>40HC <Tag color="warning" style={{ marginLeft: '5px' }}>紧缺</Tag></>, },
    //     { key: 'd', label: <>45HC <Tag color="error" style={{ marginLeft: '5px' }}>爆仓</Tag></>, },
    // ]

    const columns = [
        {
            title: '费用类型',
            dataIndex: 'costCategory',
        },
        {
            title: '单位',
            dataIndex: 'unit',
        },
        {
            title: '币别',
            dataIndex: 'currency',
        },
        {
            title: '价格',
            dataIndex: 'price',
        },
    ]

    const columnDs = [
        {
            title: '费用类型',
            dataIndex: 'costCategory',
        },
        {
            title: '天数',
            dataIndex: 'days',
            width: '80px',
        },
        {
            title: '单位',
            dataIndex: 'unit',
        },
        {
            title: '币别',
            dataIndex: 'currency',
        },
        {
            title: '价格',
            dataIndex: 'price',
        },
    ]


    return (<Modal
        title="运价详情"
        width={1200}
        open={props.open}
        onCancel={props.onCancel}
        maskClosable={false}
        footer={null}
    >
        <Row>
            <Col span={12} style={{ display: 'flex', alignItems: 'center' }}>
                <img src={shipmentIcon(current.shipCompany)} style={{ width: '48px', height: '48px', border: '1px solid #f0f0f0' }} />
                <div style={{ marginLeft: '20px', lineHeight: '48px', fontSize: '16px' }}>{current.shipCompany}</div>
            </Col>
            <Col span={12}>
                <p>船名航次：{current.vessel} / {current.voyage}</p>
                <p>运价有效期：{current.runDate} 到 {current.expireDate}</p>
            </Col>
        </Row>

        <Descriptions items={rateItems} style={{ marginBottom: '20px' }} />

        <Tabs
            type="card"
            items={containers}
            onChange={(e) => changeContainer(e, details)}
        >
        </Tabs>
        <div style={{ border: '1px solid #f0f0f0', marginTop: '-17px', height: 'calc(100vh - 450px)' }}>
            <Row style={{ margin: '10px' }}>
                <Col span={12}>
                    <span className='rate-title'>Total：</span>
                    <span className='rate-value'>{boxInfo.currency} {boxInfo.money}</span>
                    <span className='rate-title'> Others：</span>
                    <span className='rate-value'>{boxInfo.others}</span>
                </Col>
                <Col span={12} style={{ textAlign: 'right' }}>
                    <Radio.Group onChange={(e) => changeFees(e.target.value, boxInfo)} value={feeType}>
                        <Radio.Button value="a"> Total </Radio.Button>
                        <Radio.Button value="b"> D&D </Radio.Button>
                        <Radio.Button value="c"> 亏仓费 </Radio.Button>
                        <Radio.Button value="d"> 其他 </Radio.Button>
                    </Radio.Group>
                </Col>
            </Row>
            <Table
                size='small'
                columns={feeType === "b" ? columnDs : columns}
                dataSource={boxFees}
                pagination={false}
                expandable={{ expandedRowKeys: expandedRowKeys, onExpand: onExpand }}
                scroll={{ y: 280 }}
            />
        </div>
    </Modal>);

}