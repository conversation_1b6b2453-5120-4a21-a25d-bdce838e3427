import React, { useRef, useState, useEffect } from 'react';
import { Button, Form, Select, Space, Divider, Table, message, ConfigProvider } from 'antd';
import { RedoOutlined, DownOutlined, RightOutlined, AppstoreOutlined, BarsOutlined, } from '@ant-design/icons';
import Texty from 'rc-texty';
import AnimDate from '@/components/AnimDate';
import AnimPorts from '@/components/AnimPorts';
import ShipCompany from '@/components/ShipCompany';
import './index.less';
import { api, copyObject, dateFormat, constant } from '@/common/utils';
import { shipmentIcon } from '@/common/common';
import ShipSchedule from '@/components/ShipSchedule';
import NoData from '@/components/NoData';

export default () => {

  const refPol = useRef();
  const refPod = useRef();
  const [listType, setListType] = useState('card');
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [searchForm] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [listData, setListData] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [originalDatas, setOriginalDatas] = useState([]);

  const current = JSON.parse(sessionStorage.getItem(constant.KEY_CURRENT_USER));
  const schedule_history_key = current?.id + "_history_schedule";
  const [scheduleHistorys, setScheduleHistorys] = useState([]);
  const [pol, setPol] = useState('');
  const [pod, setPod] = useState('');

  const shipSchedulesSelect = [{ key: 'SUN', value: '周日' }, { key: 'MON', value: "周一" }, { key: 'TUE', value: "周二" },
  { key: 'WED', value: "周三" }, { key: 'THU', value: "周四" }, { key: 'FRI', value: "周五" }, { key: 'SAT', value: "周六" }];
  const scheduleMap = {
    'MON': '周一', 'TUE': '周二', 'WED': '周三', 'THU': '周四', 'FRI': '周五', 'SAT': '周六', 'SUN': '周日'
  }

  const scheduleColumns = [
    { title: '船公司', dataIndex: 'shipCompany', width: '183px' },
    { title: 'ETD', dataIndex: 'etd', width: '137px' },
    { title: '班期', dataIndex: 'etdWeek', width: '117px', render: (text, record) => <>{scheduleMap[text]}</> },
    { title: '航程', dataIndex: 'shipDays', width: '89px', render: (text, record) => <>{text}天</> },
    { title: '码头', dataIndex: 'polTerminal', width: '131px' },
    { title: '船名/航次', dataIndex: 'vessel', width: '345px', render: (text, record) => <>{text} / {record.voyage}</> },
    { title: '中转直达', dataIndex: 'ttOrDt', render: (text, record) => <>{text === 'tt' ? ' 中转' : '直达'}</> },

  ]

  //设置查询历史
  useEffect(() => {
    let storedString = localStorage.getItem(schedule_history_key);
    let items = storedString ? JSON.parse(storedString) : [];
    setScheduleHistorys(items);
  }, []);

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const onSearch = () => {
    let searchData = searchForm.getFieldValue();
    if (!searchData.pol) {
      message.error("请选择起运港!");
      return;
    }
    if (!searchData.pod) {
      message.error("请选择目的港!");
      return;
    }
    if (!searchData.etd) {
      message.error("请选择船期!");
      return;
    }
    // copyObject(searchData, { dateStart: dateFormat(searchData.etd, 'yyyy-MM-dd') });
    setSearchLoading(true);
    api.freightPrice.listShipSchedules(searchData).subscribe({
      next: (data) => {
        setOriginalDatas(data);
        changeListData(data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
    //设置查询历史
    setSearchHis();
  };

  const setSearchHis = () => {
    if (pol && pod) {
      let newItem = {
        pol: pol.portEdi,
        pod: pod.portEdi,
        polName: pol.enName + "," + pol.countryCode + ' ' + pol.cnName + " | " + pol.countryName,
        podName: pod.enName + "," + pod.countryCode + ' ' + pod.cnName + " | " + pod.countryName,
        polEnName: pol.enName,
        polCnName: pol.cnName,
        podEnName: pod.enName,
        podCnName: pod.cnName,
          polCountryCode:pol.countryCode,
          polCountryName:pol.countryName,
          podCountryCode:pod.countryCode,
          podCountryName:pod.countryName,
        id: pol.enName + "," + pol.countryCode + ' - ' + pod.enName + "," + pod.countryCode,
      }
      updateLocalStorageArray(schedule_history_key, newItem);
    }
  }

  function updateLocalStorageArray(key, newItem) {
    let storedString = localStorage.getItem(key);
    let items = storedString ? JSON.parse(storedString) : [];
    for (let i = 0; i < items.length; i++) {
      if (items[i].id === newItem.id) {
        //存在 则删除
        items.splice(i, 1);
        break;
      }
    }
    //插入
    items.unshift(newItem);
    //保留3个
    if (items.length > 3) {
      items.pop();
    }
    setScheduleHistorys(items);
    let updatedString = JSON.stringify(items);
    //设置缓存
    localStorage.setItem(key, updatedString);
  }

  //修改数据
  const changeListData = (datas) => {
    let newdata = datas, filterData = filterForm.getFieldValue();
    //过滤船司
    if (filterData.shipCompanys && filterData.shipCompanys.length != 0) {
      newdata = newdata.filter((e) => filterData.shipCompanys.indexOf(e.shipCompany) != -1);
    }
    //过滤直达中转
    if (filterData.ttOrDt && filterData.ttOrDt.length != 0) {
      newdata = newdata.filter((e) => filterData.ttOrDt.indexOf(e.ttOrDt) != -1);
    }
    //过滤班期
    if (filterData.shipSchedules && filterData.shipSchedules.length != 0) {
      newdata = newdata.filter((e) => filterData.shipSchedules.indexOf(e.etdWeek) != -1);
    }
    //过滤航程
    if (filterData.shipDays && filterData.shipDays.length >= 2) {
      newdata = newdata.filter((e) => e.shipDays >= filterData.shipDays[0] && e.shipDays <= filterData.shipDays[1]);
    }
    setListData(newdata);
  }

  return (
    <div className="za-content schedule">
      <div className="schedule-content">
        <div className="schedule-slogan">
          <div className="slogan-title">
            <Texty delay={1000}>实时船期</Texty>
          </div>
          <div className="slogan-sub-title">
            <Texty delay={2000} type="flash">支持30+船公司, 实时船期查询。</Texty>
          </div>
        </div>

        <div className="schedule-search">
          <Form
            form={searchForm}
            name="searchRef"
            onFinish={() => onSearch()}
          >
            <div className='search-default'>
              <div className="search-ports">
                <div className='search-pol'>
                  <Form.Item name="pol" >
                    <AnimPorts label="起运港" getSelectItem={(e) => setPol(e)} childRef={refPol} />
                  </Form.Item>
                </div>
              </div>
              <div className="search-ports" style={{ margin: '0 0 0 15px' }}>
                <div className='search-pod'>
                  <Form.Item name="pod" >
                    <AnimPorts label="目的港" getSelectItem={(e) => setPod(e)} childRef={refPod} />
                  </Form.Item>
                </div>
              </div>
              <div className="search-etd">
                <Form.Item name="etd" >
                  <AnimDate label="船期" />
                </Form.Item>
              </div>
              <Button htmlType="submit" className="search-btn" loading={searchLoading}>查 询</Button>
            </div>

            <div className='search-tool'>

              <div className="schedule-history">
                {scheduleHistorys.map((r) => <span key={r.id} onClick={() => {
                  setPol({ enName: r.polEnName, cnName: r.polCnName, portEdi: r.pol, countryCode: r.polCountryCode, countryName: r.polCountryName });
                  setPod({ enName: r.podEnName, cnName: r.podCnName, portEdi: r.pod, countryCode: r.podCountryCode, countryName: r.podCountryName });
                  refPol.current?.setValue({ portName: r.polName });
                  refPod.current?.setValue({ portName: r.podName });
                  searchForm.setFieldsValue({ pol: r.pol, pod: r.pod });
                  console.info(r)
                }}>
                  {r.id}
                </span>
                )}
                <span style={{ display: scheduleHistorys.length > 0 ? '' : 'none' }} onClick={() => {
                  localStorage.removeItem(schedule_history_key);
                  setScheduleHistorys([]);
                }}>清空记录</span>
              </div>

              <div className="tool-btn">
                <Button type="text" icon={<RedoOutlined />} size="small" htmlType="reset">
                  重置
                </Button>
              </div>
            </div>
          </Form>
          <Divider style={{ margin: '20px 0' }} />
          <div className='schedule-list'>
            <div className="schedule-tools">
              <div className="schedule-filter">
                <div className="rate-filter">
                  <Form layout="inline" form={filterForm} onFieldsChange={() => changeListData(originalDatas)}>
                    <Form.Item name='shipCompanys'>
                      <ShipCompany mode='multiple' beOnline={true} width='200px' placeholder="请选择船公司" />
                    </Form.Item>
                    <Form.Item name='ttOrDt'><Select placeholder="中转/直达" options={[{ label: '中转', value: 'tt' }, { label: '直达', value: 'dt' }]} mode='multiple' allowClear style={{ width: '155px' }}></Select></Form.Item>
                    <Form.Item name='shipSchedules'>
                      <Select mode='multiple' allowClear maxTagCount={1} placeholder='班期' style={{ width: '155px' }}>
                        {shipSchedulesSelect.map((item) => {
                          return <Option key={item.key} value={item.key}>{item.value}</Option>
                        })}
                      </Select>
                    </Form.Item>
                    <Form.Item name='shipDays' label=''><ShipSchedule placeholder='请筛选航程' style={{ width: '100px' }} /></Form.Item>
                  </Form>
                </div>
              </div>
              <div className="schedule-tools-btn">
                <Space>
                  <Button type="text" icon={<BarsOutlined style={listType == 'table' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('table')}></Button>
                  <Button type="text" icon={<AppstoreOutlined style={listType == 'card' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('card')}></Button>
                </Space>
              </div>
            </div>
            {listType == 'table' ?
              <ConfigProvider renderEmpty={() => <NoData />}>
                <Table
                  className='schedule-table'
                  rowKey='id'
                  pagination={false}
                  style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
                  // rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
                  columns={scheduleColumns}
                  dataSource={listData}
                  expandable={{
                    expandedRowRender: (record) => (
                      <div className='schedule-expand'>
                        <p><span>航线代码：</span>{record.lineCode}<span>ETA：</span>{record.eta?.substring(0, 10)}<span >共舱情况：</span>{record.shareCabins || '-'}
                        </p>
                      </div>
                    ),
                    expandIcon: ({ expanded, onExpand, record }) =>
                      expanded ?
                        (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                        :
                        (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                  }}
                />
              </ConfigProvider>
              :
              <>
                <div className='card-header'>
                  <div className='header-item' style={{ width: '208px', border: 0, textAlign: 'left' }}>
                    <span style={{ marginLeft: '16px' }}>船公司</span>
                  </div>
                  <div className='header-item' style={{ width: '151px' }}>ETD</div>
                  <div className='header-item' style={{ width: '98px' }}>班期</div>
                  <div className='header-item' style={{ width: '101px' }}>航程</div>
                  <div className='header-item' style={{ width: '111px' }}>码头</div>
                  <div className='header-item' style={{ width: '365px' }}>船名/航次</div>
                  <div className='header-item' style={{ width: '118px' }}>中转直达</div>
                </div>

                {listData.map((r) => <div className='card-list'>
                  <div className='card-item-top'>
                    <div className='card-item-logo'>
                      <img src={shipmentIcon(r.shipCompany)} />
                    </div>
                    <div className='card-item-shipCompany'>
                      <p>{r.shipCompany}</p>
                    </div>
                    <div className='card-item-etd'>
                      <p>{r.etd}</p>
                    </div>
                    <div className='card-item-shipSchedule'>
                      <p>{scheduleMap[r.etdWeek]}</p>
                    </div>
                    <div className='card-item-shipDays'>
                      <p>{r.shipDays}天</p>
                    </div>
                    <div className='card-item-polTerminal'>
                      <p>{r.polTerminal}</p>
                    </div>
                    <div className='card-item-vessel'>
                      <p>{r.vessel} / {r.voyage} </p>
                    </div>
                    <div className='card-item-ttOrDt'>
                      <p>{r.ttOrDt == 'tt' ? '中转' : '直达'} </p>
                    </div>
                  </div>
                  <div className='card-item-bottom'>
                    <p><span>航线代码：</span>{r.lineCode}<span>ETA：</span>{r.eta?.substring(0, 10)}<span >共舱情况：</span>{r.shareCabins || '-'}
                    </p>
                    {/* {expandCards[r.id] ?
                      <>
                        <p><span>下单时间：</span>{dateFormat(r.createTime, 'yyyy-MM-dd hh:mm')}</p>
                        <Button type="text" icon={<UpOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>收起</Button>
                      </>
                      :
                      <Button type="text" icon={<DownOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>展开</Button>} */}
                  </div>
                </div>)}
                {listData.length == 0 && <NoData />}
              </>}
          </div>
        </div>
      </div>
    </div>
  );
};
