import { Row, Col, Dropdown, Input, Slider, Button } from 'antd';
import { useState } from 'react';

export default (props) => {
    const [value,setValue] = useState([20, 50]);

    const marks = {
        1: '1天',
        90: '90天'
    }

    const changeRange = (v) => {
        setValue(v);
    }

    const slider = () => {
        return <div style={{ height: '100px', width: '240px', }}>
            <Row style={{ background: '#fff', padding: '10px 30px', borderRadius: '4px', boxShadow: '2px 2px 2px #ccc' }}>
                <Col span={24}>
                    <Slider
                        range={{ draggableTrack: true, }}
                        defaultValue={value}
                        marks={marks}
                        step={5}
                        onChange={changeRange}
                    />
                </Col>
                <Col span={24} style={{ textAlign: 'right' }}>
                    <Button type="primary" size='small' onClick={() => props.onChange(value)}> 确定 </Button>
                </Col>
            </Row>

        </div>
    }

    return (
        <Dropdown
            placement="bottom"
            arrow
            dropdownRender={slider}
            trigger="click"
        >
            <Input {...props} readOnly />
        </Dropdown>
    );
}
