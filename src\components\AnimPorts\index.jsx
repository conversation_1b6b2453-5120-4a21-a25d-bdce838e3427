import React, { useRef, useEffect, useState, useImperativeHandle } from 'react';
import { Select } from 'antd';
import { api, constant } from '@/common/utils';
import debounce from 'lodash/debounce';
import './index.less';
import * as R from 'ramda';

export default (props) => {

    const [value, setValue] = useState();
    const delayedQuery = useRef(debounce((value) => searchData(value), 300)).current;
    const [points, setPoints] = useState([]);


    useEffect(() => {
        if (props.label && props.label === '起运港') {
            setPoints(constant.INIT_POLS);
        } else {
            searchData('');
        }
    }, [props.label]);

    const searchData = (keywords) => {
        api.base.listPort(keywords).subscribe({
            next: (body) => {
                let data = [];
                data = body.map((item) => ({
                    label: item.enName + "," + item.countryCode + ' ' + item.cnName + " | " + item.countryName,
                    value: item.portEdi,
                    item: item,
                }));
                setPoints(data);
            },
        });
    };

    const handleChange = (selectItem) => {
        setValue();
        props.onChange(selectItem?.value || '');
        if (props.getSelectItem) {
            R.forEach((v) => {
                if (v.value === selectItem?.value) {
                    props.getSelectItem(v.item);
                    return;
                }
            }, points);
        }
    };

    useImperativeHandle(props.childRef, () => ({
        setValue: (value) => {
            setValue(value.portName);
        },
    }));


    return (
        <div className="anim-port">
            <Select {...props}
                value={value}
                allowClear style={{ width: '100%', border: 'none' }}
                showSearch
                labelInValue
                filterOption={false}
                onClear={() => { handleChange(); searchData(''); }}
                onChange={(selectItem) => { handleChange(selectItem) }}
                onSearch={(v) => {
                    delayedQuery(v);
                }}
            >
                {points && points.map((item) => (
                    <Select.Option key={item.value}>{item.label}</Select.Option>
                ))}
            </Select>
            <label className={(props.labelAlgin == 'right') ? 'right' : 'left'}>{props.label}</label>
        </div>
    );
}
