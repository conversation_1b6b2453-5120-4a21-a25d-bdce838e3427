import { Input, InputNumber } from 'antd';
import '../AnimPorts/index.less';

export default (props) => {


    return (
        <div className="anim-port">
            <InputNumber {...props} style={{ width: '100%', border: 'none' }} max={props.max || 28260} min={props.min || 1} controls={false}/>
            <label className={(props.labelAlgin == 'right') ? 'right' : 'left'}>{props.label}</label>
        </div>
    );
}
