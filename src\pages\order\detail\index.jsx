import React, { useState, useEffect } from "react";
import { Modal, Tabs, Descriptions, Table } from 'antd';
import BaseInfo from "./baseInfo";
import FeeInfo from "./feeInfo";
import AmendInfo from "./amendInfo";
import DocInfo from "./docInfo";
import { api, copyObject, dateFormat, constant } from '@/common/utils';


export default (props) => {
    const { open, orderId, onCancel } = props;

    const [activeKey, setActiveKey] = useState("1");
    const [orderInfo, setOrderInfo] = useState({});


    const items = [
        { key: '1', label: `提单信息` },
        { key: '2', label: `费用信息` },
        { key: '3', label: `更 改 单` },
        // { key: '4', label: `票据单证` }
    ]


    useEffect(() => {
        setActiveKey('1');
        if (open) {
            getOrderInfo(orderId);
        }
    }, [open, orderId]);

    const onChange = (key) => {
        setActiveKey(key)
    };

    const getOrderInfo = (id) => {
        api.order.getOrderInfo(id).subscribe({
            next: (data) => {
                setOrderInfo(data);
            }
        }).add(() => {

        });
    };




    const tableContent = [<BaseInfo activeKey={activeKey} orderId={orderId} orderInfo={orderInfo} />, <FeeInfo activeKey={activeKey} orderId={orderId} />,
    <AmendInfo activeKey={activeKey} orderId={orderId} />, <DocInfo activeKey={activeKey} orderId={orderId} />]

    return (
        <Modal
            title={<Tabs activeKey={activeKey} items={items} onChange={onChange} size="small" />}
            centered
            open={open}
            width={1160}
            footer={null}
            onCancel={() => onCancel()}
            maskClosable={false}
            styles={{
                body: {
                    padding: '0',
                    height: 'calc(100vh - 200px)',
                    overflow: 'auto'
                }
            }}
        >
            {tableContent[activeKey - 1]}
        </Modal>
    )
}