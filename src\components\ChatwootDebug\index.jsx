import React from 'react';
import { Button, Space, message } from 'antd';
import { setChatwootUser, resetChatwoot, getCurrentUser } from '@/common/chatwootUtils';

const ChatwootDebug = () => {
  const handleSetUser = () => {
    const user = getCurrentUser();
    if (user) {
      setChatwootUser(user);
      message.success('用户信息已更新到Chatwoot');
    } else {
      message.warning('当前没有登录用户');
    }
  };

  const handleReset = () => {
    resetChatwoot();
    message.success('Chatwoot已重置');
  };

  const handleCheckStatus = () => {
    console.log('=== Chatwoot Status ===');
    console.log('window.chatwootSDK:', window.chatwootSDK);
    console.log('window.$chatwoot:', window.$chatwoot);
    console.log('Current user:', getCurrentUser());
    
    if (window.$chatwoot) {
      console.log('Chatwoot hasLoaded:', window.$chatwoot.hasLoaded);
      console.log('Chatwoot isOpen:', window.$chatwoot.isOpen);
    }
    
    message.info('状态信息已输出到控制台');
  };

  const handleOpenChat = () => {
    if (window.$chatwoot) {
      window.$chatwoot.toggle('open');
    } else {
      message.error('Chatwoot未初始化');
    }
  };

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      left: '10px', 
      zIndex: 9999,
      background: 'white',
      padding: '10px',
      border: '1px solid #ccc',
      borderRadius: '4px'
    }}>
      <div style={{ marginBottom: '8px', fontSize: '12px', fontWeight: 'bold' }}>
        Chatwoot Debug
      </div>
      <Space direction="vertical" size="small">
        <Button size="small" onClick={handleSetUser}>
          设置用户信息
        </Button>
        <Button size="small" onClick={handleReset}>
          重置Chatwoot
        </Button>
        <Button size="small" onClick={handleCheckStatus}>
          检查状态
        </Button>
        <Button size="small" onClick={handleOpenChat}>
          打开聊天
        </Button>
      </Space>
    </div>
  );
};

export default ChatwootDebug;
