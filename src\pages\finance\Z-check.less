// 订单
.bill {
    background: url('@/assets/rate-bg.png');
    background-repeat: no-repeat;
    background-position: top center;

    .bill-content {
        width: 1200px;
        margin: 0 auto;

        .bill-slogan {
            height: 190px;
            overflow: hidden;

            .slogan-title {
                font-weight: bold;
                font-size: 38px;
                color: #482828;
                line-height: 53px;
                margin-top: 50px;
            }

            .slogan-sub-title {
                font-weight: 400;
                font-size: 20px;
                color: #482828;
                line-height: 28px;
                margin-top: 14px;
            }
        }

        .bill-search {
            width: 1160px;
            min-height: 100px;
            margin: 24px auto;
            background: #fff;
            border-radius: 10px 10px;
            box-shadow: 0px 0px 20px #ddd;
            padding: 20px;

            .search-default {
                margin-bottom: 16px;

                .search-code {
                    width: 422px;
                    height: 62px;
                    margin-right: 16px;
                    border: 1px solid #D9D9D9;
                    border-radius: 4px;
                }

                .search-checkUserName {
                    width: 276px;
                    height: 62px;
                    border: 1px solid #D9D9D9;
                    border-radius: 4px;
                }

                .search-atd {
                    width: 274px;
                    height: 62px;
                    margin-right: 16px;
                    border: 1px solid #D9D9D9;
                    border-radius: 4px;
                }

                .search-billDate {
                    width: 276px;
                    height: 62px;
                    margin-right: 16px;
                    border: 1px solid #D9D9D9;
                    border-radius: 4px;
                }


                .search-btn {
                    width: 278px;
                    height: 64px;
                    background: rgba(180, 20, 27, 1);
                    color: #fff;
                    border-radius: 4px;
                    font-size: 22px;
                    line-height: 64px;
                    text-align: center;
                    user-select: none;
                }

                .search-btn:hover {
                    cursor: pointer;
                    background: rgba(180, 20, 27, 0.9);
                }
            }

            .search-tool {
                display: flex;
                justify-content: flex-end;
                margin: 0;

                .tool-btn {
                    width: 150px;
                    display: flex;
                    justify-content: end;

                }

            }
        }

        .bill-list {


            .bill-expand {
                padding: 0 40px;

                p {
                    margin: 0;
                    line-height: 32px;

                    span {
                        color: #999;
                        margin-left: 30px;
                    }
                }
            }

            .bill-table {
                .ant-table-thead {
                    position: sticky;
                    top: 56px;
                    z-index: 99;
                }

            }

            .bill-alert {
                height: 46px;
                background: #FAFAFA;
                border-radius: 6px;
                margin-top: 16px;
                padding: 0 24px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: #999999;
            }
        }

        .bill-pagination {
            display: flex;
            justify-content: flex-end;
            margin: 20px 0 0 0;
        }

    }
}