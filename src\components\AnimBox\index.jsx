import { Select } from 'antd';
import './index.less';

export default (props) => {

    const ctnOptions = [
        { value: '20GP', label: '20GP' },
        { value: '40GP', label: '40GP' },
        { value: '40HC', label: '40HC' },
        // { value: '45HC', label: '45HC' },
    ]

    return (
        <div className="anim-box">
            <Select {...props} options={ctnOptions} mode='multiple' maxTagCount={4} maxTagTextLength={4} allowClear style={{ width: '100%', border: 'none', paddingLeft: '8px' }} />
            <label>{props.label}</label>
        </div>
    );
}
