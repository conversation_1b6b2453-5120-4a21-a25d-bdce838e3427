import React, { useState } from 'react';
import { Button, Modal } from 'antd';
import { MessageOutlined, CloseOutlined } from '@ant-design/icons';
import './index.less';

const SimpleChatButton = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const openChat = () => {
    console.log('Opening chat modal');
    setIsModalVisible(true);
  };

  const closeChat = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <div className="simple-chat-button-container">
        <Button 
          type="primary" 
          shape="circle" 
          size="large"
          icon={<MessageOutlined />}
          onClick={openChat}
          className="simple-chat-button"
          title="在线客服"
        />
      </div>

      <Modal
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>在线客服</span>
            <Button 
              type="text" 
              icon={<CloseOutlined />} 
              onClick={closeChat}
              size="small"
            />
          </div>
        }
        open={isModalVisible}
        onCancel={closeChat}
        footer={null}
        width={400}
        height={600}
        styles={{
          body: { 
            padding: 0, 
            height: '500px',
            overflow: 'hidden'
          }
        }}
        destroyOnClose={true}
      >
        <iframe
          src="http://192.168.1.58:3000/widget?website_token=KNGYN9tZffVKpCVV6BCAT86e"
          width="100%"
          height="500px"
          frameBorder="0"
          style={{ border: 'none' }}
          title="Chatwoot Chat"
        />
      </Modal>
    </>
  );
};

export default SimpleChatButton;
