.simple-chat-button-container {
  position: fixed;
  bottom: 80px; /* 稍微高一点，避免与原来的按钮重叠 */
  right: 20px;
  z-index: 1000;
}

.simple-chat-button {
  width: 60px;
  height: 60px;
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
  }

  .anticon {
    font-size: 24px;
  }
}
