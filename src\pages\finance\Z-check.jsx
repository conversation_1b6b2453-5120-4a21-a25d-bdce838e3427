import React, { useState } from 'react';
import { Button, Row, Form, Space, Divider, Table, Pagination, Spin, message } from 'antd';
import { RedoOutlined, DownOutlined, RightOutlined, } from '@ant-design/icons';
import Texty from 'rc-texty';
import AnimDate from '@/components/AnimDate';
import AnimInput from '@/components/AnimInput';
import './index.less';
import { api, dateFormat, formatMoney } from '@/common/utils';

export default () => {

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchForm] = Form.useForm();
  const [datas, setDatas] = useState([]);
  const [loading, setLoading] = useState(false);


  const columns = [
    { title: '对账编号', dataIndex: 'code', width: '137' },
    { title: '客户名称', dataIndex: 'customerName', width: '137' },
    { title: '对账日期', dataIndex: 'billDate', width: '107', render: (text, record) => <>{dateFormat(text)}</> },
    { title: '对账人', dataIndex: 'checkUserName', width: '105' },
    { title: '应付RMB', dataIndex: 'outRmb', width: '111', render: (text, record) => <>{formatMoney(text)}</> },
    { title: '应付USD', dataIndex: 'outUsd', width: '111', render: (text, record) => <>{formatMoney(text)}</> },
    { title: '未付RMB', dataIndex: 'unOutRmb', width: '111', render: (text, record) => <>{formatMoney(text)}</> },
    { title: '未付USD', dataIndex: 'unOutUsd', width: '111', render: (text, record) => <>{formatMoney(text)}</> },
    {
      title: '操作', dataIndex: 'option', width: '160px', align: 'center', render: (text, record) =>
         <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', color: '#B4141B' }}>
         <div onClick={() => exportBill(record)}>导出账单</div>
         <div onClick={() => invoiceApply(record.id)}>发票申请</div>
       </div>
    },
  ]

  const onSelectChange = (selectedKeys, selectedRows) => {
    setSelectedRowKeys(selectedKeys);
  };

  const onSearch = (pageNo, pageSize) => {
    setPageNo(pageNo);
    setPageSize(pageSize);
    setSearchLoading(true);
    let searchData = searchForm.getFieldValue();
    let param = { dto: searchData, pageNo: pageNo, pageSize: pageSize };
    api.checkBill.searchBill(param).subscribe({
      next: (data) => {
        setTotal(data.total);
        setDatas(data.data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };

  const onShowSizeChange = (current, pageSize) => {
    onSearch(current, pageSize);
  };

  const exportBill = (record) => {
    let fileName = "对账-" + record.code + ".xlsx";
    doExportBill([record.id], fileName);
  }

  const exportBills = () => {
    if (selectedRowKeys.length === 0) {
      message.error("请至少选择一条数据!");
      return;
    }
    let fileName = "对账-" + dateFormat(Date.now(), 'yyyy-MM-dd hh:mm') + ".xlsx";
    doExportBill(selectedRowKeys, fileName);
  }

  const doExportBill = (selectedRowKeys, fileName) => {
    setLoading(true);
    api.checkBill.exportBills({ ids: selectedRowKeys }).subscribe({
      next: (res) => {
        const blob = new Blob([res], {
          type: 'application/octet-stream',
        });
        if (window.navigator.msSaveOrOpenBlob) {
          navigator.msSaveBlob(blob, fileName);
        } else {
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
          // 释放内存
          window.URL.revokeObjectURL(link.href);
        }
      }
    }).add(() => {
      setLoading(false);
    });
  };


  const invoiceApply = () => {
    onSearch(current, pageSize);
  };

  return (
    <Spin spinning={loading}>
      <div className="za-content bill">
        <div className="bill-content">
          <div className="bill-slogan">
            <div className="slogan-title">
              <Texty delay={1000}>财务管理</Texty>
            </div>
            <div className="slogan-sub-title">
              <Texty delay={2000} type="flash">支持10+船公司，16种箱型，一键实时查询，掌握真实运价。</Texty>
            </div>
          </div>

          <div className="bill-search">
            <Form
              form={searchForm}
              name="searchRef"
              onFinish={() => onSearch(1, pageSize)}
            >
              <div className='search-default'>
                <Row>
                  <div className='search-code'>
                    <Form.Item name="code" >
                      <AnimInput label="对账编号" />
                    </Form.Item>
                  </div>
                  <div className='search-code'>
                    <Form.Item name="blNo" >
                      <AnimInput label="提单号" />
                    </Form.Item>
                  </div>
                  <div className="search-checkUserName">
                    <Form.Item name="checkUserName" >
                      <AnimInput label="对账人(待定)" />
                    </Form.Item>
                  </div>
                </Row>
                <Row style={{ marginTop: '15px' }}>
                  <div className="search-atd">
                    <Form.Item name="orderCode" >
                      <AnimInput label="业务编号" />
                    </Form.Item>
                  </div>
                  <div className="search-billDate">
                    <Form.Item name="billDateStart" >
                      <AnimDate label="从对账日" />
                    </Form.Item>
                  </div>
                  <div className="search-billDate">
                    <Form.Item name="billDateEnd" >
                      <AnimDate label="到对账日" />
                    </Form.Item>
                  </div>
                  <Button htmlType="submit" className="search-btn" loading={searchLoading}>查 询</Button>
                </Row>
              </div>

              <div className='search-tool'>
                <div className="tool-btn">
                  <Button type="text" icon={<RedoOutlined />} size="small" htmlType="reset">
                    重置
                  </Button>
                </div>
              </div>
            </Form>
            <Divider style={{ margin: '16px 0' }} />
            <div className='bill-list'>
              <div className="bill-tools">
                <Space>
                  <Button type='primary' onClick={() => exportBills()}>导出账单</Button>
                  <Button type='primary' style={{ marginLeft: '10px' }} >发票申请</Button>
                </Space>

              </div>

              {selectedRowKeys.length > 0 && <div className='bill-alert'>
                <span> {'已选择 ' + selectedRowKeys.length + ' 项'}</span>
                <span style={{ color: '#B4141B' }} onClick={() => setSelectedRowKeys([])}>取消选择</span></div>}
              <Table
                className='bill-table'
                rowKey='id'
                pagination={false}
                style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
                rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
                columns={columns}
                dataSource={datas}
                expandable={{
                  expandedRowRender: (record) => (
                    <div className='bill-expand'>
                      <p><span>备注：</span>{record.note} </p>
                    </div>
                  ),
                  expandIcon: ({ expanded, onExpand, record }) =>
                    expanded ?
                      (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                      :
                      (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                }}
              />
            </div>
            <div className='bill-pagination'>
              <Pagination
                pageSizeOptions={[10, 20, 50]}
                showSizeChanger
                onShowSizeChange={onShowSizeChange}
                onChange={onShowSizeChange}
                // defaultCurrent={p}
                total={total || 0}
                current={pageNo || 0}
                pageSize={pageSize || 10}
                showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`}
              />

            </div>
          </div>
        </div>
      </div>
    </Spin>

  );
};
