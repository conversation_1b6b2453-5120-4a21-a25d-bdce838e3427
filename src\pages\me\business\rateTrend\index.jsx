import React, { useState } from 'react';
import { Form, Row, Col, message, Button, Space, DatePicker } from 'antd';
import { Line } from '@ant-design/plots';
import './index.less';
import Menu from '../../components/Menu';
import Port from '@/components/Port';
import ShipCompany from '@/components/ShipCompany';
import Box from '@/components/Box';
import NoData from '@/components/NoData';
import { shipmentIcon } from '@/common/common';
import { api, } from '@/common/utils';
import * as R from 'ramda';


export default () => {
  const [searchForm] = Form.useForm();
  const [shipCompanys, setShipCompanys] = useState([]);
  const [originalDatas, setOriginalDatas] = useState([]);
  const [datas, setDatas] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [shipCompany, setShipCompany] = useState('');

  const config = {
    data: datas,
    xField: 'etdStr',
    yField: 'price',
    colorField: 'boxType',
    legend: {
      color: {
        layout: {
          justifyContent: 'flex-end',
          alignItems: 'center',
        },
        itemMarkerSize: 10,
      },
    },
    point: {
      shapeField: 'circle',
      sizeField: 2,
    },
    insetTop: 20,
    // shapeField: 'smooth',
    scale: { color: { range: ['#30BF78', '#F4664A', '#FAAD14', '#66F66A'] } },
    style: {
      lineWidth: 2,

    },
  };

  const onSearch = () => {
    setShipCompany('');
    setShipCompanys([]);
    setOriginalDatas([]);
    setDatas([]);
    let searchData = searchForm.getFieldValue();
    if (!searchData.pol) {
      message.error("请选择起运港!");
      return;
    }
    if (!searchData.pod) {
      message.error("请选择目的港!");
      return;
    }
    if (!searchData.shipCompanys || searchData.shipCompanys.length === 0) {
      message.error("选择船公司!");
      return;
    }
    if (!searchData.boxTypes || searchData.boxTypes.length === 0) {
      message.error("请选择箱型!");
      return;
    }
    setSearchLoading(true);
    api.freightPrice.listPriceTrend(searchData).subscribe({
      next: (data) => {
        setShipCompany('');
        setShipCompanys([]);
        setOriginalDatas([]);
        setDatas([]);

        let ships = searchData.shipCompanys;
        setShipCompanys(ships);
        let shipCompanySelected = ships.length > 0 ? ships[0] : '';
        setShipCompany(shipCompanySelected);
        setOriginalDatas(data);
        setDatas(data?.filter(r => r.shipCompany === shipCompanySelected));
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };


  const changeShipCom = (e) => {
    let temps = originalDatas;
    setShipCompany(e);
    setDatas(temps?.filter(r => r.shipCompany === e));
  }



  return (
    <Menu selectKeys={["rateTrend"]} openKeys={["business"]} >
      <>
        <div className="me-search">
          <Form form={searchForm}>
            <Row wrap={false} gutter={20}>
              <Col flex={'auto'}>
                <Row>
                  <Col span={8}>
                    <Form.Item name="pol" label="起运港" labelCol={{ flex: '90px' }}>
                      <Port placeholder="请选择" label="起运港" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="pod" label="目的港" labelCol={{ flex: '90px' }}>
                      <Port placeholder="请选择" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="shipCompanys" label="船公司" labelCol={{ flex: '90px' }}>
                      <ShipCompany placeholder="请选择" mode='multiple' />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="boxTypes" label="箱型" labelCol={{ flex: '90px' }}>
                      <Box placeholder="请选择" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="etdStart" label="船期起" labelCol={{ flex: '90px' }}>
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="etdEnd" label="船期止" labelCol={{ flex: '90px' }}>
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col flex={'100px'}>
                <Space size={[10, 24]} wrap>
                  <Button type="primary" htmlType="submit" onClick={() => onSearch()} loading={searchLoading}> 查 询 </Button>
                  <Button type="default" htmlType="reset"> 重 置 </Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </div>
        <div className='me-shipment-tabs'>
          {shipCompanys.map((r) => <img key={r}
            src={shipmentIcon(r)} style={{ opacity: r === shipCompany ? 1 : 0.5, border: r === shipCompany ? '1px solid #B4141B' : '1px solid #FFF' }} onClick={() => changeShipCom(r)} />
          )}
        </div>
        <div className='me-trend'>
          {datas.length == 0 ?
            <NoData />
            :
            <Line {...config} />
          }
        </div>
      </>
    </Menu>
  );
};
