.orderWarn-search {
    margin: 24px 0;
    border-bottom: 1px solid #f0f0f0;
}



.orderWarn-list {
    padding: 0 24px;
    .orderWarn-tools {
        display: flex;
        justify-content: space-between;

        .orderWarn-tools-btn {
            width: 200px;
            display: flex;
            justify-content: end;
        }
    }

    .orderWarn-radio {
        padding: 0 10px;
    }

    .orderWarn-expand {
        padding: 0 10px;

        p {
            margin: 0;
            line-height: 32px;

            span {
                color: #999;
                margin-left: 20px;
            }
        }
    }

    .orderWarn-table {
        .ant-table-thead {
            position: sticky;
            top: 56px;
            z-index: 99;
        }

    }

    .card-header {
        width: 100%;
        background: #FAFAFA;
        border: 1px solid #E8E8E8;
        margin-top: 16px;
        border-radius: 6px 6px 0 0;
        display: flex;
        justify-content: start;
        position: sticky;
        top: 56px;
        z-index: 99;

        .header-item {
            font-family: 'Arial';
            font-weight: 600;
            color: rgba(0, 0, 0, 0.88);
            line-height: 35px;
            margin: 10px 0;
            padding: 0 10px 0 9px;
            border-left: 1px solid #f0f0f0;
            text-align: center;
        }

    }

    .card-list {
        width: 100%;
        border: 1px solid #E8E8E8;
        border-radius: 6px;
        margin-top: 20px;

        .card-item-top {
            display: flex;
            justify-content: start;
            align-items: center;
            padding: 16px 0;

            p {
                margin: 0;
                line-height: 24px;
            }

            .card-item-cus {
                width: 165px;
                margin-left: 20px;
            }

            .card-item-logo {
                width: 45px;
                height: 45px;
                border-radius: 4px;
                border: 1px solid #E8E8E8;
                overflow: hidden;

                img {
                    width: 100%;
                }
            }

            .card-item-ports {
                width: 230px;
                padding: 0 16px;

            }

            .card-item-vess {
                width: 230px;
                display: flex;
                justify-content: start;
                padding: 0 5px;
            }

            .vessel-text {
                max-width: 220px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .card-item-atd {
                width: 120px;
                text-align: center;
            }

            .card-item-option {
                width: 120px;
                text-align: center;
                padding-top: 20px;
            }
        }

        .card-item-bottom {
            border-top: 1px solid #E8E8E8;
            background: #fafafa;
            overflow: hidden;
            position: relative;
            p {
                margin: 12px 0;
                line-height: 20px;
                color: #999;

                span {
                    color: #AAA;
                    margin-left: 30px;
                }
            }

            .card-expand-btn {
                position: absolute;
                right: 20px;
                bottom: 10px;
                color: #999;
            }

        }
    }

    .card-list:hover {
        background: #fffbfb;

        .card-item-bottom {
            background: transparent;
        }
    }
}


.orderWarn-pagination {
    display: flex;
    justify-content: flex-end;
    margin: 24px;
}