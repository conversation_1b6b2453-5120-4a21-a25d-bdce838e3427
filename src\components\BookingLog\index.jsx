import React, { useState, useEffect } from 'react';
import { Timeline } from 'antd';
import { api, dateFormat } from '@/common/utils';
import './index.less';
import * as R from 'ramda';


export default (props) => {

    // 日志数据 示例
    const temps = [
        {
            color: '#b4141b',
            children: <>
                <div className='log-title'>
                    <div className='title-optionType'>修改</div>
                    <div className='title-time'>2019-07-30 12:32:02</div>
                </div>
                <div className='log-content'>
                    共进行了3处修改，这里是对订单的修改记录...
                </div>
            </>
        },
    ]

    const [logItems, setLogItems] = useState([]);

    useEffect(() => {
        api.bookOrder.listHistoryLogs(props.id).subscribe({
            next: (data) => {
                if (data && data.length > 0) {
                    let temp = [];
                    R.forEach((v) => {
                        temp.push(
                            {
                                color: '#b4141b',
                                children: <>
                                    <div className='log-title'>
                                        <div className='title-optionType'>{v.title}</div>
                                        <div className='title-time'>{dateFormat(v.createTime, 'yyyy-MM-dd hh:mm')}</div>
                                    </div>
                                    <div className='log-content'>
                                        {v.description && R.split('#', v.description).map(
                                            (detail, index) =>
                                                detail && (
                                                    <>
                                                        <span key={index}>
                                                            {'<' + (index + 1) + '>. ' + detail}
                                                            <br />
                                                        </span>
                                                    </>
                                                ),
                                        )}
                                    </div>
                                </>
                            },

                        );
                    }, data);
                    setLogItems(temp);
                }
            },
        }).add(() => {
            // setLoading(false);
        });

    }, [props.id, props.activeKey,props.open]);


    return (<Timeline
        items={logItems}
        style={props.style}
    />)
}