import Texty from 'rc-texty';
import QueueAnim from 'rc-queue-anim';
import { Flex } from 'antd';
import './index.less';

export default () => {

  const toolArr = [
    { icon: 't01', title: '船期查询', desc: '查询实时船期', url: '/tools/shipSchedule' },
    { icon: 't02', title: '船舶轨迹', desc: '查询实时船舶轨迹', url: '/tools' },
    { icon: 't03', title: '用箱时间', desc: '查询用箱时间', url: '/tools' },
    { icon: 't04', title: '港区查询', desc: '查询全球港区信息', url: '/tools' },
    { icon: 't05', title: '舱单发送', desc: '直连八大船代', url: '/tools' },
    { icon: 't06', title: 'VGM发送', desc: '直联30+船司，一键发送', url: '/tools' },
    { icon: 't04', title: '目的港收费计算器', desc: '计算目的港各项费用', url: '/tools/portChargeCalculator' },
  ]

  return (
    <div className="za-content tools">
      <div className="com-content">
        <div className="tools-banner">
          <div className="tools-slogan">
            <div className="slogan-title">
              <Texty delay={100}>客户服务中心</Texty>
            </div>
            <div className="slogan-sub-title">
              <Texty delay={1000} type="flash">为您提供完整的国际货运解决方案，高效配合解决国际物流货运问题。为客户提供高效、快捷、优质的服务。</Texty>
            </div>

            <QueueAnim delay={3000} type="bottom" className="slogan-tags">
              <div className="tag-item" key="1">
                <div className="tag-icon i01"></div>
                <div className="tag-text">专业</div>
                <div className="tag-desc">20+年从业经验</div>
              </div>
              <div className="tag-item" key="2">
                <div className="tag-icon i02"></div>
                <div className="tag-text">快捷</div>
                <div className="tag-desc">30+头部船司合作</div>

              </div>
              <div className="tag-item" key="3">
                <div className="tag-icon i03"></div>
                <div className="tag-text">高效</div>
                <div className="tag-desc">一键线上订舱</div>
              </div>
              <div className="tag-item" key="14">
                <div className="tag-icon i04"></div>
                <div className="tag-text">优质</div>
                <div className="tag-desc">交通部正规资质</div>
              </div>
            </QueueAnim>
          </div>
        </div>

        <Flex wrap justify="flex-start">
          {toolArr.map((item, index) => <a className="tools-item" href={item.url} key={index}>
            <div className={`tools-item-icon ${item.icon}`}></div>
            <div className="tools-item-title">{item.title}</div>
            <div className="tools-item-desc">{item.desc}</div>
          </a>)
          }
        </Flex>
      </div>
    </div>
  );
};
