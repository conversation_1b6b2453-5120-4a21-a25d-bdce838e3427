import { defineConfig } from 'umi';
const { REACT_ENV } = process.env;

let proxy = {
  "dev": {
    '/authority/': {
      target: 'http://192.168.1.225:8008/',
    },
    '/business/': {
      target: 'http://192.168.1.225:8008/',
    },
  },
  "1": {
    '/authority/': {
      target: 'http://127.0.0.1:8080/',
      pathRewrite: { "authority": "eapi" }
    },
    '/business/': {
      target: 'http://127.0.0.1:8080/',
      pathRewrite: { "^/business": "eapi" }
    },
  }
}

export default defineConfig({
  npmClient: 'yarn',
  title: '客户服务平台',
  links: [
    { rel: 'icon', href: './favicon.ico' },
  ],
  metas: [
    { 'name': 'viewport', 'content': '' },
  ],
  locale: {
    enable: true,
    default: 'zh-CN',
  },
  // outputPath: 'html', //自定义打包输出文件
  hash: true, //增量发布和避免浏览器加载缓存
  proxy: proxy[REACT_ENV || 'dev'],
  mfsu: {},
    esbuildMinifyIIFE: true,
});
