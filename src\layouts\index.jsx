import { Outlet, useLocation, useIntl } from 'umi';
import { ConfigProvider, App } from 'antd';
import en from 'antd/lib/locale/en_GB.js';
import cn from 'antd/lib/locale/zh_CN.js';
import 'dayjs/locale/zh-cn';
import Nav from '@/components/Nav';
import CopyRight from '@/components/CopyRight';
import ChatwootWidget from '@/components/ChatwootWidget';
import ChatButton from '@/components/ChatButton';


export default function Layout() {
  const { formatMessage } = useIntl();
  const location = useLocation();
  const intlMap = { 'zh-CN': cn, 'en': en };

  return (
    <ConfigProvider    
      locale={intlMap[localStorage.getItem("umi_locale") || 'zh-CN']}
      theme={{
        token: {
          colorPrimary: '#B4141B',
        },
        components: {
          Splitter: {
            resizeSpinnerSize: 90
          },
        },
      }}>
      <App>
        {(location.pathname === '/login')
          ?
          <Outlet />
          :
          <>
            <Nav />
            <Outlet />
            <CopyRight />
            <ChatButton />
          </>
        }
        <ChatwootWidget />
      </App>
    </ConfigProvider>
  );
}
