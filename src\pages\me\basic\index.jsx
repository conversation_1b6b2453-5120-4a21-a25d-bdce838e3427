import React, { useState, useEffect } from 'react';
import { Button, Descriptions, Form, Input, Space, Row, Col, Avatar, Upload, Select, Spin, message } from 'antd';
import { UserOutlined, UploadOutlined } from '@ant-design/icons';
import { api, constant } from '@/common/utils';
import Menu from '../components/Menu';
import Company from '../components/Company';
import './index.less';
import objectAssign from 'object-assign';

export default () => {
  const [isEdit, setIsEdit] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState({});
  const [avatar, setAvatar] = useState('');


  useEffect(() => {
    getCurrentUser();
  }, []);

  const getCurrentUser = () => {
    api.user.getCurrentUser().subscribe({
      next: (data) => {
        setIsEdit(false);
        setCurrentUser(data);
        setAvatar(data?.avatar ? data?.avatar : '');
        sessionStorage.setItem(constant.KEY_CURRENT_USER, JSON.stringify(data));
      }
    });
  };


  const items = [
    {
      key: '1',
      label: <div className='basic-info-title'> 姓名</div>,
      children: currentUser.userRealCnName,
    },
    {
      key: '2',
      label: <div className='basic-info-title'> 性别</div>,
      children: currentUser.userSex || '保密',
    },
    {
      key: '3',
      label: <div className='basic-info-title'>手机号码</div>,
      children: currentUser.userMobile
    },
    {
      key: '4',
      label: <div className='basic-info-title'>电子邮箱</div>,
      children: currentUser.userEmail
    },

    {
      key: '9',
      label: <div className='basic-info-title'></div>,
      labelStyle: { color: '#fff' },
      children: <Button type='default' onClick={() => updateInfo()}>修改信息</Button>
    },
  ]

  const updateInfo = () => {
    objectAssign(currentUser, { userSex: currentUser.userSex || '保密' });
    form.setFieldsValue(currentUser);
    setIsEdit(true)
  }

  // 保存修改
  const saveInfo = (data) => {
    if (!data.userRealCnName) {
      message.warning('请填写姓名!');
      return false;
    }
    if (data.userEmail && !constant.REGEX_EMAIL.test(data.userEmail)) {
      message.warning('请填写正确的邮箱!');
      return false;
    }
    objectAssign(data, { id: currentUser.id, userName: currentUser.userName, avatar: avatar });
    setLoading(true);
    api.me.updateTenantUser(data).subscribe({
      next: (data) => {
        getCurrentUser();
      }
    }).add(() => {
      setLoading(false);
    });
  }

  return (
    <Spin spinning={loading}>

      <Menu selectKeys={["basic"]} openKeys={[]}>
        <div className='basicContent'>
          <Company />
          {isEdit ?
            <>
              <Row style={{ margin: '24px 0' }}>
                <Col span={3} style={{ textAlign: 'right', paddingRight: '24px' }}>
                  <Avatar size={64} icon={<UserOutlined />} src={avatar ? constant.FILE_URL + avatar : ''} />
                </Col>
                <Col span={18}>

                  <Upload showUploadList={false}
                    accept=".png, .jpg"
                    customRequest={async (options) => {
                      setLoading(true);
                      const { file, onSuccess, onError } = options;
                      const formData = new FormData();
                      formData.append('file', file);
                      api.base.upload(formData).subscribe({
                        next: (data) => {
                          onSuccess({ message: 'OK' });
                          message.success('上传成功!');
                          setAvatar(data.url);
                        },
                        error: (err) => onError(err),
                      }).add(() => {
                        setLoading(false);
                      });;
                    }}

                    beforeUpload={(file) => {
                      return new Promise((resolve, reject) => {
                        if (file.size / (1024 * 1024) < 3) {
                          resolve();
                        } else {
                          const err = '上传文件大小应小于3M';
                          message.error(err);
                          reject(new Error(err));
                        }
                      });
                    }}
                  >
                    <Button icon={<UploadOutlined />}>更换头像</Button>
                    <p style={{ margin: "5px" }}>支持图片类型：png, jpg</p>
                  </Upload>
                </Col>
              </Row>
              <Form onFinish={saveInfo} form={form}>
                <Form.Item label="姓名" name="userRealCnName" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                  <Input />
                </Form.Item>
                <Form.Item label="性别" name="userSex" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                  <Select style={{ width: '100%' }}>
                    <Select.Option value="男">男</Select.Option>
                    <Select.Option value="女">女</Select.Option>
                    <Select.Option value="保密">保密</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item label="手机号" name="userMobile" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                  <Input disabled={true} />
                </Form.Item>
                <Form.Item label="电子邮箱" name="userEmail" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                  <Input />
                </Form.Item>
                <Form.Item wrapperCol={{ offset: 3 }}>
                  <Space>
                    <Button onClick={() => setIsEdit(false)}>
                      取消
                    </Button>
                    <Button type="primary" htmlType="submit">
                      保存
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </>
            :
            <Descriptions
              title="基本信息"
              column={1}
              items={items}
              style={{ margin: '30px 20px' }}
            />
          }
        </div>
      </Menu>

    </Spin>
  );
};
