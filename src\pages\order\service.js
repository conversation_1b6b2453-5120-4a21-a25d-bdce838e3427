import { constant, iget, ipost, isearch, isget, } from '@/common/utils';

//查询
export function searchOrder(params) {
  return isearch(constant.API_ORDER + '/searchByCtm', params);
}

//运踪
export function getOrderTrack(params) {
  return isget(constant.API_ORDER + '/getOrderTrack/' + params);
}

//预警订阅详情
export function getTrackBookInfo(params) {
  return isget(constant.API_ORDER + '/getTrackBookInfo/' + params);
}

//异常订阅
export function bookException(params) {
  return ipost(constant.API_ORDER + '/bookException', params);
}

//业务详情
export function getOrderInfo(params) {
  return isget(constant.API_ORDER + '/' + params);
}

//业务详情
export function listFees(params) {
  return iget(constant.API_ORDER_FEE + '/listFees/' + params);
}
//业务详情
export function listChangeFees(params) {
  return iget(constant.API_ORDER_FEE + '/listChangeFees/' + params);
}


//业务详情
export function addBill(params) {
  return ipost(constant.API_CHECK_BILL + '/saveByOrderFees', params);
}

//查询预警
export function searchWarnOrder(params) {
  return isearch(constant.API_ORDER + '/searchWarnByCtm', params);
}