import React, { useState, useEffect, useRef } from 'react';
import { Modal, Spin } from 'antd';
import { PhotoSlider } from 'react-photo-view';
import 'react-photo-view/dist/index.css';
import Draggable from 'react-draggable';
import { constant } from '@/common/utils';

export default (props) => {
    const draggleRef = useRef();
    const { visible, fileUrl, onClose } = props;
    const [previewVisible, setPreviewVisible] = useState(false);
    const [photoVisible, setPhotoVisible] = useState(false);
    const { clientWidth, clientHeight } = window?.document?.documentElement;
    const [frameKey, setFrameKey] = useState('');
    const [frameUrl, setFrameUrl] = useState('');
    const [photoImages, setPhotoImages] = useState([]);
    const targetRect = draggleRef?.current?.getBoundingClientRect();
    const [disabled, setDisabled] = useState(true);
    const [bounds, setBounds] = useState({});

    const onStart = (_, uiData) => {
        setBounds({
            left: -targetRect?.left + uiData?.x,
            right: clientWidth - (targetRect?.right - uiData?.x),
            top: -targetRect?.top + uiData?.y,
            bottom: clientHeight - (targetRect?.bottom - uiData?.y),
        });
    };

    useEffect(() => {
        if (visible) {
            toPreview(fileUrl);
        }
    }, [visible]);

    //预览
    const toPreview = (url) => {
        let imgas = ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff'];
        let ext = url?.substr(url?.lastIndexOf(".") + 1)?.toLowerCase();
        let isImage = imgas.indexOf(ext?.toLowerCase()) != -1;
        let previewUrl = url?.replace('http://192.168.1.5/', "");
        if (previewUrl.indexOf('group') > -1) {
            previewUrl = constant.FDFS_PREFIX + previewUrl;
        }
        if (isImage) {
            setPhotoVisible(true);
            setPhotoImages(previewUrl.split(","));
        } else {
            setFrameUrl(ext === 'pdf' ? previewUrl : 'https://view.officeapps.live.com/op/embed.aspx?src=' + previewUrl);
            setFrameKey(Math.random());
            setPreviewVisible(true);
        }
    }

    return (
        <>
            <Modal
                title={
                    <div
                        style={{
                            width: '100%',
                            cursor: 'move',
                        }}
                        onMouseOver={() => {
                            if (disabled) {
                                setDisabled(false);
                            }
                        }}
                        onMouseOut={() => {
                            setDisabled(true);
                        }}
                        onFocus={() => { }}
                        onBlur={() => { }}
                    >
                        文件预览
                    </div>
                }
                width={clientWidth - 400}
                onCancel={() => {
                    setPreviewVisible(false);
                    onClose();
                }}
                centered
                footer={false}
                maskClosable={false}
                visible={previewVisible}
                bodyStyle={{ padding: '0', height: clientHeight - 56, overflow: 'auto' }}
                style={{ left: 0, right: 0, top: 0, bottom: 0, paddingBottom: 0, maxWidth: clientWidth }}
                modalRender={(modal) => (
                    <Draggable
                        disabled={disabled}
                        bounds={bounds}
                        nodeRef={targetRect}
                        onStart={(event, uiData) => onStart(event, uiData)}
                    >
                        <div ref={targetRect}>{modal}</div>
                    </Draggable>
                )}
            >
                <Spin spinning={false} delay={0} size='large'>
                    <iframe key={frameKey} src={frameUrl} width='100%' height={clientHeight - 56} frameBorder='0'
                        style={{ verticalAlign: 'bottom' }}></iframe>
                </Spin>
            </Modal>
            <PhotoSlider
                images={photoImages.map((item) => ({ src: item }))}
                visible={photoVisible}
                onClose={() => {
                    setPhotoVisible(false);
                    onClose();
                }}
            // index={photoIndex}
            // onIndexChange={setPhotoIndex}
            />
        </>
    )
}
