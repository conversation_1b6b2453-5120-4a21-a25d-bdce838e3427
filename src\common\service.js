import * as user from '@/pages/login/service';
import * as freightPrice from '@/pages/rate/service';
import * as base from '@/pages/components/service';
import * as bookOrder from '@/pages/booking/service';
import * as order from '@/pages/order/service';
import * as checkBill from '@/pages/finance/service';
import * as invoiceApply from '@/pages/me/finance/service';
import * as dict from '@/components/Dict/service';
import * as nav from '@/components/Nav/service';
import * as me from '@/pages/me/service';
import * as system from '@/pages/me/system/service';
import * as ai from '@/components/ChatRate/service';
import * as columnSetting from '@/components/Grid/service';

const api = {
    user,
    freightPrice,
    base,
    bookOrder,
    order,
    checkBill,
    invoiceApply,
    dict,
    nav,
    me,
    system,
    ai,
    columnSetting
};

export default api;