import React, { useState } from 'react';
import { Form, Row, Col, Button, Pagination, Table, Tag, Input, DatePicker, Space, Radio } from 'antd';
import './index.less';
import Menu from '../../components/Menu';
import Port from '@/components/Port';
import { api, dateFormat } from '@/common/utils';
import ShipCompany from '@/components/ShipCompany';
import { DownOutlined, UpOutlined, RightOutlined, AppstoreOutlined, BarsOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import objectAssign from 'object-assign';
import { shipmentIcon } from '@/common/common';
import Booking from '@/pages/rate/Booking';
import NoData from '@/components/NoData';

export default () => {
  const [searchForm] = Form.useForm();
  const [datas, setDatas] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [state, setState] = useState('');
  const [listType, setListType] = useState('card');
  const [rateData, setRateData] = useState({});
  const [isBookingOpen, setIsBookingOpen] = useState(false); // 下单


  const rateTrendMap = {
    "DOWN": <ArrowDownOutlined style={{ color: 'green' }} />,
    "UP": <ArrowUpOutlined style={{ color: 'red' }} />,
  }
  const shipScheduleMap = { '0': "周日", "1": "周一", "2": "周二", "3": "周三", "4": "周四", "5": "周五", "6": "周六", };
  const stateStatus = {
    TODO: { color: 'warning', text: '未下单' },
    DONE: { color: 'success', text: '已下单' },
    EXPIRED: { color: 'error', text: '已过期' },
  };

  const columns = [
    { title: '客户名称', dataIndex: 'customerName', width: 160, ellipsis: true, },
    { title: '船公司', dataIndex: 'shipCompany', width: 120, ellipsis: true, },
    { title: '20GP', dataIndex: 'gp20Price', width: 120, render: (text, record) => <>{text ? '$' + text : '-'} {text ? rateTrendMap[record.gp20Trend] : ''}</> },
    { title: '40GP', dataIndex: 'gp40Price', width: 120, render: (text, record) => <>{text ? '$' + text : '-'} {text ? rateTrendMap[record.gp40Trend] : ''}</> },
    { title: '40HC', dataIndex: 'hc40Price', width: 120, render: (text, record) => <>{text ? '$' + text : '-'} {text ? rateTrendMap[record.hc40Trend] : ''}</> },
    { title: '状态', dataIndex: 'state', width: 80, render: (text, record) => <Tag color={stateStatus[text]?.color}>{stateStatus[text]?.text}</Tag> },
    {
      title: '操作栏', dataIndex: 'option', width: 80, render: (text, record) => <>
        <Button size='small' type='primary' onClick={() => onBooking(record)} disabled={record.state === 'DONE' || record.state === 'EXPIRED'}>去下单</Button>
      </>
    },
  ]


  const onSearch = (pageNo, pageSize, state) => {
    setSearchLoading(true);
    setPageNo(pageNo);
    setPageSize(pageSize);
    let searchData = searchForm.getFieldValue();
    objectAssign(searchData, { state: state });
    let param = { dto: searchData, pageNo: pageNo, pageSize: pageSize };
    api.freightPrice.searchPlan(param).subscribe({
      next: (data) => {
        setTotal(data.total);
        setDatas(data.data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };


  const onShowSizeChange = (current, pageSize) => {
    onSearch(current, pageSize, state);
  };
  const onStateChange = ({ target: { value } }) => {
    setState(value);
    onSearch(1, pageSize, value);
  };

  const onSelectChange = (selectedKeys, selectedRows) => {
    setSelectedRowKeys(selectedKeys);
  };

  const stateOptions = [
    {
      label: <span className='rateHistory-radio'>全部</span>,
      value: '',
    },
    {
      label: <span className='rateHistory-radio'>未下单</span>,
      value: 'TODO',
    },
    {
      label: <span className='rateHistory-radio'>已下单</span>,
      value: 'DONE',
    },
  ];

  // 开票中每项的展开状态
  const [expandCards, setExpandCards] = useState({});
  const toggleItem = (id) => {
    setExpandCards(prevState => ({
      ...prevState,
      [id]: !prevState[id],
    }));
  };

  // 下单
  const onBooking = (data) => {
    let bookData = {};
    objectAssign(bookData, {
      runDate: data.runDate && data.expireDate ? (data.runDate + '~' + data.expireDate.substring(5, 10)) : '', pol: data.pol, pod: data.pod, polEnName: data.polEnName, podEnName: data.podEnName, ttOrDt: data.ttOrDt, ttPol: data.ttPol,
      shipCompany: data.shipCompany, gp20Price: data.gp20Price, gp40Price: data.gp40Price, hc40Price: data.hc40Price, etd: data.etd,
      vessel: data.vessel, voyage: data.voyage, shipDays: data.shipDays, shipSchedule: data.shipSchedule, rateType: data.rateType, planId: data.id,
    });
    setRateData(bookData);
    setIsBookingOpen(true);
  }

  return (<>

    <Menu selectKeys={["rateHistory"]} openKeys={["business"]} >
      <>
        <div className="rateHistory-search">
          <Form form={searchForm}>
            <Row wrap={false} gutter={20}>
              <Col flex={'auto'}>
                <Row>
                  <Col span={8}>
                    <Form.Item name="customerName" label="客户名称" labelCol={{ flex: '90px' }}>
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="pol" label="起运港" labelCol={{ flex: '90px' }}>
                      <Port placeholder="请选择" label="起运港" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="pod" label="目的港" labelCol={{ flex: '90px' }}>
                      <Port placeholder="请选择" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="shipCompanys" label="船公司" labelCol={{ flex: '90px' }}>
                      <ShipCompany placeholder="请选择" mode='multiple' />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="etdStart" label="船期起" labelCol={{ flex: '90px' }}>
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="etdEnd" label="船期止" labelCol={{ flex: '90px' }}>
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col flex={'100px'}>
                <Space size={[10, 24]} wrap>
                  <Button type="primary" htmlType="submit" onClick={() => onSearch()} loading={searchLoading}> 查 询 </Button>
                  <Button type="default" htmlType="reset"> 重 置 </Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </div>
        <div className='rateHistory-list'>
          <div className="rateHistory-tools">
            <div className="rateHistory-filter">
              <Radio.Group options={stateOptions} onChange={onStateChange} value={state} optionType="button" style={{}} />
            </div>
            <div className="rateHistory-tools-btn">
              <Space>
                <Button type="text" icon={<BarsOutlined style={listType == 'table' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('table')}></Button>
                <Button type="text" icon={<AppstoreOutlined style={listType == 'card' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('card')}></Button>
              </Space>
            </div>
          </div>
          {listType == 'table' ?
            <Table
              className='rateHistory-table'
              rowKey='id'
              pagination={false}
              style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
              // rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
              columns={columns}
              dataSource={datas}
              expandable={{
                expandedRowRender: (record) => (
                  <div className='rateHistory-expand'>
                    <p><span>起运港：</span>{record.polName}  <span>目的港：</span>{record.podName}  <span>航程：</span>{record.shipDays}天
                      <span>班期：</span>{shipScheduleMap[record.shipSchedule]} </p>
                    <p><span>船名航次：</span>{record.vessel}  /{record.voyage}   <span>ETD：</span>{dateFormat(record.etd)}
                      <span>报价时间：</span>{dateFormat(record.createTime, 'yyyy-MM-dd hh:mm')}
                      {/* <span>运价类型：</span><Tag color={record.rateType === '线下' ? 'success' : 'error'}>{record.rateType}</Tag>  */}
                    </p>
                  </div>
                ),
                expandIcon: ({ expanded, onExpand, record }) =>
                  expanded ?
                    (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                    :
                    (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
              }}
            />
            :
            <>
              <div className='card-header'>
                <div className='header-item' style={{ width: '180px', textAlign: 'left', paddingLeft: '20px' }}>客户名称</div>
                <div className='header-item' style={{ width: '299px' }}>基础信息</div>
                <div className='header-item' style={{ width: '90px' }}>20GP</div>
                <div className='header-item' style={{ width: '90px' }}>40GP</div>
                <div className='header-item' style={{ width: '90px' }}>40HC</div>
                <div className='header-item' style={{ width: '100px' }}>操作栏</div>
              </div>

              {datas.map((r) => <div className='card-list'>
                <div className='card-item-top'>
                  <div className='card-item-cus'>
                    <span>{r.customerName}</span>
                  </div>
                  <div className='card-item-logo'>
                    <img src={shipmentIcon(r.shipCompany)} />
                  </div>
                  <div className='card-item-ports'>
                    <p>{r.shipCompany}</p>
                    <p style={{ color: '#999' }}>{r.polEnName} → {r.podEnName}</p>
                  </div>

                  <div className='card-item-price'>
                    <p>{r.gp20Price ? '$' + r.gp20Price : '-'}{r.gp20Price ? rateTrendMap[r.gp20Trend] : ''}</p>
                  </div>
                  <div className='card-item-price'>
                    <p>{r.gp40Price ? '$' + r.gp40Price : '-'}{r.gp40Price ? rateTrendMap[r.gp40Trend] : ''}</p>
                  </div>
                  <div className='card-item-price'>
                    <p>{r.hc40Price ? '$' + r.hc40Price : '-'}{r.hc40Price ? rateTrendMap[r.hc40Trend] : ''}</p>
                  </div>
                  <div className='card-item-option'>
                    <Button type="primary" onClick={() => onBooking(r)} disabled={r.state === 'DONE' || r.state === 'EXPIRED'}>下单</Button>
                  </div>

                </div>
                <div className='card-item-bottom'>
                  <p><Tag color={stateStatus[r.state]?.color} style={{ marginLeft: '20px' }}>{stateStatus[r.state]?.text}</Tag>
                    <span>船名航次：</span>{r.vessel}  /{r.voyage} <span>航程：</span>{r.shipDays}天
                    <span>ETD：</span>{dateFormat(r.etd)}
                    <span>报价时间：</span>{dateFormat(r.createTime, 'yyyy-MM-dd hh:mm')}</p>

                  {expandCards[r.id] ?
                    <>
                      <p> <span style={{ marginLeft: '20px' }}>起运港：</span>{r.polName}  <span>目的港：</span>{r.podName}
                        <span>班期：</span>{shipScheduleMap[r.shipSchedule]} </p>
                      <Button type="text" icon={<UpOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>收起</Button>
                    </>
                    :
                    <Button type="text" icon={<DownOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>展开</Button>}
                </div>
              </div>)}

              {datas.length == 0 && <NoData />}

            </>
          }
        </div>
        <div className='rateHistory-pagination'>
          <Pagination
            pageSizeOptions={[10, 20, 50]}
            showSizeChanger
            onShowSizeChange={onShowSizeChange}
            onChange={onShowSizeChange}
            // defaultCurrent={p}
            total={total || 0}
            current={pageNo || 0}
            pageSize={pageSize || 10}
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`}
          />

        </div>
      </>
    </Menu>

    <Booking
      current={rateData}
      open={isBookingOpen}
      onCancel={() => setIsBookingOpen(false)}
      onSubmit={() => {
        setIsBookingOpen(false);
        onSearch(1, pageSize, state);
      }}
    />
  </>

  );
};
