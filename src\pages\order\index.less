// 订单
.order {
    background: url('@/assets/order-bg.png');
    background-repeat: no-repeat;
    background-position: top center;

    .order-content {
        width: 1200px;
        margin: 0 auto;

        .order-slogan {
            height: 190px;
            overflow: hidden;

            .slogan-title {
                font-weight: bold;
                font-size: 38px;
                color: #482828;
                line-height: 53px;
                margin-top: 50px;
            }

            .slogan-sub-title {
                font-weight: 400;
                font-size: 20px;
                color: #482828;
                line-height: 28px;
                margin-top: 14px;
            }
        }

        .order-search {
            width: 1160px;
            min-height: 100px;
            margin: 24px auto;
            background: #fff;
            border-radius: 10px 10px;
            box-shadow: 0px 0px 20px #ddd;
            padding: 20px;

            .search-default {
                display: flex;
                margin-bottom: 24px;

                .search-ports {
                    width: 300px;
                    height: 60px;
                    border: 1px solid #D9D9D9;
                    border-radius: 4px;
                    display: flex;
                    margin: '0 0 0 15px';

                    .search-pol,
                    .search-pod {
                        width: 100%;
                        margin: 0 0 0 15px
                    }

                    .ports-btn {
                        width: 60px;

                        img {
                            width: 36px;
                            height: 36px;
                            margin: 12px;
                            border-radius: 50%;
                            box-shadow: 0 0 5px #ccc;
                        }
                    }
                }

                .search-atd {
                    width: 300px;
                    height: 60px;
                    border: 1px solid #D9D9D9;
                    border-radius: 4px;
                    margin: 0 15px;
                }

                .search-btn {
                    width: 230px;
                    height: 60px;
                    background: rgba(180, 20, 27, 1);
                    color: #fff;
                    border-radius: 4px;
                    font-size: 22px;
                    line-height: 60px;
                    text-align: center;
                    user-select: none;
                }

                .search-btn:hover {
                    cursor: pointer;
                    background: rgba(180, 20, 27, 0.9);
                }
            }

            .search-expand {
                margin: 0;
                padding: 0;
            }

            .search-tool {
                display: flex;
                justify-content: flex-end;
                margin: 0;

                .tool-btn {
                    width: 150px;
                    display: flex;
                    justify-content: end;

                }

            }
        }

        .order-list {
            min-height: 500px;
            .order-tools {
                display: flex;
                justify-content: space-between;

                .order-tools-btn {
                    width: 200px;
                    display: flex;
                    justify-content: end;
                }
            }

            .order-expand {
                padding: 0 80px;

                p {
                    margin: 0;
                    line-height: 32px;

                    span {
                        color: #999;
                        margin-left: 30px;
                    }
                }
            }

            .order-table {
                .ant-table-thead {
                    position: sticky;
                    top: 56px;
                    z-index: 99;
                }

            }

            .card-header {
                width: 100%;
                background: #FAFAFA;
                border: 1px solid #E8E8E8;
                margin-top: 16px;
                border-radius: 6px 6px 0 0;
                display: flex;
                justify-content: start;
                position: sticky;
                top: 56px;
                z-index: 99;

                .header-item {
                    font-family: 'Arial';
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.88);
                    line-height: 35px;
                    margin: 10px 0;
                    padding: 0 10px 0 9px;
                    border-left: 1px solid #f0f0f0;
                    text-align: center;
                }

            }

            .card-list {
                width: 100%;
                border: 1px solid #E8E8E8;
                border-radius: 6px;
                margin-top: 20px;

                .card-item-top {
                    display: flex;
                    padding: 16px 0px 10px 0;
                    justify-content: space-between;

                    .card-item-topLeft {
                        display: flex;
                        justify-content: start;
                        line-height: 28px;

                        .card-item-checkbox {
                            width: 16px;
                            padding: 0 18px 0 10px;
                        }

                        .card-item-blNo {
                            width: 200px;
                            display: flex;
                            justify-content: start;

                            .blNo-text {
                                max-width: 110px;
                                white-space: nowrap;
                                padding: 0 10px;
                                overflow: hidden;
                            }
                        }

                        .card-item-vessel {
                            width: 315px;
                            display: flex;
                            justify-content: start;

                            .vessel-text {
                                max-width: 220px;
                                white-space: nowrap;
                                padding: 0 10px;
                                overflow: hidden;
                            }
                        }
                    }

                    .card-item-topRight {
                        width: 560px;
                        padding-right: 40px;

                        .ant-steps-item {
                            width: 56px;
                        }

                        .ant-steps-item-title {
                            font-size: 12px !important;
                        }

                        .ant-steps-item-content {
                            margin-top: 2px;
                        }

                        .ant-steps-item-active {
                            .ant-steps-item-icon {
                                width: 8px;
                                height: 8px;
                            }
                        }
                    }
                }

                .card-item-middle {
                    background: #fafafa;
                    display: flex;
                    justify-content: start;
                    padding: 16px 0;

                    p {
                        margin: 0;
                        line-height: 24px;
                    }

                    .card-item-checkbox {
                        width: 16px;
                        margin: 12px 10px;
                    }

                    .card-item-entrust {
                        width: 120px;
                        margin: 12px 10px;
                    }

                    .card-item-logo {
                        width: 45px;
                        height: 45px;
                        border-radius: 4px;
                        border: 1px solid #E8E8E8;
                        overflow: hidden;

                        img {
                            width: 100%;
                        }
                    }

                    .card-item-ports {
                        width: 320px;
                        padding: 0 16px;

                        p {
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }

                    .card-item-boxInfo {
                        width: 140px;
                        text-align: center;
                        font-size: 14px;
                        font-weight: bold;
                        line-height: 45px;
                    }

                    .card-item-order {
                        width: 145px;
                        color: #333;
                        font-size: 14px;
                        line-height: 45px;
                    }
                }

                .card-item-bottom {
                    border-top: 1px solid #E8E8E8;
                    background: #fafafa;
                    overflow: hidden;
                    display: flex;
                    justify-content: space-between;

                    .card-item-extend {
                        margin: 12px 12px;
                        line-height: 20px;
                        color: #999;

                        span {
                            color: #AAA;
                            margin-left: 30px;
                        }
                    }

                    .card-item-traceBtn {
                        margin: 10px 12px;
                    }

                }
            }

            .card-list:hover {
                background: #fffbfb;

                .card-item-middle,
                .card-item-bottom {
                    background: transparent;
                }
            }
        }

        .order-pagination {
            display: flex;
            justify-content: flex-end;
            margin: 20px 0 0 0;
        }

        .order-radio {
            padding: 0 10px;
        }

        .order-cargo {
            color: #999999;
        }
    }
}


.order-track-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;

    .order-track-title-info {
        width: 1000px;
    }
}

.order-track-ctnDetail {
    margin-top: -16px;
    // width: 1152px;
    height: calc(100vh - 460px);
    overflow: auto;
    border: 1px solid #f0f0f0;
    border-top: 0;
    padding: 0 24px;

    .order-track-ctnRowHeader {
        font-weight: bold;
        border-bottom: 1px solid #f0f0f0;
        margin: 20px 0 20px 0;
        padding: 0 25px;
        line-height: 40px;
        background-color: #FAFAFA;
    }

    .order-track-ctnRow {
        border-bottom: 1px solid #f6f6f6;
        margin-bottom: -5px;

        .ant-col {
            line-height: 30px;
        }
    }

    .order-track-alert {
        background: #FFF1F0;
        border-radius: 4px;
        border: 1px solid #FFCCC7;
        margin-top: 24px;
    }

}


.tarck-order-steps {
    .ant-steps-item-tail {
        // width: 69px;
        height: 2px;
        background: #B4141B;
    }

    .ant-steps-item-content {
        margin-top: 0 !important;
    }
}