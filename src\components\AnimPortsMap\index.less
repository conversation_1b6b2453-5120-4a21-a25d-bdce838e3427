.anim-port {
    position: relative;
    width: 100%;
    padding-top: 27px;

    .ant-select .ant-select-selector {
        border: none !important;
        box-shadow: none !important;
    }

    .ant-select-focused~label,
    .ant-select:has(.ant-select-selection-item)~label {
        transform: translateY(-18px);
    }

    .ant-select-arrow {
        display: none;
    }

    label {
        position: absolute;
        top: 20px;
        font-size: 16px;
        line-height: 20px;
        color: #666;
        pointer-events: none;
        transition: all 0.25s ease;

        @supports not selector(:has(p)) {
            top: 2px !important;
        }
    }

    label.left {
        left: 15px;
    }

    label.right {
        right: 15px;
    }

}

.portName {
    margin: 0 10px 0 0;
    padding: 0 15px;
    line-height: 30px;
    color: #666;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
}

.portName:hover {
    background: #f0f0f0;
    color: #B4141B;
}