import React, { useState, useEffect, useRef } from 'react';
import { api, constant, isEmpty, md5, parse, useInterval } from '@/common/utils';
import { Alert, Tabs, Form, Row, Col, Input, Checkbox, Button, message, Tour, FloatButton, Result, Tooltip, Modal, Space } from 'antd';
import { LockOutlined, LockTwoTone, UserOutlined, MobileOutlined, CodeOutlined, QuestionCircleOutlined, CheckOutlined } from '@ant-design/icons';

import QueueAnim from 'rc-queue-anim';
import { history } from 'umi';
import './index.less';


import demo from '@/assets/demo-cover.jpg';
const getPageQuery = () => parse(window.location.href.split('?')[1]);

const LoginMessage = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const getCookie = (name) => {
  var arr,
    reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)');
  if ((arr = document.cookie.match(reg))) return unescape(arr[2]);
  else return null;
};

export default () => {
  const [activeTab, setActiveTab] = useState('login');
  const bindRef = useRef();
  const [qrUrl, setQrUrl] = useState();
  const [qrLoading, setQrLoading] = useState(false);
  const [unionId, setUnionId] = useState('');
  const [qrMask, setQrMask] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [bindVisible, setBindVisible] = useState(false);
  const [bindLoading, setBindLoading] = useState(false);
  const [beBindError, setBeBindError] = useState(false);
  const [bindErrorMessage, setBindErrorMessage] = useState('');
  const [okDisabled, setOkDisabled] = useState(true);

  const [loginForm] = Form.useForm();
  const [registForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [regInfo, setRegInfo] = useState({});

  const [count, setCount] = useState(0);
  const [disabled, setDisabled] = useState(false);

  const [regStep, setRegStep] = useState(1);

  const [loading, setLoading] = useState(false);

  const [errorMessage, setErrorMessage] = useState('');
  const [beError, setBeError] = useState(false);


  useEffect(() => {
    if (count > 0) {
      const intervalId = setInterval(() => {
        setCount((prevCount) => prevCount - 1);
      }, 1000);
      return () => clearInterval(intervalId);
    } else {
      setDisabled(false);
    }
  }, [count]);

  useEffect(() => {
    loginForm.setFieldsValue({
      userName: getCookie(constant.SYSTEM_AVATAR_NAME + 'userName'),
      userPasswd: getCookie(constant.SYSTEM_AVATAR_NAME + 'userPasswd'),
      autoLogin: getCookie(constant.SYSTEM_AVATAR_NAME + 'autoLogin'),
    });

    clearLoopQRResult();
    return () => {
      clearLoopQRResult();
    };
  }, []);

  useEffect(() => {
    if (unionId) {
      setIsRunning(true);
    }
    return () => {
      clearLoopQRResult();
    };
  }, [unionId]);


  const fetchQRTicket = () => {
    clearLoopQRResult();
    setQrLoading(true);
    api.user.fetchQRTicket()
      .subscribe({
        next: (data) => {
          const t = data[0];
          setUnionId(t.scene);
          setQrUrl(t.qrUrl);
        },
      })
      .add(() => setQrLoading(false));
  };

  const fetchQRResult = (unionId) => {
    api.user.fetchQRResult(unionId).subscribe({
      next: (data) => {
        // console.log(data);
        setQrMask('');
        const result = data[0];
        if (!result) {
          return;
        }

        if (result.scene !== unionId) {
          return;
        }

        if (result.beScan) {
          // console.log('已扫码。。。。');
          setQrMask('BESCAN');
        }

        if (result.state === 'CREATED') {
          // console.log('已创建，待扫描。。。');
        }

        if (result.state === 'EXPIRED') {
          console.log('二维码已过期。。。重新生成。。。');
          clearLoopQRResult();
          fetchQRTicket();
          return;
        }

        if (result.state === 'NOT_BIND') {
          console.log('未绑定。。。请绑定');
          clearLoopQRResult();
          setBindVisible(true);
          return;
        }

        if (result.state === 'SUCCESS') {
          console.log('登陆成功。。。');
          setQrMask('SUCCESS');
          clearLoopQRResult();
          keepToken(result.accessToken, 'WEIXIN');
          return;
        }
      },
    });
  };

  useInterval(
    () => {
      fetchQRResult(unionId);
    },
    isRunning ? 1000 : null,
  );

  const bindWeixinUser = (v) => {
    console.info(v)
    setBindLoading(true);
    v.userPasswd = md5(v.userPasswd);
    v.unionId = unionId;
    api.user.bindUser(v).subscribe({
      next: (data) => {
        const result = data[0];
        if (result.state === 'SUCCESS') {
          clearLoopQRResult();
          keepToken(result.accessToken, 'WEIXIN');
        }
      },
    }).add(() => setBindLoading(false));
  };

  const clearLoopQRResult = () => {
    setIsRunning(false);
  };

  useEffect(() => {
    if (unionId) {
      setIsRunning(true);
    }
    return () => {
      clearLoopQRResult();
    };
  }, [unionId]);

  const handleToken = (token, systemTag) => {
    let itoken;
    if (!isEmpty(token)) {
      itoken = `Bearer ${token}`;
      sessionStorage.setItem(constant.KEY_USER_TOKEN, itoken);
    }
    if (systemTag) {
      sessionStorage.setItem(constant.KEY_USER_SYSTEM_TAG, systemTag);
    }
  };

  const handleRemeberMe = (values) => {
    const { userName, originPassword, autoLogin } = values;
    var exp = new Date();
    exp.setTime(exp.getTime() + 7 * 24 * 60 * 60 * 1000);

    if (autoLogin) {
      document.cookie = constant.SYSTEM_AVATAR_NAME + 'userName' + "=" + escape(userName) + ';expires=' + exp.toGMTString();
      document.cookie = constant.SYSTEM_AVATAR_NAME + 'userPasswd' + "=" + escape(originPassword) + ";expires=" + exp.toGMTString();
      document.cookie = constant.SYSTEM_AVATAR_NAME + 'autoLogin' + "=" + escape(autoLogin) + ";expires=" + exp.toGMTString();
    } else {
      exp.setTime(exp.getTime() - 1);
      document.cookie = constant.SYSTEM_AVATAR_NAME + 'userName' + "=" + escape('') + ';expires=' + exp.toGMTString();
      document.cookie = constant.SYSTEM_AVATAR_NAME + 'userPasswd' + "=" + escape('') + ";expires=" + exp.toGMTString();
      document.cookie = constant.SYSTEM_AVATAR_NAME + 'autoLogin' + "=" + escape('') + ";expires=" + exp.toGMTString();
    }
  };

  const handleRedirect = () => {
    const urlParams = new URL(window.location.href);
    const params = getPageQuery();
    // localStorage.setItem('umi-locale', localStorage.getItem('umi-locale') || 'zh-CN');
    let { redirect } = params;
    let url = '/rate';
    if (redirect) {
      const redirectUrlParams = new URL(redirect);
      if (redirectUrlParams.origin === urlParams.origin) {
        redirect = redirect.substr(urlParams.origin.length);
        if (redirect.match(/^\/.*#/)) {
          redirect = redirect.substr(redirect.indexOf('#') + 1);
          url = redirect;
        }
      }
    }
    history.push(url);
  };


  const onChange = (key) => {
    setActiveTab(key);
  };


  const items = [
    { key: 'login', label: '系统登录' },
    // { key: 'regist', label: '账号注册' }
  ]

  const onLogin = (v) => {
    setLoading(true);
    v.systemTag = 'business' + constant.PREFIX_OF_CACHE;
    v.originPassword = v.userPasswd;
    v.userPasswd = md5(v.userPasswd);
    api.user.login(v, v.systemTag).subscribe({
      next: (data) => {
        const resp = data[0];
        message.success('登录成功');
        handleRemeberMe(v);
        handleToken(resp.access_token, v.systemTag);

        api.user.loadUserButtons().subscribe({
          next: (br) => {
            sessionStorage.setItem(constant.KEY_USER_BUTTON_PERMS, br || []);
          }
        });
        //获取用户信息
        api.user.getCurrentUser().subscribe({
          next: (cu) => {
            sessionStorage.setItem(constant.KEY_CURRENT_USER, JSON.stringify(cu));

            // 更新Chatwoot用户信息
            if (window.chatwootSDK) {
              setTimeout(() => {
                try {
                  const userInfo = {
                    email: cu.userEmail || `${cu.userName}@company.com`,
                    name: cu.userRealCnName || cu.userName,
                    identifier: cu.userName,
                    phone: cu.userPhone || '',
                    company: cu.companyName || '',
                    department: cu.departmentName || ''
                  };

                  window.chatwootSDK.setUser(cu.userName, userInfo);

                  const customAttributes = {
                    userId: cu.userId,
                    userCode: cu.userCode,
                    loginTime: new Date().toISOString(),
                    systemTag: v.systemTag
                  };

                  window.chatwootSDK.setCustomAttributes(customAttributes);
                  console.log('Chatwoot user info updated after login');
                } catch (error) {
                  console.error('Error updating Chatwoot user info after login:', error);
                }
              }, 1000);
            }

            handleRedirect();
          }
        });

      },
      error: (err) => {
        console.log(err);
      }
    }).add(() => setLoading(false));
  };

  const keepToken = (token, systemTag) => {
    handleToken(token, systemTag);

    api.user.loadUserButtons().subscribe({
      next: (br) => {
        sessionStorage.setItem(constant.KEY_USER_BUTTON_PERMS, br || []);
      }
    });
    //获取用户信息
    api.user.getCurrentUser().subscribe({
      next: (cu) => {
        sessionStorage.setItem(constant.KEY_CURRENT_USER, JSON.stringify(cu));

        // 更新Chatwoot用户信息
        if (window.chatwootSDK) {
          setTimeout(() => {
            try {
              const userInfo = {
                email: cu.userEmail || `${cu.userName}@company.com`,
                name: cu.userRealCnName || cu.userName,
                identifier: cu.userName,
                phone: cu.userPhone || '',
                company: cu.companyName || '',
                department: cu.departmentName || ''
              };

              window.chatwootSDK.setUser(cu.userName, userInfo);

              const customAttributes = {
                userId: cu.userId,
                userCode: cu.userCode,
                loginTime: new Date().toISOString(),
                systemTag: systemTag
              };

              window.chatwootSDK.setCustomAttributes(customAttributes);
              console.log('Chatwoot user info updated after WeChat login');
            } catch (error) {
              console.error('Error updating Chatwoot user info after WeChat login:', error);
            }
          }, 1000);
        }

        handleRedirect();
      }
    });
  }

  const onRegist = (values) => {
    // console.log('onRegist:', values);
    setRegInfo(values);
    setRegStep(2);
  };

  const onSetPassword = (values) => {
    // console.log('onSetPassword:', values);
    let passwordInfo = { password: md5(values.password), repeatPassword: md5(values.repeatPassword) };
    let regForm = { ...regInfo, ...passwordInfo }

    api.user.register(regForm).subscribe({
      next: (data) => {
        message.success('注册成功！');
        setRegStep(3);
      }
    })
  }

  const validatePhone = (phoneNumber) => {
    const regex = /^1[3-9]\d{9}$/;
    return regex.test(phoneNumber);
  }

  const getCaptcha = (v) => {
    setCount(30); // 设置倒计时秒数
    setDisabled(true);
    api.user.sendSms(v).subscribe({
      next: (data) => {
        message.info('验证码已经发送至' + v + '，请注意查收');
      }
    });
  }

  // 演示
  const [open, setOpen] = useState(false);

  const ref1 = useRef(null);
  const ref2 = useRef(null);
  const ref3 = useRef(null);

  const steps = [
    {
      title: '',
      description: '客户中心系统登录演示',
      cover: (<img alt="tour.png" src={demo} />),
    },
    {
      title: '输入账号',
      description: '请输入您的账号，手机号或邮箱',
      target: () => ref1.current,
    },
    {
      title: '输入密码',
      description: '请输入您的密码，密码是6位数字',
      target: () => ref2.current,
    },
    {
      title: '系统登录',
      description: '请点击登录，进入系统，开始使用',
      target: () => ref3.current,
    },
  ];


  return (<>
    <div className='za-login'>
      <div className='za-login-left'>
        <div className='za-login-title'>
          <QueueAnim type='top' interval={500}>
            <h3 key="t01">海运运价客户服务平台</h3>
            <span key="t02">给每一个客户提供快捷、高效、优质的服务</span>
          </QueueAnim>
        </div>
        <div className='za-login-description'>
          <QueueAnim delay={800} interval={300}>
            <li key="l01">聚焦国际货运核心场景，运价一键查询</li>
            <li key="l02">界面响应快速，智能响应用户需求</li>
            <li key="l03">交通部批准成立的一级货运代理</li>
            <li key="l04">30+船公司的直接订舱代理</li>
          </QueueAnim>
        </div>
      </div>
      <div className='za-login-right'>
        <div className='za-login-form'>
          <div className='za-login-logo'></div>
          <Tabs size='large' activeKey={activeTab} items={items} onChange={onChange} />
          {activeTab === 'login' ?
            <Form
              key="login"
              form={loginForm}
              size='large'
              onFinish={onLogin}
            >
              {beError && (
                <LoginMessage
                  content={errorMessage}
                />)}
              <div ref={ref1}>
                <Form.Item
                  name="userName"
                  rules={[{ required: true, message: '', }]}
                >
                  <Input prefix={<UserOutlined />} placeholder="请输入用户名" />
                </Form.Item>
              </div>
              <div ref={ref2}>
                <Form.Item
                  name="userPasswd"
                  rules={[{ required: true, message: '', }]}
                >
                  <Input.Password prefix={<LockOutlined />} placeholder="请输入密码" />
                </Form.Item>
              </div>
              <Row>
                <Col span={12}>
                  <Form.Item
                    name="autoLogin"
                    valuePropName="checked"
                  >
                    <Checkbox>记住密码</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={12} className='za-login-forget'>
                  <Form.Item>
                    <a>忘记密码？</a>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item >
                <Button type="primary" htmlType="submit" loading={loading} style={{ width: '100%' }} ref={ref3}>
                  登录
                </Button>
              </Form.Item>
              {/*<div className='za-login-other'>*/}
              {/*  <Tooltip title={<img className='wxCode' src={qrUrl} />} color="#FFF" key="wx" trigger="click">*/}
              {/*    <div className='za-login-other-wx' onClick={() => fetchQRTicket()}></div>*/}
              {/*  </Tooltip>*/}
              {/*  <div className='za-login-other-qq' onClick={() => message.warning('尚未开放，敬请期待')}></div>*/}
              {/*</div>*/}
            </Form>
            :
            <>
              {regStep == 1 &&
                <Form
                  key="regist"
                  form={registForm}
                  size='large'
                  onFinish={onRegist}
                >
                  <Form.Item
                    name="userName"
                    rules={[{ required: true, message: '' }]}
                  >
                    <Input prefix={<MobileOutlined />} placeholder="请输入手机号码" />
                  </Form.Item>
                  <Row gutter={8}>
                    <Col flex='auto'>
                      <Form.Item
                        name="validateCode"
                        rules={[{ required: true, message: '' }]}
                      >
                        <Input prefix={<CodeOutlined />} placeholder="请输入验证码" />
                      </Form.Item>
                    </Col>
                    <Col flex="120px">
                      <Form.Item shouldUpdate>
                        {({ getFieldValue }) => (<Button
                          style={{ width: '100%' }}
                          disabled={!validatePhone(getFieldValue('userName')) || disabled}
                          onClick={() => getCaptcha(getFieldValue('userName'))}
                        >
                          {count > 0 ? `重新获取(${count})` : '获取验证码'}
                        </Button>
                        )}
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item
                    name="agreement"
                    valuePropName="checked"
                  >
                    <Checkbox>同意所有《<a>用户注册协议与条款</a>》</Checkbox>
                  </Form.Item>
                  <Form.Item shouldUpdate>
                    {({ getFieldValue }) => (
                      <Button type="primary" htmlType="submit" style={{ width: '100%' }} disabled={!getFieldValue('agreement')}>
                        快速注册
                      </Button>
                    )}
                  </Form.Item>

                  <div className='za-login-other'>
                    <Tooltip title={<img className='wxCode' src={require('../../assets/temp/code.png')} />} color="#FFF" key="wx" trigger="click">
                      <div className='za-login-other-wx'></div>
                    </Tooltip>
                    <div className='za-login-other-qq' onClick={() => message.warning('尚未开放，敬请期待')}></div>
                  </div>
                </Form>
              }
              {regStep == 2 &&
                <Form
                  key="setPassword"
                  form={passwordForm}
                  size='large'
                  onFinish={onSetPassword}
                >
                  <Form.Item style={{ textAlign: 'center', color: '#999', lineHeight: '32px' }}>
                    请设置初始密码，完成注册。
                  </Form.Item>
                  <Form.Item
                    name="password"
                    rules={[{ required: true, message: '' }]}
                  >
                    <Input prefix={<LockOutlined />} placeholder="设置密码，6 - 16 位密码，区分大小" type='password' />
                  </Form.Item>

                  <Form.Item
                    name="repeatPassword"
                    rules={[{ required: true, message: '' }]}
                  >
                    <Input prefix={<CheckOutlined />} placeholder="请再次确认密码" type='password' />
                  </Form.Item>

                  <Form.Item shouldUpdate>
                    {({ getFieldValue }) => (
                      <Button type="primary" htmlType="submit" style={{ width: '100%' }} disabled={getFieldValue('password') != getFieldValue('repeatPassword') || !getFieldValue('password')}>
                        确认提交
                      </Button>
                    )}
                  </Form.Item>

                  <Form.Item style={{ marginBottom: '20px' }}>
                    <Button type="default" style={{ width: '100%' }} onClick={() => setRegStep(1)}>
                      上 一 步
                    </Button>
                  </Form.Item>

                </Form>
              }
              {regStep == 3 &&
                <Result
                  status="success"
                  title={<p style={{ textAlign: 'center', lineHeight: '38px', fontSize: '18px', margin: '0' }}>您的账号：13968868911 注册成功</p>}
                  subTitle="请登录"
                  extra={[
                    <Button key="home" onClick={() => history.push('/')}>
                      返回首页
                    </Button>,
                    <Button key="login" type="primary" onClick={() => setActiveTab('login')}>
                      立即登录
                    </Button>
                  ]}>
                </Result>
              }
            </>
          }
        </div>
      </div>
    </div>
    <Modal
      ref={bindRef}
      loading={bindLoading}
      title="绑定系统账号"
      width="400px"
      open={bindVisible}
      onCancel={() => {
        setBindVisible(false);
        fetchQRTicket();
      }}
      footer={null}
    >
      {beBindError && <LoginMessage content={bindErrorMessage} />}
      <Form onFinish={bindWeixinUser}>
        <Row>
          <Col span={24}>
            <Form.Item
              name="userName"
              label="账号"
              rules={[
                {
                  required: true,
                  message: '请输入账号!',
                },
              ]}
            >
              <Input
                autoFocus={true}
                prefix={<UserOutlined />}
                placeholder="请输入账号"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Form.Item
              name="userPasswd"
              label="密码"
              rules={[
                {
                  required: true,
                  message: '请输入密码！',
                },
              ]}
            >
              <Input.Password
                prefix={<LockTwoTone />}
                placeholder="请输入密码"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={4}></Col>
          <Col span={20}>
            <p style={{ marginTop: '20px' }}>初次扫码登录，需要先绑定您的系统账号，<br />账号密码验证通过后自动绑定微信号。</p>
          </Col>
        </Row>

        <Form.Item wrapperCol={{ offset: 14 }}>
          <Space>
            <Button onClick={() => {
              setBindVisible(false);
              fetchQRTicket();
            }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              绑定
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
    <Tour open={open} onClose={() => setOpen(false)} steps={steps} />
    <FloatButton
      icon={<QuestionCircleOutlined />}
      type="default"
      onClick={() => setOpen(true)}
    />
  </>
  );
};
