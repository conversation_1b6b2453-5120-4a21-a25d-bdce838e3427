import React, { useRef, useState, useEffect } from 'react';
import './index.less';
import { api, copyObject, dateFormat, constant } from '@/common/utils';
import { RedoOutlined, DownOutlined, RightOutlined, AppstoreOutlined, BarsOutlined, } from '@ant-design/icons';
//新引入
import ContastTable from '@/components/ContastTable'

export default () => {
  const [head, setHead] = useState('运价对比')
  return (
    <>
      <div className='con-title'>
        <div className='con-title-text'>
          <div className='con-head'>
            <span className='con-head-skey'>运价</span>
            <RightOutlined style={{ fontSize: 11, color: '#8C8C8C' }} />
            <span>{head}</span>
          </div>
          <div className='con-head-content'>
            <div className='con-table-title'>运价对比</div>
            <div className='con-table'>
              <ContastTable></ContastTable>
            </div>
          </div>
        </div>
      </div>
    </>
  )
};
