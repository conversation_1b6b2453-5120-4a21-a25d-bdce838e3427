import React, { Fragment, useState, useEffect, useMemo } from 'react';
import { Checkbox, Steps, Button, Flex, Modal, Input, Tooltip } from 'antd';
import { PushpinOutlined, PushpinFilled, CloseOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import { api, dateFormat, copyObject } from '@/common/utils';
import { shipmentIcon } from '@/common/common';
import './index.less'
import Booking from '@/pages/rate/Booking';
import Quote from '@/pages/rate/Quote';

// 预设颜色数组
const COLORS = [
  '#FF6B6B', '#FF9F1C', '#4ECDC4', '#FFD166', '#6A0572',
  '#118AB2', '#06D6A0', '#EF476F', '#073B4C',
  '#7209B7', '#3A86FF', '#FB5607', '#8338EC',
  '#FF9F1C', '#2EC4B6', '#E71D36', '#FF9F1C'
];

export default (props) => {
  const [checkbox1, setcheckbox1] = useState(false);
  const [checkbox2, setcheckbox2] = useState(false);

  // 标题项配置
  const [titleItems, setTitleItems] = useState([
    { label: '', hasNumber: true, isShowCom: true },//0
    { label: '海运费USD', customClass: 'haiyunfei1', isShowCom: true },//1
    { label: '其他RMB', isShowCom: true },//2
    { label: '预计船期ETD', isShowCom: true },//3
    { label: '预计航程', isShowCom: true },//4
    { label: '中转直航', isShowCom: true },//5
    { label: '目的港箱使', isShowCom: true },//6
    // { label: '限重要求', isShowCom: true },//7
    // { label: '品名要求', isShowCom: true },//8
    { label: '签单方式', isShowCom: true },//9
    { label: '备注说明', customClass: 'beizhu1', isShowCom: true },
    { label: '运价类型', isShowCom: true },
    { label: '' },
  ]);

  // 动态生成标题列
  const renderTitleItems = (item, index) => (
    <div className={`border-bottom box-title-item ${item.customClass || ''}`} key={index}>
      {item.hasNumber ?
        <>
          {item.label} <span className='number'>{allData.length}</span> 个运价
        </>
        : item.label}
    </div>
  )

  // 保存原始数据
  const [allData, setAllData] = useState([])

  // 所有列数据 
  const [originalPosiItems, setOriginalPosiItems] = useState([]);

  // 固定列数据配置
  const [fixedItems, setFixedItems] = useState([])

  // 非固定列数据配置
  const [posiItems, setPosiItems] = useState([])

  // 初始化数据
  useEffect(() => {
    const arr = JSON.parse(localStorage.getItem('allData')) || allData
    setAllData(arr)
    setPosiItems(arr)
    setOriginalPosiItems([...arr])
  }, [])

  const [isModalOpen, setIsModalOpen] = useState(false); // 弹窗
  const [price, setPrice] = useState(false); // 报价

  // 打开弹窗
  const showModal = (item) => {
    setRateData([item]);
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  // 价格颜色
  const generateColorMap = useMemo(() => {
    const colorMap = {};
    const allItems = [...fixedItems, ...posiItems];
    const priceColor = allItems.map(item => item.gp20Price);
    const uniquePrices = [...new Set(priceColor)];

    uniquePrices.forEach((price, index) => {
      colorMap[price] = COLORS[index % COLORS.length];
    });

    return colorMap;
  }, [fixedItems, posiItems]);

  // 价格颜色2
  const generateColorMap2 = useMemo(() => {
    const colorMap = {};
    const allItems = [...fixedItems, ...posiItems];
    const priceColor = allItems.map(item => item.gp40Price);

    const uniquePrices = [...new Set(priceColor)];


    uniquePrices.forEach((price, index) => {
      colorMap[price] = COLORS[index % COLORS.length];
    });
    return colorMap;
  }, [fixedItems, posiItems]);

  // 价格颜色3
  const generateColorMap3 = useMemo(() => {
    const colorMap = {};
    const allItems = [...fixedItems, ...posiItems];
    const priceColor = allItems.map(item => item.hc40Price);

    const uniquePrices = [...new Set(priceColor)];


    uniquePrices.forEach((price, index) => {
      colorMap[price] = COLORS[index % COLORS.length];
    });
    return colorMap;
  }, [fixedItems, posiItems]);

  // 价格颜色4
  const generateColorMap4 = useMemo(() => {
    const colorMap = {};
    // 获取所有gp20Price值（包括固定列和非固定列）
    const allItems = [...fixedItems, ...posiItems];
    const priceColor = allItems.map(item => item.hc45Price);

    const uniquePrices = [...new Set(priceColor)];


    uniquePrices.forEach((price, index) => {
      colorMap[price] = COLORS[index % COLORS.length];
    });
    return colorMap;
  }, [fixedItems, posiItems]);

  // 价格颜色5
  const generateColorMap5 = useMemo(() => {
    const colorMap = {};
    // 获取所有gp20Price值（包括固定列和非固定列）
    const allItems = [...fixedItems, ...posiItems];
    const priceColor = allItems.map(item => item.nor40Price);

    const uniquePrices = [...new Set(priceColor)];


    uniquePrices.forEach((price, index) => {
      colorMap[price] = COLORS[index % COLORS.length];
    });
    return colorMap;
  }, [fixedItems, posiItems]);

  // 预计航程颜色
  const generateColorMap6 = useMemo(() => {
    const colorMap = {};
    // 获取所有gp20Price值（包括固定列和非固定列）
    const allItems = [...fixedItems, ...posiItems];
    const AllShipDays = allItems.map(item => item.shipDays);

    const uniquePrices = [...new Set(AllShipDays)];


    uniquePrices.forEach((price, index) => {
      colorMap[price] = COLORS[index % COLORS.length];
    });
    return colorMap;
  }, [fixedItems, posiItems]);

  // 中转直航颜色
  const generateColorMap7 = useMemo(() => {
    const colorMap = {};
    // 获取所有gp20Price值（包括固定列和非固定列）
    const allItems = [...fixedItems, ...posiItems];
    const AllTtOrDts = allItems.map(item => item.ttOrDt);

    const uniquePrices = [...new Set(AllTtOrDts)];

    uniquePrices.forEach((price, index) => {
      colorMap[price] = COLORS[index % COLORS.length];
    });
    return colorMap;
  }, [fixedItems, posiItems]);

  // 目的港箱使颜色
  const generateColorMap8 = useMemo(() => {
    const colorMap = {};
    // 获取所有gp20Price值（包括固定列和非固定列）
    const allItems = [...fixedItems, ...posiItems];
    const AllTtOrDts = allItems.map(item => item.podContainerFreeDays);

    const uniquePrices = [...new Set(AllTtOrDts)];

    uniquePrices.forEach((price, index) => {
      colorMap[price] = COLORS[index % COLORS.length];
    });
    return colorMap;
  }, [fixedItems, posiItems]);

  // 限重要求
  const generateColorMap9 = useMemo(() => {
    const colorMap = {};
    // 获取所有gp20Price值（包括固定列和非固定列）
    const allItems = [...fixedItems, ...posiItems];
    const AllTtOrDts = allItems.map(item => item.weightLimitNote);

    const uniquePrices = [...new Set(AllTtOrDts)];

    uniquePrices.forEach((price, index) => {
      colorMap[price] = COLORS[index % COLORS.length];
    });
    return colorMap;
  }, [fixedItems, posiItems]);

  // 内容超出显示省略号
  const [arrow, setArrow] = useState('Show');
  const mergedArrow = useMemo(() => {
    if (arrow === 'Hide') {
      return false;
    }
    if (arrow === 'Show') {
      return true;
    }
    return {
      pointAtCenter: true,
    };
  }, [arrow]);

  // 动态生成固定内容数据列
  const renderFixedItems = (item, index, fixed) => (
    <div className='box-fixed-item border-right' key={item.id}>
      {
        <>
          <div className='border-bottom fixed-head'>
            <div className='fixed-heads'>
              {fixed == true ?
                <>
                  <div className='fixed-icon1'> <PushpinFilled />已钉住</div>
                  <div className='fixed-icon2' ><CloseOutlined onClick={() => { closeItem(index, 'fixed') }} /></div>
                </> :
                <>
                  <div className='unfixed-icon1' onClick={() => { handleFixedLeft(index) }}> <PushpinOutlined />钉在左侧</div>
                  <div className='fixed-icon2' ><CloseOutlined onClick={() => { closeItem(index, 'posi') }} /></div>
                </>
              }
            </div>
            <div className='fixed-img'>
              <img src={shipmentIcon(item.shipCompany)} alt="" />
              <div className='fixed-text'>{item.shipCompany}</div>
            </div>
            <div className='fixed-steps'>
              <Steps
                progressDot
                current={-1}
                direction="vertical"
                items={[
                  {
                    title: item.polName
                  },
                  {
                    title: item.podName
                  },
                ]}
              />
            </div>
          </div>
          {/* 按钮 */}
          <div className='border-bottom box-title-item anniu'>
            <Flex justify='space-between'>
              {fixed === true ?
                <>
                  <Flex gap={5}>
                    <Button onClick={() => { handleFixedMoveLeft(index) }} disabled={index == 0 ? true : false}><LeftOutlined /></Button>
                    <Button onClick={() => { handleFixedMoveRight(index) }} disabled={index == fixedItems.length - 1 ? true : false}><RightOutlined /></Button>
                  </Flex>
                  <Button className='copy' onClick={() => { showModal(item) }}>复制报价</Button>
                </> :
                <>
                  <Flex gap={5}>
                    <Button onClick={() => { handlePosiMoveLeft(index) }} disabled={index == 0 ? true : false}><LeftOutlined /></Button>
                    <Button onClick={() => { handlePosiMoveRight(index) }} disabled={index == posiItems.length - 1 ? true : false}><RightOutlined /></Button>
                  </Flex>
                  <Button className='copy' onClick={() => { showModal(item) }}>复制报价</Button>
                </>

              }

            </Flex>
          </div>

          {/* 海运费 */}
          <div className='border-bottom box-title-item haiyunfei2'>
            <div style={isGp20 == false ? { display: 'block' } : { display: 'none' }}>20GP:
              <span
                style={isHighLight == true && item.gp20Price > 0 ? {
                  color: generateColorMap[item.gp20Price],
                  fontWeight: 600,
                  paddingLeft: '4px'
                } : {}}
              >
                &nbsp;
                ${item.gp20Price}
              </span>
            </div>
            <div style={isGp40 == false ? { display: 'block' } : { display: 'none' }}>40GP:
              <span
                style={isHighLight == true && item.gp40Price > 0 ? {
                  color: generateColorMap2[item.gp40Price],
                  fontWeight: 600,
                  paddingLeft: '4px'
                } : {}}
              >
                &nbsp;
                ${item.gp40Price}
              </span>
            </div>
            <div style={isHc40 == false ? { display: 'block' } : { display: 'none' }}>40HC:
              <span
                style={isHighLight == true && item.hc40Price > 0 ? {
                  color: generateColorMap3[item.hc40Price],
                  fontWeight: 600,
                  paddingLeft: '4px'
                } : {}}
              >
                &nbsp;
                ${item.hc40Price}
              </span>
            </div>
            <div style={isHc45 == false ? { display: 'block' } : { display: 'none' }}>45HC:
              <span
                style={isHighLight == true && item.hc45Price > 0 ? {
                  color: generateColorMap4[item.hc45Price],
                  fontWeight: 600,
                  paddingLeft: '4px'
                } : {}}
              >
                &nbsp;
                ${item.hc45Price}
              </span>
            </div>
            <div style={isNor40 == false ? { display: 'block' } : { display: 'none' }}>40NOR:
              <span
                style={isHighLight == true && item.nor40Price > 0 ? {
                  color: generateColorMap5[item.nor40Price],
                  fontWeight: 600,
                  paddingLeft: '4px'
                } : {}}
              >
                &nbsp;
                ${item.nor40Price}
              </span>
            </div>
          </div>
          {/* 其他RMB */}
          <div className='border-bottom box-title-item white-style other'>--</div>
          {/* 预计船期ETD */}
          <div className='border-bottom box-title-item white-style' style={isTime == false ? { display: 'block' } : { display: 'none' }}>{dateFormat(item.etd, 'yyyy-MM-dd')}</div>
          {/* 预计航程 */}
          <div className='border-bottom box-title-item white-style' style={isShipDays == false ? { display: 'block' } : { display: 'none' }}>
            <span style={isHighLight == true ? {
              color: generateColorMap6[item.shipDays],
              fontWeight: 600,
              paddingLeft: '4px'
            } : {}}>{item.shipDays}</span>
          </div>
          {/* 中转直航 */}
          <div className='border-bottom box-title-item white-style' style={isShow == false ? {} : { display: 'none' }}>
            <span style={isHighLight == true ? {
              color: generateColorMap7[item.ttOrDt],
              fontWeight: 600,
              paddingLeft: '4px'
            } : {}}>{item.ttOrDt}</span>
          </div>
          {/* 目的港箱使 */}
          {/* <div className='border-bottom box-title-item'>{item.podContainerFreeDays}</div> */}
          <Tooltip placement="bottom" title={item.podContainerFreeDays} arrow={mergedArrow}>
            <div className='border-bottom box-title-item white-style' style={isUse == false ? {} : { display: 'none' }} >
              <span style={isHighLight == true ? {
                color: generateColorMap8[item.podContainerFreeDays],
                fontWeight: 600,
                paddingLeft: '4px'
              } : {}}>{item.podContainerFreeDays}</span>
              {/* } : {}}>1个柜子申请12天，2-4个柜子申请 14天试试，以最终批复为准，大票单独确认</span> */}
            </div>
          </Tooltip>
          {/* 限重要求 */}
          {/* <Tooltip placement="bottom" title={item.weightLimitNote} arrow={mergedArrow}>
            <div className='border-bottom box-title-item white-style' style={isWeight == false ? {} : { display: 'none' }}>
              <span style={isHighLight == true ? {
                color: generateColorMap9[item.weightLimitNote],
                fontWeight: 600,
                paddingLeft: '4px'
              } : {}}>{item.weightLimitNote}</span>
            </div>
          </Tooltip> */}
          {/* 品名要求 */}
          {/* <div className='border-bottom box-title-item'></div> */}
          {/* 签单方式 */}
          <div className='border-bottom box-title-item white-style'>--</div>
          {/* 备注说明 */}
          <div className='border-bottom box-title-item beizhu2'>{item.bookNote}</div>
          {/* 运价类型 */}
          <div className='border-bottom box-title-item'>
            {item.priceType == 0 ? '线下运价' : '线上运价'}
          </div>
          <div className='border-bottom box-title-item white-style xiadan'><Button size='small' type="primary" onClick={() => { onBooking(item) }}>立即下单</Button></div>

        </>
      }
    </div >
  )

  // 下单
  const [rateData, setRateData] = useState([]);
  const [isBookingOpen, setIsBookingOpen] = useState(false); // 下单
  const shipScheduleCnMap = { '周日': "0", "周一": "1", "周二": "2", "周三": "3", "周四": "4", "周五": "5", "周六": "6", };

  const onBooking = (data) => {
    // console.log(data, '下单数据')
    let bookData = {};
    copyObject(bookData, {
      runDate: data.effectiveDateStr, pol: data.pol, pod: data.pod, polEnName: data.polEnName, podEnName: data.podEnName, ttOrDt: data.ttOrDt, ttPol: data.ttPol,
      polId: data.polId, podId: data.podId, shipCompanyId: data.shipCompanyId, shipCompany: data.shipCompany,
        gp20Price: data.gp20Price, gp40Price: data.gp40Price, hc40Price: data.hc40Price, etd: data.etd,
      otherPrice: data.nor40Price, otherPriceOne: data.hc45Price,
      vessel: data.vessel, voyage: data.voyage, shipDays: data.shipDays, shipSchedule: shipScheduleCnMap[data.shipSchedule], rateType: '线下'
    });
    setRateData(bookData);
    setIsBookingOpen(true);
  }

  // 钉在左侧
  const handleFixedLeft = (index) => {
    const newPosiArr = [...posiItems]
    const removeItem = newPosiArr.splice(index, 1)[0]
    const fixedArr = [...fixedItems, removeItem]
    setFixedItems(fixedArr)
    setPosiItems(newPosiArr)
    setOriginalPosiItems(newPosiArr)
  }

  // 关闭
  const closeItem = (index, type) => {
    let arr1 = [...fixedItems]
    let arr2 = [...posiItems]
    let allArray = []
    if (type == 'fixed') {
      arr1.splice(index, 1)
      setFixedItems(arr1)
    } else {
      arr2.splice(index, 1)
      setPosiItems(arr2)
      setOriginalPosiItems(arr2)
    }
    allArray = [...arr1, ...arr2]
    setAllData(allArray)
    localStorage.setItem('allData', JSON.stringify(allArray))
    localStorage.setItem('compareCount', allArray.length)
  }

  const [isFliter, setIsFliter] = useState(false)

  // 高亮-隐藏
  const [isHighLight, setIsHighLight] = useState(false) //高亮


  const [isShow, setIsShow] = useState(false) // 中转直航-隐藏
  const [isUse, setIsUse] = useState(false) // 目的港箱使-隐藏
  const [isTime, setIsTime] = useState(false) // 预计船期ETD-隐藏
  const [isWeight, setIsWeight] = useState(false) // 预计船期ETD-隐藏
  const [isShipDays, setIsShipDays] = useState(false) // 预计航程-隐藏
  const [isGp20, setIsGp20] = useState(false) // 20Gp-隐藏
  const [isGp40, setIsGp40] = useState(false) // 40Gp-隐藏
  const [isHc40, setIsHc40] = useState(false) // 40HC-隐藏
  const [isHc45, setIsHc45] = useState(false) // 45HC-隐藏
  const [isNor40, setIsNor40] = useState(false) // 40NOR-隐藏


  const onChange = (e, type) => {
    if (type == 'highlight') {
      setIsHighLight(e.target.checked)
    } else {
      console.log(type)
      const allItems = [...fixedItems, ...posiItems];

      // 获取数据
      const AllTtOrDts = allItems.map(item => item.ttOrDt); //中转直航
      const AllEtds = allItems.map(item => item.etd); //预计时间
      const AllCons = allItems.map(item => item.podContainerFreeDays); //目的港厢式
      const AllShipDays = allItems.map(item => item.shipDays); //预计航程
      // const AllWeight = allItems.map(item => item.weightLimitNote); //限重要求
      const AllGp20Prices = allItems.map(item => item.gp20Price); //20GP
      const AllGp40Prices = allItems.map(item => item.gp40Price); //40GP
      const AllHc40Prices = allItems.map(item => item.hc40Price); //40HC
      const AllHc45Prices = allItems.map(item => item.hc45Price); //45HC
      const AllNor40Prices = allItems.map(item => item.nor40Price); //40NOR

      // 判断各项是否相同
      const allSame = AllTtOrDts.every((item, index, array) => item === array[0]);
      const allSame2 = AllEtds.every((item, index, array) => item === array[0]);
      const allSame3 = AllCons.every((item, index, array) => item === array[0]);
      const allSame4 = AllShipDays.every((item, index, array) => item === array[0]);
      const allSame5 = AllGp20Prices.every((item, index, array) => item === array[0]);
      const allSame6 = AllGp40Prices.every((item, index, array) => item === array[0]);
      const allSame7 = AllHc40Prices.every((item, index, array) => item === array[0]);
      const allSame8 = AllHc45Prices.every((item, index, array) => item === array[0]);
      const allSame9 = AllNor40Prices.every((item, index, array) => item === array[0]);
      // const allSame10 = AllWeight.every((item, index, array) => item === array[0]);


      const titleArrs = [...titleItems]



      if (allSame == true) { //中转直航
        if (e.target.checked == true) {

          titleArrs[5].isShowCom = !allSame
        } else {
          titleArrs[5].isShowCom = allSame
        }
        setIsShow(e.target.checked)
      } else {
        titleArrs[5].isShowCom = !allSame
      }
      // if (allSame10 == true) { //限重要求
      //   if (e.target.checked == true) {

      //     titleArrs[7].isShowCom = !allSame10
      //   } else {
      //     titleArrs[7].isShowCom = allSame10
      //   }
      //   setIsWeight(e.target.checked)
      // } else {
      //   titleArrs[7].isShowCom = !allSame10
      // }

      if (allSame2 == true) {
        if (e.target.checked == true) {

          titleArrs[3].isShowCom = !allSame2
        } else {
          titleArrs[3].isShowCom = allSame2
        }
        setIsTime(e.target.checked)
      } else {
        titleArrs[3].isShowCom = !allSame2
      }

      if (allSame3 == true) {
        if (e.target.checked == true) {
          titleArrs[6].isShowCom = !allSame3
        } else {
          titleArrs[6].isShowCom = allSame3
        }
        setIsUse(e.target.checked)
      } else {
        titleArrs[6].isShowCom = !allSame3
      }

      if (allSame4 == true) {
        if (e.target.checked == true) {
          titleArrs[4].isShowCom = !allSame4
        } else {
          titleArrs[4].isShowCom = allSame4
        }
        setIsShipDays(e.target.checked)
      } else {
        titleArrs[4].isShowCom = !allSame4
      }

      if (allSame5 == true) {
        setIsGp20(e.target.checked)
      }
      if (allSame6 == true) {
        setIsGp40(e.target.checked)
      }
      if (allSame7 == true) {
        setIsHc40(e.target.checked)
      }
      if (allSame8 == true) {
        setIsHc45(e.target.checked)
      }
      if (allSame9 == true) {
        setIsNor40(e.target.checked)
      }

      //更改显示隐藏状态
      console.log(titleArrs, 'titleArrs')
      setTitleItems(titleArrs)

    }
  };


  // 固定列向左移动
  const handleFixedMoveLeft = (index) => {
    const arr = [...fixedItems]
    const targetIndex = index - 1
    if (targetIndex < 0) return
    [arr[index], arr[targetIndex]] = [arr[targetIndex], arr[index]]
    setFixedItems(arr)
  }
  // 固定列向右移动
  const handleFixedMoveRight = (index) => {
    const arr = [...fixedItems]
    const targetIndex = index + 1
    if (targetIndex < 0) return
    [arr[index], arr[targetIndex]] = [arr[targetIndex], arr[index]]
    setFixedItems(arr)
  }
  // 非固定列向左移动
  const handlePosiMoveLeft = (index) => {
    const arr = [...posiItems]
    const targetIndex = index - 1
    if (targetIndex < 0) return
    [arr[index], arr[targetIndex]] = [arr[targetIndex], arr[index]]
    setPosiItems(arr)
  }
  // 非固定列向右移动
  const handlePosiMoveRight = (index) => {
    const arr = [...posiItems]
    const targetIndex = index + 1
    if (targetIndex < 0) return
    [arr[index], arr[targetIndex]] = [arr[targetIndex], arr[index]]
    setPosiItems(arr)
  }


  return (
    <>
      {allData.length > 0 ?
        <div className='box-con border-left'>
          {/* 标题 */}
          <div className='box-title border-right'>
            <div className='border-bottom box-title-item head'>
              <div>共选择 <span className='number'>{allData.length}</span> 个运价 </div>
              <div className='box-radio'>
                <Checkbox onChange={(e) => { onChange(e, 'highlight') }}>高亮不同项</Checkbox>
              </div>
              <div className='box-radio'>
                <Checkbox onChange={(e) => { onChange(e, 'hide') }}>隐藏相同项</Checkbox>
              </div>
            </div>
            <div className='border-bottom box-title-item anniu'></div>
            {titleItems.map((item, index) => (
              <Fragment key={index}>
                {index === 0 || item.isShowCom == false ? null : renderTitleItems(item, index)}
              </Fragment>
            ))}
          </div>
          <>
            <Quote
              open={isModalOpen}
              onCancel={() => setIsModalOpen(false)}
              data={rateData}
              rateType='线下'
            />
          </>
          {/* 固定列 */}
          <div className={`box-fixed   ${fixedItems.length > 0 ? 'fixed-minWidth box-unfixed overflow-x' : ''} `}>
            {fixedItems ?
              fixedItems.map((item, index) => (
                <Fragment key={index}>
                  {renderFixedItems(item, index, true)}
                </Fragment>
              )) : null}
          </div>
          {/* 不固定列 */}
          <div className={`box-fixed border-right box-unfixed ${posiItems.length > 0 ? 'fixed-minWidth overflow-x' : ''}`}>
            {posiItems ?
              posiItems.map((item, index) => (
                <Fragment key={index}>
                  {renderFixedItems(item, index, false)}
                </Fragment>
              )) : null}
          </div>
          {/* 下单弹窗 */}
          <Booking
            current={rateData}
            open={isBookingOpen}
            onCancel={() => setIsBookingOpen(false)}
            onSubmit={() => {
              setIsBookingOpen(false);
            }}
          />
        </div> :
        <div className='empty'>暂无数据</div>
      }
    </>

  )
}