.box-con {
    font-size: 12px;
    font-weight: 500;
    border-radius: 5px;
    display: flex;
    min-height: 300px;

    .box-title {
        width: 174px;
    }

    .box-title-item {
        min-width: 174px;
        min-height: 47px;
        line-height: 47px;
        padding-left: 20px;
        padding-right: 5px;
        font-weight: 500;
        font-size: 13px;
        color: #666666;
        text-align: left;


    }

    .white-style {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .anniu {
        height: 52px;
        box-sizing: border-box;
        padding: 10px;
        background: #f9f9f9;
    }

    .copy {
        color: #fff;
        background: #B4141B;
    }

    .head {
        height: 200px;
        line-height: 15px;
        padding-top: 10px;
        box-sizing: border-box;
        background: #f9f9f9;

        .number {
            color: red;
            font-weight: 600;
        }

        .box-radio {
            margin-top: 10px;
        }
    }

    .fixed-head {
        height: 200px;
        width: 230px;
        padding-right: 15px;
        box-sizing: border-box;
        background: #f9f9f9;

        .fixed-heads {
            height: 20px;
            display: flex;
            justify-content: space-between;
            cursor: pointer;
        }

        .fixed-icon1 {
            width: 60px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            background: #dfdcdc;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            font-style: normal;
        }

        .unfixed-icon1 {
            width: 78px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            background: #F9EDED;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #B23B34;
            font-style: normal;
            border: 1px solid #FFD6DC;
        }

        .fixed-icon2 {
            width: 20px;
            height: 20px;
            // line-height: 20px;
            text-align: center;
            color: #9f9898;
            font-size: 14px;
        }

        .fixed-img {
            margin-top: 15px;
            display: flex;

            img {
                width: 48px;
                height: 45px;
                margin: 0 10px 0 15px;
            }

            .fixed-text {
                line-height: 45px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 14px;
                color: #333333;
                text-align: left;
                font-style: normal;
            }
        }

        .fixed-steps {
            padding: 6px 0 0 13px;

            .ant-steps-item-title {
                font-size: 12px;
                line-height: 20px;
                color: #5c5959 !important;
            }
        }

    }

    .haiyunfei1 {
        height: 113px;
        // line-height: 100px;
        line-height: 112px;
        padding-top: 15px;

        .price-huafei {
            color: #B4141B;
            font-weight: 600;
        }
    }

    .haiyunfei2 {
        height: 113px;
        line-height: 20px;
        padding-top: 15px;

        .price-huafei {
            color: #B4141B;
            font-weight: 600;
        }
    }

    .other {
        color: #B4141B;
    }

    .beizhu1 {
        height: 90px;
        line-height: 80px;
        padding: 0 0 0 20px;
    }

    .beizhu2 {
        height: 90px;
        line-height: 18px;
        padding: 5px 15px;
        overflow: scroll;
        box-sizing: border-box;
    }

    .xiadan {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .box-fixed {
        // width: 800px;
        display: flex;

        .box-fixed-item {
            width: 230px;
        }
    }

    .overflow-x {
        overflow-x: scroll;
    }

    .fixed-minWidth {
        min-width: 231px;
    }

    .box-unfixed {
        // width: 800px;
        display: flex;
        max-width: calc(1200px - 30px - 147px);

        .box-fixed-item {
            width: 230px;
        }
    }
}

.border-right {
    border-right: 1px solid #F0F0F0;
}

.border-left {
    border-left: 1px solid #F0F0F0;
}

.border-bottom {
    border-bottom: 1px solid #F0F0F0;
}

.border-top {
    border-top: 1px solid #F0F0F0;
}

.box-color {
    background: #F0F0F0;
}

.empty {
    text-align: center;
    line-height: 500px;
    color: #8d8d8d;
    min-height: calc(100vh - 250px);
}