import { ipost, iput, iget, iupload, constant, idelete } from '@/common/utils';

export function chat(c) {
    return ipost(`${constant.API_AI_PRICE}`, c)
}

export function pdf(form) {
    return iupload(constant.API_AI_PDF, form);
}

export function newChat(model) {
    return ipost(constant.API_AI_NEW_CHAT, { model: model });
}

export function deleteChat(id) {
    return idelete(`${constant.API_AI}?id=${id}`);
}

export function chatDetail(id) {
    return iget(`${constant.API_AI_CHAT_DETAIL}?id=${id}`);
}

export function chatHistory() {
    return iget(`${constant.API_AI_CHAT_HISTORY}`);
}

export function rename(vo) {
    return ipost(`${constant.API_AI_RENAME}`, vo);
}