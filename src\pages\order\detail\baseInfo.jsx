import React, { useState, useEffect } from 'react';
import { Descriptions, Table } from 'antd';
import { api, dateFormat, } from '@/common/utils';
import DICTIONARY from '@/common/dictionary';

export default (props) => {
    const { orderInfo, orderId, } = props;
    
    const [signWayCodeDic, setSignWayCodeDic] = useState({});

    useEffect(() => {
        api.dict.listMapByParentCode(DICTIONARY.DICT_SIGNWAY_TYPE_TAG).subscribe({
            next: (data) => {
                setSignWayCodeDic(data);
            }
        })
    }, []);

    const columns = [
        {
            title: 'No.',
            width: 50,
            align: 'center',
            dataIndex: '',
            render: (text, record, index) => <>{index + 1}</>,
        },
        {
            title: '箱号',
            dataIndex: 'containerNo',
            width: 120,
        },
        {
            title: '箱型',
            align: 'center',
            dataIndex: 'containerType',
        },
        {
            title: '铅封',
            align: 'center',
            dataIndex: 'sealNo',
        },
        {
            title: '件数',
            align: 'center',
            dataIndex: 'cargoTotalQty',
        },
        {
            title: '包装',
            align: 'center',
            dataIndex: 'cargoPkgName',
        },
        {
            title: '重量',
            align: 'center',
            dataIndex: 'cargoTotalWeight',
        },
        {
            title: '尺码',
            align: 'center',
            dataIndex: 'cargoTotalSize',
        },
        {
            title: '备注',
            dataIndex: 'note',
            width: 120,
        },
    ];

    // useEffect(() => {
    //     console.info(props.orderId)
    //     console.info(props.open)
    //     setOrderInfo({});
    //     if (props.orderId && props.open) {
    //         getOrderInfo(props.orderId);
    //     }

    // }, [props.orderId, props.open]);

    return <>
        <Descriptions title="基本信息" column={4}>
            <Descriptions.Item label="提单号">{orderInfo.blNo}</Descriptions.Item>
            <Descriptions.Item label="开船日期">{dateFormat(orderInfo.atd)}</Descriptions.Item>
            <Descriptions.Item label="船名航次" span={2}>{orderInfo.vessel} / {orderInfo.voyage}</Descriptions.Item>

            <Descriptions.Item label="船公司">{orderInfo.shipCompany}</Descriptions.Item>
            <Descriptions.Item label="装货港">{orderInfo.polEnName}</Descriptions.Item>
            <Descriptions.Item label="卸货港">{orderInfo.podEnName}</Descriptions.Item>
            <Descriptions.Item label="航程">{orderInfo.shipDays || '-'}天</Descriptions.Item>

            <Descriptions.Item label="船代">{orderInfo.entrustShipCompany}</Descriptions.Item>
            <Descriptions.Item label="场站">{orderInfo.boxDepot}</Descriptions.Item>
            <Descriptions.Item label="截单时间">{dateFormat(orderInfo.cot, 'yyyy-MM-dd hh:mm')}</Descriptions.Item>
            <Descriptions.Item label="截港时间">{dateFormat(orderInfo.dot, 'yyyy-MM-dd hh:mm')}</Descriptions.Item>

            <Descriptions.Item span={4} />
        </Descriptions>

        <Descriptions column={3} layout="vertical">
            <Descriptions.Item label="发货人" contentStyle={{ fontSize: '12px', marginBottom: '20px' }}>
                {orderInfo.shipper}
            </Descriptions.Item>
            <Descriptions.Item label="收货人" contentStyle={{ fontSize: '12px', marginBottom: '20px' }}>
                {orderInfo.consignee}
            </Descriptions.Item>
            <Descriptions.Item label="通知人" contentStyle={{ fontSize: '12px', marginBottom: '20px' }}>
                {orderInfo.notify}
            </Descriptions.Item>
        </Descriptions>

        <Descriptions title="箱货信息" column={4}>
            <Descriptions.Item label="箱型箱量"> {orderInfo.boxInfo}</Descriptions.Item>
            <Descriptions.Item label="包装件数"> {orderInfo.cargoPkgs + ' ' + (orderInfo.cargoUnits || '')}</Descriptions.Item>
            <Descriptions.Item label="货物总量"> {orderInfo.cargoWeight} KGS</Descriptions.Item>
            <Descriptions.Item label="货物尺码"> {orderInfo.cargoSize} CBM</Descriptions.Item>
        </Descriptions>
        <Table
            size='small'
            columns={columns}
            dataSource={orderInfo.containers || []}
            scroll={{ y: 200 }}
            pagination={false}
            style={{ margin: '20px 0', border: '1px solid #f0f0f0', borderRadius: '5px' }}
        />
        <Descriptions title="提单信息" column={3}>
            <Descriptions.Item label="签单方式">{signWayCodeDic[orderInfo.signWayCode]}</Descriptions.Item>
            <Descriptions.Item label="签单时间">{dateFormat(orderInfo.billSignDate)}</Descriptions.Item>
            <Descriptions.Item label="签单地点" span={2}>{orderInfo.billSignPlace} </Descriptions.Item>
        </Descriptions>
    </>
}