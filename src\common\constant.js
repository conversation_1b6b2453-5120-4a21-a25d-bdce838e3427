//= ==============================================//
//                 SYSTEM CONST                  //
//= ==============================================//
export default Object.freeze({

  //FASTDFS前缀
  FDFS_PREFIX: 'http://www.jzbz.com/',

  //初始起运港
  INIT_POLS: [
    {
      latitude: 35.3537, longitude: 119.3012, enName: 'QINGDAO,CN', cnName: '青岛 | 中国', portEdi: 'CNTAO', label: 'QINGDAO,CN 青岛 | 中国', value: 'CNTAO',
      item: {
        cnName: "青岛", countryCode: "CN", countryName: "中国", enName: "QINGDAO", portEdi: "CNTAO"
      }
    },
    {
      latitude: 31.230416, longitude: 121.4737, enName: 'SHANGHAI,CN', cnName: '上海 | 中国', portEdi: 'CNSHA', label: 'SHANGHAI,CN 上海 | 中国', value: 'CNSHA',
      item: {
        cnName: "上海", countryCode: "CN", countryName: "中国", enName: "SHANGHAI", portEdi: "CNSHA"
      }
    },
    {
      latitude: 22.543096, longitude: 114.057865, enName: 'SHENZHEN,CN', cnName: '深圳 | 中国', portEdi: 'CNSZX', label: 'SHENZHEN,CN 深圳 | 中国', value: 'CNSZX',
      item: {
        cnName: "深圳", countryCode: "CN", countryName: "中国", enName: "SHENZHEN", portEdi: "CNSZX"
      }
    },
    {
      latitude: 29.868336, longitude: 121.54399, enName: 'NINGBO,CN', cnName: '宁波 | 中国', portEdi: 'CNNGB', label: 'NINGBO,CN 宁波 | 中国', value: 'CNNGB',
      item: {
        cnName: "宁波", countryCode: "CN", countryName: "中国", enName: "NINGBO", portEdi: "CNNGB"
      }
    },
    {
      latitude: 39.084158, longitude: 117.200983, enName: 'TIANJIN,CN', cnName: '天津 | 中国', portEdi: 'CNTSN', label: 'TIANJIN,CN 天津 | 中国', value: 'CNTSN',
      item: {
        cnName: "天津", countryCode: "CN", countryName: "中国", enName: "TIANJIN", portEdi: "CNTSN"
      }
    },
  ],

  REGEX_EMAIL: /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]|(\w|\-)+\.([a-zA-Z]{2,4})$/,
  REGEX_MOBILE: /^1[3456789]\d{9}$/,

  // 登录路径
  ROUTE_LOGIN: '/login',
  //授权码登录
  ROUTE_LOGIN_TEMP: '/user/loginTemp',
  // 用户按钮KEY-sessionStorage
  KEY_USER_BUTTON_PERMS: '_USER_BUTTON_PERMS',
  // 用户token存储KEY-sessionStorage
  KEY_USER_TOKEN: 'ODM_USER_TOKEN',
  //系统标识
  KEY_USER_SYSTEM_TAG: '_USER_SYSTEM_TAG',
  // 当前用户
  KEY_CURRENT_USER: '_CURRENT_USER',
  //词典数据缓存
  KEY_DICT: 'ODM_DICT_',
  //用户数据缓存
  KEY_USER: 'ODM_USER',
  //租户数据缓存
  KEY_TENANT: 'ODM_TENANT',
  //头像名称
  SYSTEM_AVATAR_NAME: 'ODM',
  // 登录路径
  SYSTEM_ROUTE_LOGIN: '/user/login',

  // 缓存超时时间
  TIME_OUT_FOR_CACHE: 30,
  // localstorage key前缀
  PREFIX_OF_CACHE: '_dsi_',

  // 登录
  API_LOGIN: '/authority/oauth/token',
  // 登录
  API_GROUP: '/authority/group',
  // 登出
  API_LOGOUT: '/authority/oauth/logout',
  //用户
  API_USER: '/authority/user',
  //根据账号获取用户
  API_USER_GET_BY_CODE: '/authority/user/getByCode',
  // 当前登录用户信息
  API_USER_CURRENT: '/authority/user/currentUser',
  // 用户权限菜单
  API_USER_MENUS: '/authority/user/listMenus',
  // 用户权限按钮
  API_USER_BUTTONS: '/authority/user/listButtons',
  // 用户更新自己的个人信息
  API_USER_UPDATE_SELF: '/authority/user/updateSelfUser',
  // 用户组织列表
  API_USER_DEPARTMENTS: '/authority/group/userDepartments',
  // 用户切换组织
  API_USER_CHANGE_DEPARTMENT: '/authority/group/change',
  //验证用户并获取组织
  API_USER_VALIDATE_FOR_USER_DEPARTMENTS: '/authority/user/validateUserForDepartments',
  // 邮件验证
  API_USER_EMAIL_VALIDATE: '/authority/user/validateEmail',
  //获取所有TAG对应的组织列表和用户
  API_USER_TREE_ALL_GROUPS_AND_USERS_BY_TAG: '/authority/group/treeAllGroupsAndUsersByTag',
  // 注册用户消息
  API_SSE_REGISTER: '/authority/sse/register',
  // 订阅用户消息
  API_SSE_SUBSCRIBE: '/authority/sse/subscribe',
  // 取消订阅用户消息
  API_SSE_UNSUBSCRIBE: '/authority/sse/unsubscribe',
  // 发送消息
  API_SSE_SEND: '/authority/sse/send',

  //发送短信验证码
  API_USER_SEND_SMS: '/authority/register/sendSms',
  //注册
  API_USER_REGISTER: '/authority/register/register',
  //提交
  API_USER_SUBMIT: '/authority/register/commit',
  //撤销提交
  API_USER_CANCEL: '/authority/register/cancel',
  //= ==============================================//
  //                   微信相关                      //
  //= ==============================================//
  //微信获取二维码
  API_WEIXIN_FETCH_QR_TICKET: '/authority/weixin/fetchQRTicket',
  //微信获取扫码状态
  API_WEIXIN_FETCH_QR_RESULT: '/authority/weixin/fetchQRResult',
  //绑定
  API_WEIXIN_BIND_USER: '/authority/weixin/bindWeixinUser',

  //文件服务
  // FILE_URL: 'http://files.dsi-log.cn:2020/',
  FILE_URL: 'https://files.dsi-log.net/',

  // 线下运价
  API_FREIGHT_PRICE: '/business/freightPrice',
  // 线上运价
  API_FREIGHT_PRICE_ONLINE: '/business/freightPriceOnline',
  // 订单
  API_BOOK_ORDER: '/business/shipBook',
  // 港口
  API_PORT: '/business/port',
  // 船司
  API_SHIPCOMPANY: '/business/shipCompany',
  // 航线
  API_LINE: '/business/line',
  // 提单
  API_ORDER: '/business/order',
  // 其他
  API_BUSINESS: '/business/business',
  // OSS
  API_OSS: '/business/oss',
  // 提单费用
  API_ORDER_FEE: '/business/orderFee',
  // 账单
  API_CHECK_BILL: '/business/checkingBill',
  // 发票申请
  API_INVOICE_APPLY: '/business/invoiceApply',
  // 发票记录
  API_INVOICE_RECORD: '/business/invoiceRecord',
  //文件下载
  API_DOWNLOAD_FILE: '/authority/fileManage/download',
  //字典
  API_DICT_CHILD_LIST: '/authority/dictionaryChild/listByParentCode',
  //报价方案
  API_FREIGHT_PRICE_PLAN: '/business/freightPricePlan',
  //查询历史
  API_FREIGHT_PRICE_SEARCH_HISTORY: '/business/freightPriceSearchHistory',
  //AI
  API_AI_PRICE: '/authority/ai/price',
  //PDF
  API_AI_PDF: '/authority/ai/pdf',
  //创建AI会话
  API_AI_NEW_CHAT: '/authority/ai/newChat',
  //AI
  API_AI: '/authority/ai',
  //AI 聊天内容
  API_AI_CHAT_DETAIL: '/authority/ai/chatDetail',
  //AI 聊天历史
  API_AI_CHAT_HISTORY: '/authority/ai/history',
  //AI 重命名
  API_AI_RENAME: '/authority/ai/rename',
  // 列设置
  API_COLUMN: '/business/columnSetting',
});
