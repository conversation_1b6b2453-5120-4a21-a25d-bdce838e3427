html,
body {
    margin: 0;
    padding: 0;
    font-size: 14px;
    background: #F5F5F5;
}

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background-color: #fff;
}

::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background-color: #ccc;
}

.ant-table-expand-icon-col {
    width: 24px !important;
}

.ant-table-row-expand-icon-cell {
    padding: 16px 6px !important;
}

.za-header {
    width: 100%;
    height: 56px;
    min-width: 1200px;
    background: url('./assets/header-bg.png') repeat-y top center #262626;
    position: fixed;
    top: 0;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.25);
    z-index: 999;

    .za-header-container {
        width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;

        .za-header-logo {
            width: 220px;

            img {
                width: 200px;
                height: 56px;
            }
        }

        .za-header-menu {
            width: 90%;
            display: flex;
            justify-content: end;

            .menu-item {

                a {
                    display: block;

                    color: rgba(255, 255, 255, 0.6);
                    text-decoration: none;
                    padding: 5px 16px;
                    margin: 12px;
                }

                a.active {
                    background: #B4141B;
                    color: #fff;
                    border-radius: 4px;
                }

                a:hover {
                    color: #fff;
                }
            }

        }

        .za-header-user {
            display: flex;
            padding: 14px 0;
            margin-left: 20px;

            .lang-item {
                padding: 0 10px;
            }

            .user-item {
                width: 135px;
                display: flex;
                justify-content: end;
                overflow: hidden;
                user-select: none;
                cursor: pointer;

                img {
                    width: 28px;
                    height: 28px;
                    display: block;
                }

                span {
                    display: block;
                    color: #ccc;
                    line-height: 28px;
                    padding: 0 10px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

            }
        }
    }

}

.za-content {
    width: 100%;
    margin-top: 56px;
    min-height: calc(100vh - 111px);
}

.za-footer {
    background: #303030;
    border-top: #B4141B 5px solid;

    .copyright {
        font-size: 12px;
        line-height: 50px;
        color: #f6f6f6;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.com-content {
    width: 1200px;
    margin: 0 auto;
}

.odm-modal {
    .ant-spin-blur {
        opacity: 1
    }
}

.ant-table-body{
    overflow-y: auto !important;
}

// 弹窗
.odm-modal {
    
    //loading 样式
    .ant-spin-blur {
      opacity: 1
    }
  
  }