import React, { useState } from 'react';
import { Form, Row, Col, message, Button, Pagination, Table, Select, Tag, Input, Space, Spin, Modal } from 'antd';
import './index.less';
import Menu from '../../components/Menu';
import { api, dateFormat, constant } from '@/common/utils';
import objectAssign from 'object-assign';

export default () => {
  const [searchForm] = Form.useForm();
  const [datas, setDatas] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setLoading] = useState(false);

  const [addForm] = Form.useForm();
  const [addLoading, setAddLoading] = useState(false);
  const [showAdd, setShowAdd] = useState(false);
  const [current, setCurrent] = useState(false);

  const tags = {
    'UNACTIVE': { color: 'default', text: '未激活' },
    'LOCKED': { color: 'reject', text: '已锁定' },
    'ACTIVE': { color: 'success', text: '已启用' },
    'c': { color: 'error', text: '已停用' },
  };


  const columns = [
    { title: '状态', dataIndex: 'state', width: 80, render: (text, record) => <> <Tag bordered={false} color={tags[text]?.color}>{tags[text]?.text}</Tag></> },
    { title: '账号', dataIndex: 'userName', width: 80, ellipsis: true, },
    { title: '姓名', dataIndex: 'userRealCnName', width: 100, ellipsis: true, },
    // { title: '性别', dataIndex: 'userSex', width: 60, ellipsis: true, },
    { title: '手机号', dataIndex: 'userMobile', width: 100, ellipsis: true, },
    { title: '邮箱', dataIndex: 'userEmail', width: 120, ellipsis: true, },
    { title: '创建时间', dataIndex: 'createTime', width: 120, render: (text, record) => <>{dateFormat(record.createTime, 'yyyy-MM-dd hh:mm')}</> },

  ]


  const onSearch = (pageNo, pageSize) => {
    setSearchLoading(true);
    setPageNo(pageNo);
    setPageSize(pageSize);
    let searchData = searchForm.getFieldValue();
    let param = { dto: searchData, pageNo: pageNo, pageSize: pageSize };
    api.system.searchTenantUser(param).subscribe({
      next: (data) => {
        setTotal(data.total);
        setDatas(data.data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };


  const onShowSizeChange = (current, pageSize) => {
    onSearch(current, pageSize);
  };

  const onSelectChange = (selectedKeys, selectedRows) => {
    setSelectedRowKeys(selectedKeys);
  };

  const active = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请至少选择一条数据!");
      return;
    }
    setLoading(true);
    api.system.active(selectedRowKeys).subscribe({
      next: (data) => {
        setSelectedRowKeys([]);
        onSearch(1, pageSize);
      }
    }).add(() => {
      setLoading(false);
    });
  };

  const stop = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请至少选择一条数据!");
      return;
    }
    setLoading(true);
    api.system.stop(selectedRowKeys).subscribe({
      next: (data) => {
        setSelectedRowKeys([]);
        onSearch(1, pageSize);
      }
    }).add(() => {
      setLoading(false);
    });
  };


  const unstop = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请至少选择一条数据!");
      return;
    }
    setLoading(true);
    api.system.unstop(selectedRowKeys).subscribe({
      next: (data) => {
        setSelectedRowKeys([]);
        onSearch(1, pageSize);
      }
    }).add(() => {
      setLoading(false);
    });
  };

  const deletes = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请至少选择一条数据!");
      return;
    }
    setLoading(true);
    api.system.deletes({ ids: selectedRowKeys, tenantId: 1 }).subscribe({
      next: (data) => {
        setSelectedRowKeys([]);
        onSearch(1, pageSize);
      }
    }).add(() => {
      setLoading(false);
    });
  };


  const add = () => {
    addForm.resetFields();
    setCurrent({});
    setShowAdd(true);
  }
  const saveUser = (e) => {
    let formData = addForm.getFieldValue();
    if (!formData.userName) {
      message.warning('请填写账号!');
      return false;
    }
    if (!formData.userMobile) {
      message.warning('请填写手机号!');
      return false;
    }
    if (!validatePhone(formData.userMobile)) {
      message.warning('请填写正确的手机号!');
      return false;
    }
    if (!formData.userEmail) {
      message.warning('请填写邮箱!');
      return false;
    }
    if (!validateEmail(formData.userEmail)) {
      message.warning('请填写正确的邮箱!');
      return false;
    }

    objectAssign(formData, { id: current.id });
    setAddLoading(true);
    api.system.saveOrUpdateTenantUser(formData).subscribe({
      next: (data) => {
        message.success("操作成功!");
        setShowAdd(false);
        onSearch(1, pageSize);
      }
    }).add(() => {
      setAddLoading(false);
    });

  }
  const doubleClick = (e) => {
    addForm.resetFields();
    setLoading(true);
    api.system.getTenantUser(e.id).subscribe({
      next: (data) => {
        setCurrent(data);
        addForm.setFieldsValue(data);
        setShowAdd(true);
      }
    }).add(() => {
      setLoading(false);
    });

  }

  const validatePhone = (phoneNumber) => {
    const regex = /^1[3-9]\d{9}$/;
    return regex.test(phoneNumber);
  }


  const validateEmail = (text) => {
    const regex = constant.REGEX_EMAIL;
    return regex.test(text);
  }



  return (

    <>
      <Spin spinning={loading}>
        <Menu selectKeys={["user"]} openKeys={["system"]} >
          <>
            <div className="user-search">
              <Form form={searchForm}>
                <Row wrap={false} gutter={20}>
                  <Col flex={'auto'}>
                    <Row>
                      <Col span={8}>
                        <Form.Item name="state" label="状态" labelCol={{ flex: '90px' }}>
                          <Select allowClear>
                            <Option key='未激活' value='UNACTIVE'>未激活</Option>
                            {/* <Option key='已锁定' value='LOCKED'>已锁定</Option> */}
                            <Option key='已启用' value='ACTIVE'>已启用</Option>
                            <Option key='已停用' value='STOPPED'>已停用</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item name="userName" label="账号" labelCol={{ flex: '90px' }}>
                          <Input />
                        </Form.Item>
                      </Col>

                      <Col span={8}>
                        <Form.Item name="userRealCnName" label="姓名" labelCol={{ flex: '90px' }}>
                          <Input />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Col>

                  <Col flex={'180px'}>
                    <Row>
                      <Button type="default" htmlType="reset"> 重 置 </Button>
                      <Button type="primary" htmlType="submit" onClick={() => onSearch()} loading={searchLoading} style={{ marginLeft: '10px' }}> 查 询 </Button>
                    </Row>
                  </Col>

                </Row>
              </Form>
            </div>
            <div className='user-tool'>
              <Space>
                {/* <Button onClick={() => add()}>添加</Button> */}
              </Space>
              <Space>
                {/* <Button type='primary' onClick={() => active()}>激活</Button>
                <Button type='primary' onClick={() => stop()}>停用</Button>
                <Button type='primary' onClick={() => unstop()}>启用</Button> */}
                {/* <Button type='primary' onClick={() => deletes()}>删除</Button> */}
              </Space>
            </div>
            <Table
              className='user-table'
              rowKey='id'
              pagination={false}
              style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
              rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
              columns={columns}
              dataSource={datas}
              onRow={(record) => {
                return {
                  onDoubleClick: (event) => doubleClick(record),
                };
              }}
            />
            <div className='user-pagination'>
              <Pagination
                pageSizeOptions={[10, 20, 50]}
                showSizeChanger
                onShowSizeChange={onShowSizeChange}
                onChange={onShowSizeChange}
                // defaultCurrent={p}
                total={total || 0}
                current={pageNo || 0}
                pageSize={pageSize || 10}
                showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`}
              />

            </div>
          </>
        </Menu>
      </Spin>
      <Modal
        className="odm-modal"
        title={current.id ? "编辑用户" : "新增用户"}
        open={showAdd}
        width={600}
        centered
        footer={null}
        onCancel={() => setShowAdd(false)}
        modalRender={(modal) => (
          <Spin spinning={addLoading} delay={10} size="large">
            {modal}
          </Spin>
        )}
      >
        <Form onFinish={saveUser} form={addForm}>
          <Row>
            <Col span={12}>
              <Form.Item label="账号" name="userName" labelCol={{ flex: '80px' }} tooltip="最长为20位,保存后不可更改" required={true}>
                <Input disabled={current && current.id} />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.Item label="中文名" name="userRealCnName" labelCol={{ flex: '80px' }} required={true} >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="英文名" name="userRealEnName" labelCol={{ flex: '80px' }}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.Item label="手机号" name="userMobile" labelCol={{ flex: '80px' }} required={true} >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="邮箱" name="userEmail" labelCol={{ flex: '80px' }} required={true}>
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item label="备注" name="note" labelCol={{ flex: '80px' }}>
                <Input.TextArea />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item wrapperCol={{ offset: 6 }}>
            <Space>
              <Button onClick={() => setShowMobile(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>

  );
};
