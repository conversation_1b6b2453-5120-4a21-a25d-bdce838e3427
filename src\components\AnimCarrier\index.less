.anim-Carrier {
    position: relative;
    width: 100%;
    padding-top: 27px;

    .ant-select .ant-select-selector {
        border: none !important;
        box-shadow: none !important;
    }

    .ant-select-focused~label,
    .ant-select:has(.ant-select-selection-item)~label {
        transform: translateY(-18px);
    }

    label {
        position: absolute;
        top: 20px;
        left: 15px;
        font-size: 16px;
        line-height: 20px;
        color: #666;
        pointer-events: none;
        transition: all 0.25s ease;

        @supports not selector(:has(p)) {
            top: 2px !important;
        }
    }
}