import React, { useState, useEffect } from 'react';
import { Button, Row, Col, Form, Input, Modal, message } from 'antd';
import { api, dateFormat } from '@/common/utils';
import objectAssign from 'object-assign';


export default (props) => {
    const { data, onCancel, rateType } = props;
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const shipScheduleCnMap = { '周日': "0", "周一": "1", "周二": "2", "周三": "3", "周四": "4", "周五": "5", "周六": "6", };


    useEffect(() => {
        console.log(props.data, 'data')
        form.resetFields();
        if (props.open) {
            let currentList = props.data;
            let temp = '';

            currentList.forEach((current, index) => {
                let effStr = rateType === '线下' ? (current.effectiveDate ? '有 效 期: ' + dateFormat(current.effectiveDate) + '~' + dateFormat(current.ineffectiveDate) + '\n' : '') :
                    (current.runDate ? '有 效 期: ' + current.runDate + '~' + current.expireDate + '\n' : '');
                let gp20Price = current.gp20Price ? current.gp20Price + '/20GP  ' : '',
                    gp40Price = current.gp40Price ? current.gp40Price + '/40GP  ' : '',
                    hc40Price = current.hc40Price ? current.hc40Price + '/40HC  ' : '';
                if (index > 0) {
                    temp += '\n===========================================\n';
                }
                temp += '起运港: ' + current.polEnName + '\n' +
                    '目的港: ' + current.podEnName + '\n' +
                    '船公司: ' + current.shipCompany + '\n' +
                    '船期: ' + dateFormat(current.etd) + '\n' +
                    '航程: ' + current.shipDays + '天 ' + current.ttOrDt +
                    (current.ttPol ? (' ' + current.ttPol + '\n') : '\n') +
                    '运价: ' + gp20Price + gp40Price + hc40Price + '\n' +
                    '目的港箱使: ' + (current.podContainerFreeDays ? current.podContainerFreeDays : '') + '\n' + effStr +
                    '订舱备注: ' + (current.bookNote ? current.bookNote : '') + '\n' +
                    '限重备注: ' + (current.weightLimitNote ? current.weightLimitNote : '') + '\n'
            })

            form.setFieldValue('content', temp);
        }

    }, [props.open]);





    const copyToClipboard = () => {
        // setLoading(true);
        let formData = form.getFieldValue();
        let param = {}, data = props.data[0];
        objectAssign(param, {
            pol: data.pol, pod: data.pod, tt_pol: data.ttPol, ttOrDt: data.ttOrDt, shipCompany: data.shipCompany, gp20Price: data.gp20Price, gp40Price: data.gp40Price,
            hc40Price: data.hc40Price, nor40Price: data.nor40Price, hc45Price: data.hc45Price, shipSchedule: rateType === '线下' ? shipScheduleCnMap[data.shipSchedule] : data.shipSchedule, shipDays: data.shipDays, etd: data.etd,
            vessel: data.vessel, voyage: data.voyage, lineCode: data.lineCode, runDate: rateType === '线下' ? dateFormat(data.effectiveDate) : data.runDate,
            expireDate: rateType === '线下' ? dateFormat(data.ineffectiveDate) : data.expireDate, bookNote: data.bookNote, weightLimitNote: data.weightLimitNote, podContainerFreeDays: data.podContainerFreeDays,
            gp20Trend: data.gp20Trend, gp40Trend: data.gp40Trend, hc40Trend: data.hc40Trend, nor40Trend: data.nor40Trend, hc45Trend: data.hc45Trend,
            rateType: rateType, content: formData.content, customerName: formData.customerName, cabinState: data.cabinState
        })
        if (navigator.clipboard) {
            navigator.clipboard.writeText(formData.content);
            message.info("复制成功！");
        }

        // api.freightPrice.savePlan(param).subscribe({
        //     next: (data) => {
        //         if (navigator.clipboard) {
        //             navigator.clipboard.writeText(formData.content);
        //             message.info("复制成功！");
        //         }
        //         props.onCancel();
        //     },
        // }).add(() => {
        //     setLoading(false);
        // });


    }


    return (<Modal
        title="报价方案"
        width={500}
        open={props.open}
        onCancel={props.onCancel}
        maskClosable={false}
        footer={null}
    >
        <Form form={form}>
            <Row gutter={10}>
                <Col span={24}>
                    <Form.Item name="content">
                        <Input.TextArea style={{ height: '400px', lineHeight: '30px' }} />
                    </Form.Item>
                </Col>
            </Row>
            <Row gutter={10}>
                {/* <Col flex="auto">
                    <Form.Item name="customerName" label="客户名称">
                        <Input style={{ width: '100%' }} />
                    </Form.Item>
                </Col> */}
                <Col flex='90px'>
                    <Form.Item shouldUpdate>
                        {() => {
                            //  disabled={!form.getFieldValue("customerName")}
                            return <Button type="primary" onClick={copyToClipboard} loading={loading}>复制</Button>
                        }}
                    </Form.Item>
                </Col>
            </Row>
        </Form>
    </Modal>);

}