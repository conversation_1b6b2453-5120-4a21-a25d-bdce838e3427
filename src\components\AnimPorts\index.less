.anim-port {
    position: relative;
    width: 100%;
    height: 40px;
    padding-top: 24px;

    .ant-input,
    .ant-picker,
    .ant-input-number,
    .ant-select .ant-select-selector {
        border: none !important;
        box-shadow: none !important;
    }

    .ant-input-number-focused~label,
    .ant-input-number.ant-input-number-status-success~label,
    .ant-picker-focused~label,
    .ant-picker.ant-picker-status-success~label,
    .ant-input:focus~label,
    .ant-input.ant-input-status-success~label,
    .ant-select-focused~label,
    .ant-select:has(.ant-select-selection-item)~label {
        transform: translateY(-18px);
        color: #999999;
    }

    .ant-select-arrow {
        display: none;
    }

    label {
        position: absolute;
        top: 20px;
        font-size: 16px;
        color: #333;
        pointer-events: none;
        transition: all 0.25s ease;

        @supports not selector(:has(p)) {
            top: 2px !important;
        }
    }

    label.left {
        left: 15px;
    }

    label.right {
        right: 15px;
    }

}