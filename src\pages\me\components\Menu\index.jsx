import React, { useState, useEffect } from 'react';
import { IdcardFilled, InsuranceFilled, ShoppingFilled, HeartFilled, ContainerFilled, WalletFilled, FileTextFilled, SettingFilled, LeftOutlined, RightOutlined,StarFilled } from '@ant-design/icons';
import { Button, Menu } from 'antd';
import { history } from 'umi';
import './index.less';
import avatar from '@/assets/avatar.png';
import { constant } from '@/common/utils';

export default (props) => {

  const [currentUser, setCurrentUser] = useState({});

  useEffect(() => {
    getCurrentUser();
  }, []);

  const getCurrentUser = () => {
    const current = JSON.parse(sessionStorage.getItem(constant.KEY_CURRENT_USER));
    // console.info(current.beSuper)
    setCurrentUser(current);
  };


  const [collapsed, setCollapsed] = useState(false);
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  const onClick = (e) => {
    let url = "/me/";
    for (let i = e.keyPath.length - 1; i >= 0; i--) {
      url = url + e.keyPath[i] + '/';
    }

    history.push(url);
  };

  const UserInfo = () => {
    return <div className='user-info'>
      <div className={collapsed ? 'user-info-collapsed' : 'user-info-expand'}>
        <div className="user-avatar">
          <img src={currentUser?.avatar ? constant.FILE_URL + currentUser?.avatar : avatar} />
          <span className='user-name'>{currentUser?.name}</span>
        </div>
        <div className='user-company'>{currentUser?.groupName}</div>
      </div>
    </div>
  }

  const items = [
    {
      key: 'group01',
      label: '基础',
      type: 'group',
      children: [
        {
          key: 'basic',
          label: '基础信息',
          icon: <IdcardFilled />
        },
        {
          key: 'security',
          label: '安全设置',
          icon: <InsuranceFilled />
        },
      ]
    },
    {
      type: 'divider',
    },
    {
      key: 'group02',
      label: '业务',
      type: 'group',
      children: [
        {
          key: 'business',
          label: '我的业务',
          icon: <ShoppingFilled />,
          children: [
            {
              key: 'rateTrend',
              label: '运价走势',
            },
            {
              key: 'searchHistory',
              label: '查询历史',
            },
            {
              key: 'rateHistory',
              label: '报价历史',
            },
            {
              key: 'orderWarn',
              label: '异常提醒',
            },
          ],
        },
      ]
    }    
  ];

  const surperItems = [
    {
      key: 'group01',
      label: '基础',
      type: 'group',
      children: [
        {
          key: 'basic',
          label: '基础信息',
          icon: <IdcardFilled />
        },
        {
          key: 'security',
          label: '安全设置',
          icon: <InsuranceFilled />
        },
      ]
    },
    {
      type: 'divider',
    },
    {
      key: 'group02',
      label: '业务',
      type: 'group',
      children: [
        {
          key: 'business',
          label: '我的业务',
          icon: <ShoppingFilled />,
          children: [
            {
              key: 'rateTrend',
              label: '运价走势',
            },
            {
              key: 'searchHistory',
              label: '查询历史',
            },
            {
              key: 'rateHistory',
              label: '报价历史',
            },
            {
              key: 'orderWarn',
              label: '异常提醒',
            },
          ],
        },
        // {
        //   key: 'piont',
        //   label: '我的积分',
        //   icon: <StarFilled />,
        // },
        {
          key: 'resource',
          label: '企业资质',
          icon: <ContainerFilled />,
        },
        // {
        //   key: 'finance',
        //   label: '财务信息',
        //   icon: <WalletFilled />,
        //   children: [
        //     {
        //       key: 'baseInfo',
        //       label: '基础信息',
        //     },
        //     {
        //       key: 'invoiceApply',
        //       label: '发票申请',
        //     },
        //   ],
        // }
      ]
    },
    {
      type: 'divider',
    },
    {
      key: 'group03',
      label: '其他',
      type: 'group',
      children: [
        // {
        //   key: 'invitation',
        //   label: '邀请记录',
        //   icon: <HeartFilled />,
        // },
        // {
        //   key: 'agreement',
        //   label: '在线协议',
        //   icon: <FileTextFilled />,
        // },
        {
          key: 'system',
          label: '系统设置',
          icon: <SettingFilled />,
          children: [
            {
              key: 'user',
              label: '账号管理',
            },
            // {
            //   key: 'role',
            //   label: '角色管理',
            // },
            // {
            //   key: 'authority',
            //   label: '权限设置',
            // },
            // {
            //   key: 'other',
            //   label: '其他管理',
            // },
          ]
        }
      ]
    }
  ]

  return (<div className="za-content me">
    <div className="me-content">
      <div className="me-menu">
        <Button className='me-menu-toggle' size='small' shape="circle" icon={collapsed ? <RightOutlined /> : <LeftOutlined />} onClick={toggleCollapsed} />
        <UserInfo />
        <Menu
          onClick={onClick}
          style={{ width: collapsed ? 48 : 200 }}
          defaultSelectedKeys={props.selectKeys}
          defaultOpenKeys={props.openKeys}
          mode="inline"
          inlineCollapsed={collapsed}
          items={currentUser?.beSuper?surperItems:items}
        />
      </div>
      <div className="me-main">
        {props.children}
      </div>
    </div>
  </div>);
};
