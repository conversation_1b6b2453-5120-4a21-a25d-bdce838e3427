import { constant, iget, ipost, isget, iupload, idownload,iuploads } from '@/common/utils';

//港口查询
export function listPort(params) {
  return iget(constant.API_PORT + '/listByKeyWord?keyword=' + params);
}

//船司查询
export function listShipCompany(params) {
  return ipost(constant.API_SHIPCOMPANY + '/listByKeyWord', params);
}

export function listOnlineShipCompany(params) {
  return ipost(constant.API_SHIPCOMPANY + '/listOnline', params);
}

//一沓token
export function getYidaToken(params) {
  return isget(constant.API_BUSINESS + '/getYidaToken', params);
}

//oss上传
export function upload(params) {
  return iupload(constant.API_OSS + '/upload', params);
}

//fds 下载
export function download(param) {
  const { fileUrl, fileName } = param;
  return idownload(constant.API_OSS + '/download?url=' + fileUrl + '&name=' + fileName, {}, false);
}

export function uploadTenantDocument(params) {
  return iuploads(constant.API_OSS + '/uploadTenantDocument', params);
}


//船司查询
export function listLine(params) {
  return iget(constant.API_LINE + '/listByKeyWord?keyword=' + params);
}





