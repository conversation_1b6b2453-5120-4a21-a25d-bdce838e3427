.za-login {
    width: 100%;
    // max-width: 1920px;
    min-width: 1200px;
    height: 100vh;
    display: flex;

    .za-login-left {
        width: 55%;
        height: 100%;
        background: url("../../assets/login-bg.png");
        background-position: top right;
        background-size: cover;

        .za-login-title {
            margin-top: 165px;
            margin-left: 115px;

            h3 {
                font-weight: 600;
                font-size: 36px;
                color: rgba(72, 40, 40, 0.98);
                line-height: 50px;
            }

            span {
                font-weight: 400;
                font-size: 22px;
                color: rgba(72, 40, 40, 0.45);
                line-height: 30px;
                display: block;
            }
        }

        .za-login-description {
            margin-top: 60px;
            margin-left: 115px;

            li {
                list-style: none;
                font-weight: 400;
                font-size: 22px;
                color: rgba(72, 40, 40, 0.75);
                line-height: 30px;
                margin-bottom: 40px;
            }

            li::before {
                content: "";
                display: inline-block;
                width: 20px;
                height: 20px;
                background: url("../../assets/login-icon.png");
                margin-right: 10px;
            }
        }
    }

    .za-login-right {
        width: 45%;
        height: 100%;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;

        .za-login-form {
            width: 400px;

            .za-login-logo {
                width: 400px;
                height: 80px;
                background: url("../../assets/login-logo.png");
                margin-bottom: 40px;
            }

            .za-login-forget {
                text-align: right;
            }

            a {
                color: #b4141b;
            }

            .za-login-other {
                width: 100%;
                height: 60px;
                display: flex;
                justify-content: center;
                align-items: center;

                .za-login-other-wx {
                    width: 36px;
                    height: 36px;
                    background: url("../../assets/wx-icon.png");
                    background-size: contain;
                    cursor: pointer;
                    border-radius: 50%;
                    margin-right: 30px;
                }

                .za-login-other-qq {
                    width: 36px;
                    height: 36px;
                    background: url("../../assets/qq-icon.png");
                    background-size: contain;
                    cursor: pointer;
                    border-radius: 50%;
                }
            }

        }
    }

}
.wxCode{
    width: 200px;
    height: 200px;
    display: block;    
}