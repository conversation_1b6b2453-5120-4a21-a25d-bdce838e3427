import React, { useRef, useState, useEffect } from 'react';
import { Button, Row, Form, Select, Space, message, Table, Tag, Checkbox, Radio, ConfigProvider } from 'antd';
import { RedoOutlined, DownOutlined, UpOutlined, RightOutlined, AppstoreOutlined, BarsOutlined, ArrowUpOutlined, ArrowDownOutlined, } from '@ant-design/icons';
import './index.less';
import AnimCarrier from '@/components/AnimCarrierOnline';
import AnimPorts from '@/components/AnimPortsMap';
import AnimBox from '@/components/AnimBox';
import changeBtn from '@/assets/changeBtn.png';
import AnimDate from '@/components/AnimDate';
import AnimInputNumber from '@/components/AnimInputNumber';
import { api, copyObject, dateFormat, constant, dateDifferenceInDays } from '@/common/utils';
import { shipmentIcon } from '@/common/common';
import ShipSchedule from '@/components/ShipSchedule';
import Quote from './Quote';
import Booking from './Booking';
import Detail from './Detail';
import NoData from '@/components/NoData';
import CompareBtn from '@/components/CompareBtn';
import moment from 'moment';
import * as R from 'ramda';
import { history } from "umi";
import { createStyles } from 'antd-style';
const useStyle = createStyles(({ css, token }) => {
    const { antCls } = token;
    return {
        customTable: css`
      ${antCls}-table {
        ${antCls}-table-container {
          ${antCls}-table-body,
          ${antCls}-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
    `,
    };
});


export default (props) => {
    const { styles } = useStyle();
    useEffect(() => {
        let boxs = ["20GP", "40GP", "40HC"];
        filterForm.setFieldsValue({ sorted: 'etd' });
        searchForm.setFieldsValue({ boxTypes: boxs, });
        setBoxTypes(boxs);
    }, []);

    const shipScheduleMap = { '0': "周日", "1": "周一", "2": "周二", "3": "周三", "4": "周四", "5": "周五", "6": "周六", }
    const shipScheduleCnMap = { '周日': "0", "周一": "1", "周二": "2", "周三": "3", "周四": "4", "周五": "5", "周六": "6", }
    const shipSchedulesSelect = [{ key: '0', value: '周日' }, { key: '1', value: "周一" }, { key: '2', value: "周二" },
    { key: '3', value: "周三" }, { key: '4', value: "周四" }, { key: '5', value: "周五" }, { key: '6', value: "周六" }];
    const [listType, setListType] = useState('');
    const refPol = useRef();
    const refPod = useRef();
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [searchLoading, setSearchLoading] = useState(false);
    const [originalDatas, setOriginalDatas] = useState([]);
    const [freightPrices, setFreightPrices] = useState([
        // {
        //     cabinState: "0",
        //     eta: "06-26",
        //     etd: "2025-06-29T16:00:00.000+00:00",
        //     expireDate: "2025-06-05",
        //     gp20Price: 0,
        //     gp40Price: 0,
        //     hc40Price: 3726,
        //     hc45Price: 0,
        //     id: "1112744150320545792",
        //     lineCode: "AEF3",
        //     nor40Price: 0,
        //     pod: "MOMBASA,KENYA",
        //     podEnName: "MOMBASA,KE ",
        //     podName: "MOMBASA,KE 蒙巴萨｜KENYA",
        //     pol: "QINGDAO,CHINA",
        //     polEnName: "QINGDAO,CN ",
        //     polName: "QINGDAO,CN 青岛｜CHINA",
        //     runDate: "2025-05-23",
        //     shipCompany: "EMC",
        //     shipDays: 21,
        //     shipSchedule: "4",
        //     taskId: "c8012c1d85b7499bab4e9590dee61174",
        //     ttOrDt: "直达",
        //     ttPol: "",
        //     updateTime: "2025-06-10T02:09:42.000+00:00",
        //     vessel: "TS CHENNAI",
        //     voyage: "25003W"
        // },
    ]);
    const current = JSON.parse(sessionStorage.getItem(constant.KEY_CURRENT_USER));
    const freight_histor_key = current?.id + "_history_freight_online";
    const online_listType = "online_listType";
    const [freightHistorys, setFreightHistorys] = useState([]);
    const [searchForm] = Form.useForm();
    const [filterForm] = Form.useForm();
    const [pol, setPol] = useState('');
    const [pod, setPod] = useState('');
    const [podEdi, setPodEdi] = useState('');
    const [boxTypes, setBoxTypes] = useState([]);
    const [angle, setAngle] = useState(0); // 初始旋转角度为0度
    const [count, setCount] = useState(0);
    const [taskIds, setTaskIds] = useState([]);
    const [etd, setEtd] = useState();
    const [randomId, setRandomId] = useState('');

    const [isModalOpen, setIsModalOpen] = useState(false); // 报价方案
    const [isDetailOpen, setIsDetailOpen] = useState(false); // 运价详情
    const [isBookingOpen, setIsBookingOpen] = useState(false); // 下单

    const [rateData, setRateData] = useState({});


    //定义宽度，根据不同屏幕尺寸显示不同列
    const [isScreen, setIsScreen] = useState(false);
    const [isScreen2, setIsScreen2] = useState(false);
    const [isScreen3, setIsScreen3] = useState(false);

    useEffect(() => {
        const handleResize = () => {
            const currentWidth = window.innerWidth; //获取实时屏幕宽度

            if (currentWidth <= 1440) {
                setIsScreen(false);
                setIsScreen2(false);
                setIsScreen3(false);
            } else if (currentWidth >= 1441 && currentWidth <= 1670) {
                setIsScreen(true);
                setIsScreen2(true);
                setIsScreen3(false);
            } else {
                setIsScreen(true);
                setIsScreen2(true);
                setIsScreen3(true);
            }
        };

        // 初始化时立即执行一次
        handleResize();

        // 绑定事件
        window.addEventListener('resize', handleResize);

        // 清理事件
        return () => window.removeEventListener('resize', handleResize);
    }, []);


    const changeType = (type) => {
        setListType(type);
        localStorage.setItem(online_listType, type);
    }


    const handleImageClick = () => {
        setAngle(angle + 360); // 每次点击增加360度
        if (pol || pod) {
            let polTemp = pol, podTemp = pod;
            setPol(podTemp);
            setPod(polTemp);
            setPodEdi(podTemp.portEdi);
            refPol.current?.setValue({ portName: pod ? (pod.enName + ' ' + pod.cnName) : '' });
            refPod.current?.setValue({ portName: pol ? (pol.enName + ' ' + pol.cnName) : '' });
            searchForm.setFieldsValue({ pol: pod ? pod.portEdi : '', pod: pol ? pol.portEdi : '' });
        }
    };

    //设置查询历史
    useEffect(() => {
        let storedString = localStorage.getItem(freight_histor_key);
        let items = storedString ? JSON.parse(storedString) : [];
        setFreightHistorys(items);
        setListType(localStorage.getItem(online_listType) || 'table');
    }, []);


    // 开票中每项的展开状态
    const [expandCards, setExpandCards] = useState({});
    const toggleItem = (id) => {
        setExpandCards(prevState => ({
            ...prevState,
            [id]: !prevState[id],
        }));
    };

    const rateColumns = [
        { title: '船公司', dataIndex: 'shipCompany', width: 100 },
        { title: '航线代码', dataIndex: 'lineCode', width: 120 },
        // { title: '班期', dataIndex: 'shipSchedule', width: 100, render: (text, record) => <>{}</> },
        { title: '船期', etd: 'shipDays', width: 130, render: (text, record) => <>{dateFormat(record.etd)}</> },
        { title: '航程', dataIndex: 'shipDays', width: 100, render: (text, record) => <>{record.shipDays + '天'}</> },
        { title: '中转直达', dataIndex: 'ttOrDt', width: 120, render: (text, record) => <>{text === '中转' ? (record.ttPol || '') + ' 中转' : '直达'}</>, ellipsis: true, },
        {
            title: '20GP', dataIndex: 'gp20Price', width: 100, sorter: (a, b) => a.gp20Price - b.gp20Price,
            render: (text, record) => <>{boxTypes.indexOf("20GP") > -1 && text ? '$' + text : '-'} {boxTypes.indexOf("20GP") > -1 && text ? rateTrendMap[record.gp20Trend] : ""}</>
        },
        {
            title: '40GP', dataIndex: 'gp40Price', width: 100, sorter: (a, b) => a.gp40Price - b.gp40Price,
            render: (text, record) => <>{boxTypes.indexOf("40GP") > -1 && text ? '$' + text : '-'} {boxTypes.indexOf("40GP") > -1 && text ? rateTrendMap[record.gp40Trend] : ""}</>
        },
        {
            title: '40HC', dataIndex: 'hc40Price', width: 100, sorter: (a, b) => a.hc40Price - b.hc40Price,
            render: (text, record) => <>{boxTypes.indexOf("40HC") > -1 && text ? '$' + text : '-'} {boxTypes.indexOf("40HC") > -1 && text ? rateTrendMap[record.hc40Trend] : ""}</>
        },
        {
            title: '40NOR', dataIndex: 'nor40Price', width: 100, sorter: (a, b) => a.nor40Price - b.nor40Price,
            render: (text, record) => <>{boxTypes.indexOf("40NOR") > -1 && text ? '$' + text : '-'} {boxTypes.indexOf("40NOR") > -1 && text ? rateTrendMap[record.nor40Trend] : ""}</>
        },
        {
            title: '45HC', dataIndex: 'hc45Price', width: 100, sorter: (a, b) => a.hc45Price - b.hc45Price,
            render: (text, record) => <>{boxTypes.indexOf("45HC") > -1 && text ? '$' + text : '-'} {boxTypes.indexOf("45HC") > -1 && text ? rateTrendMap[record.hc45Trend] : ""}</>
        },

        { title: '舱位情况', dataIndex: 'cabinState', width: 100, align: 'center', render: (text, record) => <Tag color={tags[text]?.color}>{tags[text]?.text}</Tag> },
        {
            title: '操作', dataIndex: 'option', width: 160, align: 'center', fixed: 'right', render: (text, record) => <div style={{ display: 'flex', justifyContent: 'center' }}>
                <Button size='small' type='text' danger onClick={() => showDetail(record)}>附加费</Button>
                {/* <Button size='small' type='text' danger onClick={() => onBooking(record)}>下单</Button> */}
                <Button size='small' type='primary' onClick={() => showModal(record)}>复制报价</Button>
            </div>
        },
    ]

    const tags = [{ color: 'error', text: '爆仓' }, { color: 'success', text: '充足' }, { color: 'warning', text: '紧张' }];
    const rateTrendMap = {
        "DOWN": <ArrowDownOutlined style={{ color: 'green' }} />,
        "UP": <ArrowUpOutlined style={{ color: 'red' }} />,
    }


    const onSelectChange = (newSelectedRowKeys) => {
        setSelectedRowKeys(newSelectedRowKeys);
    };

    const onSearch = (v) => {
        if (!v.pol) {
            message.error("请选择起运港!");
            return;
        }
        if (!v.pod) {
            message.error("请选择目的港!");
            return;
        }
        if (!v.boxTypes || v.boxTypes.length === 0) {
            message.error("请选择箱型!");
            return;
        }
        if (!v.shipCompanys || v.shipCompanys.length === 0) {
            message.error("选择船公司!");
            return;
        }

        searchOnline(v);
        if (pol && pod) {
            let newItem = {
                pol: pol.portEdi,
                pod: pod.portEdi,
                polName: pol.enName + ' ' + pol.cnName,
                podName: pod.enName + ' ' + pod.cnName,
                polEnName: pol.enName,
                polCnName: pol.cnName,
                podEnName: pod.enName,
                podCnName: pod.cnName,
                id: pol.enName + ' - ' + pod.enName,
            }
            updateLocalStorageArray(freight_histor_key, newItem);
        }
    };

    const searchOnline = (param) => {
        setTaskIds([]);
        setEtd();
        setSearchLoading(true);
        api.freightPrice.createTask(param).subscribe({
            next: (data) => {
                let priceDatas = data.priceDatas || [];
                setOriginalDatas(priceDatas);
                changeListData(priceDatas);
                if (data.taskIds && data.taskIds.length > 0) {
                    setTaskIds(data.taskIds);
                    setEtd(data.etd);
                    setCount(1);
                } else {
                    setSearchLoading(false);
                }
            },
            error: (data) => {
                setSearchLoading(false);
            },
        })
    }

    useEffect(() => {
        if (count === 0 || count > 10) {
            return;
        }
        const timer = setInterval(() => {
            setCount(count + 1);
            getByTaskId(taskIds, etd);
        }, 6000);
        return () => clearInterval(timer);
    }, [count]);



    const getByTaskId = (taskIds, etd) => {
        if (count < 10) {
            api.freightPrice.getByTaskId({ taskIds: taskIds, etd: etd }).subscribe({
                next: (data) => {
                    let temp = originalDatas;
                    let ids = R.pluck('id', originalDatas);
                    let priceDatas = data.priceDatas || [];
                    let addPriceDatas = priceDatas.filter((item) => ids.indexOf(item.id) < 0);
                    let newData = temp.concat(addPriceDatas);
                    if (newData.length > 0 && priceDatas.length > 0) {
                        R.forEach((t) => {
                            const aItem = priceDatas.find(aItem => aItem.id === t.id);
                            if (aItem) {
                                copyObject(t, aItem);
                            }
                        }, newData)
                    }
                    setOriginalDatas(newData);

                    let tempListData = freightPrices;
                    if (addPriceDatas && addPriceDatas.length > 0) {
                        let newListData = addPriceDatas.concat(tempListData);
                        if (newListData.length > 0 && priceDatas.length > 0) {
                            R.forEach((t) => {
                                const aItem = priceDatas.find(aItem => aItem.id === t.id);
                                if (aItem) {
                                    copyObject(t, aItem);
                                }
                            }, newListData)
                        }
                        changeListData(newListData);
                    }

                    if (data.taskState === 'DONE') {
                        setCount(0);
                        setSearchLoading(false);
                    }
                },
                error: (data) => {
                    setSearchLoading(false);
                },
            })
        } else {
            clearInterval();
            setCount(0);
            setSearchLoading(false);
        }
    }



    function updateLocalStorageArray(key, newItem) {
        let storedString = localStorage.getItem(key);
        let items = storedString ? JSON.parse(storedString) : [];
        for (let i = 0; i < items.length; i++) {
            if (items[i].id === newItem.id) {
                //存在 则删除
                items.splice(i, 1);
                break;
            }
        }
        //插入
        items.unshift(newItem);
        //保留3个
        if (items.length > 3) {
            items.pop();
        }
        setFreightHistorys(items);
        let updatedString = JSON.stringify(items);
        //设置缓存
        localStorage.setItem(key, updatedString);
    }

    //修改数据
    const changeListData = (datas) => {
        let newdata = datas, filterData = filterForm.getFieldValue(), searchData = searchForm.getFieldValue();
        //过滤直达中转
        if (filterData.ttOrDt && filterData.ttOrDt.length != 0) {
            newdata = newdata.filter((e) => filterData.ttOrDt.indexOf(e.ttOrDt) != -1);
        }
        //过滤班期
        if (filterData.shipSchedules && filterData.shipSchedules.length != 0) {
            newdata = newdata.filter((e) => filterData.shipSchedules.indexOf(e.shipSchedule) != -1);
        }
        //过滤航程
        if (filterData.shipDays && filterData.shipDays.length >= 2) {
            newdata = newdata.filter((e) => e.shipDays >= filterData.shipDays[0] && e.shipDays <= filterData.shipDays[1]);
        }

        if (filterData?.sorted === 'price') {
            if (searchData?.boxTypes?.indexOf('20GP') != -1) {
                newdata.sort((a, b) => a.gp20Price - b.gp20Price);
            }
            if (searchData?.boxTypes?.indexOf('40GP') != -1) {
                newdata.sort((a, b) => a.gp40Price - b.gp40Price);
            }
            if (searchData?.boxTypes?.indexOf('40HC') != -1) {
                newdata.sort((a, b) => a.hc40Price - b.hc40Price);
            }
            if (searchData?.boxTypes?.indexOf('40NOR') != -1) {
                newdata.sort((a, b) => a.nor40Price - b.nor40Price);
            }
            if (searchData?.boxTypes?.indexOf('45HC') != -1) {
                newdata.sort((a, b) => a.hc45Price - b.hc45Price);
            }
        } else if (filterData?.sorted === 'etd') {
            newdata.sort((a, b) => moment(a.etd) - moment(b.etd));
        } else if (filterData?.sorted === 'schedule') {
            newdata.sort((a, b) => a.shipDays - b.shipDays);
        }
        setRandomId(moment());
        setFreightPrices(newdata);
    }

    // 报价窗口
    const showModal = (current) => {
        setRateData([current]);
        setIsModalOpen(true);
    }

    // 下单
    const onBooking = (data) => {
        if (data.etd && dateDifferenceInDays(Date.now(), data.etd) <= 5) {
            let dateDiff = dateDifferenceInDays(Date.now(), data.etd);
            if (dateDiff <= 0) {
                message.error("开船日必须大于今天~");
            } else {
                Modal.confirm({
                    title: '距离开船日仅剩' + dateDiff + '天,您确定要下单吗？',
                    okText: '确定',
                    okType: 'danger',
                    cancelText: '取消',
                    onOk() {
                        confirmBooking(data);
                    },
                });
            }
        } else {
            confirmBooking(data);
        }
    }
    const confirmBooking = (data) => {
        let bookData = {};
        copyObject(bookData, {
            runDate: data.runDate && data.expireDate ? (data.runDate + '~' + data.expireDate.substring(5, 10)) : '', pol: data.pol, pod: data.pod,
            polId: data.polId, podId: data.podId, shipCompanyId: data.shipCompanyId, shipCompany: data.shipCompany,
            polEnName: data.polEnName, podEnName: data.podEnName, ttOrDt: data.ttOrDt, ttPol: data.ttPol,
            gp20Price: data.gp20Price, gp40Price: data.gp40Price, hc40Price: data.hc40Price, otherPrice: data.nor40Price, otherPriceOne: data.hc45Price, etd: data.etd,
            vessel: data.vessel, voyage: data.voyage, shipDays: data.shipDays, shipSchedule: shipScheduleCnMap[data.shipSchedule], rateType: '线上'
        })
        setRateData(bookData);
        setIsBookingOpen(true);
    }
    //详情
    const showDetail = (param) => {
        api.freightPrice.getPriceInfoOnline(param).subscribe({
            next: (data) => {
                console.info(data)
                setRateData(data);
                setIsDetailOpen(true);
            },
        });
    }


    //多选行回调
    const onSelectNew = (record, selected, selectedRows, nativeEvent) => {
        let list = []
        selectedRows.map(item => {
            list.push({ ...item, priceType: 1 })
        })
        setSelectedRows(selectedRows)
    }
    //加入对比
    const addNum = () => {
        let arr = JSON.parse(localStorage.getItem('allData')) || [];//获取本地存储数组
        let idSet = new Set(arr.map(item => item.id));//将数组转为set
        let flag = selectedRows.some(item => idSet.has(item.id))
        let cardFlag = cardList.some(item => idSet.has(item.id))
        let allArray = [] //合并后的数据
        if (selectedRows.length > 0) {
            if (arr.length > 5 || selectedRows.length > 5 || cardList.length > 5) {
                message.error('最多只能对比5条')
                return
            } else {
                if (listType == 'table') {
                    if (flag == true) {
                        message.error('已有数据加入对比')
                        setSelectedRows([])
                        setSelectedRowKeys([]);
                    } else {
                        allArray = arr.concat(selectedRows)
                        if (allArray.length <= 5) {
                            const num = allArray.length || 0; // 获取本地存储的数组长度（对比数量）
                            localStorage.setItem('compareCount', num);
                            props.onCompare(num)
                            localStorage.setItem('allData', JSON.stringify(allArray));
                            // gridRef.current.onClean()
                            setSelectedRows([])
                            setSelectedRowKeys([]);
                        } else {
                            message.error('最多只能对比5条')
                            // gridRef.current.onClean()
                            setSelectedRows([])
                            setSelectedRowKeys([]);
                        }
                    }
                } else {
                    if (cardFlag == true) {
                        message.error('已有数据加入对比')
                    } else {
                        allArray = arr.concat(cardList)
                        if (allArray.length <= 5) {
                            const num = allArray.length || 0; // 获取本地存储的数组长度（对比数量）
                            localStorage.setItem('compareCount', num);
                            props.onCompare(num)
                            localStorage.setItem('allData', JSON.stringify(allArray));
                        } else {
                            message.error('最多只能对比5条')
                        }
                    }
                }
            }
        }
    }
    const [cardList, setCardList] = useState([])
    //卡片式选中
    const addItem = (r, e) => {
        const flag = e.target.checked;
        let arr = [...cardList];
        if (flag == true) {
            arr.push({ ...R, priceType: 1 });
        } else {
            arr = arr.filter((item) => item.id !== r.id);
        }
        setCardList(arr);
        setSelectedRows(arr)
        console.log()
    }

    return (
        <div style={props.style}>
            <div className="rate-search"  >
                <Form
                    form={searchForm}
                    name="searchRef1"
                    onFinish={onSearch}
                >
                    <div className='search-default'>
                        <Row>
                            <div className="search-ports change-ports">
                                <div className='search-pol'>
                                    <Form.Item name="pol" >
                                        <AnimPorts label="起运港" getSelectItem={(e) => setPol(e)} childRef={refPol} />
                                    </Form.Item>
                                </div>
                                <div className='ports-btn'>
                                    <img src={changeBtn} style={{ transition: 'transform 0.5s', transform: `rotate(${angle}deg)` }}
                                        onClick={handleImageClick}
                                    />
                                </div>
                                <div className='search-pod'>
                                    <Form.Item name="pod" >
                                        <AnimPorts label="目的港" labelalgin="right" getSelectItem={(e) => { setPod(e); setPodEdi(e.portEdi) }} childRef={refPod} />
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="search-container">
                                <Form.Item name="boxTypes" >
                                    <AnimBox label="箱型" onChange={(e) => setBoxTypes(e)} />
                                </Form.Item>
                            </div>
                            {isScreen &&
                                <div className="search-carriers-online margin-r">
                                    <Form.Item name="shipCompanys" >
                                        <AnimCarrier label="船公司" pod={podEdi} mode='multiple' />
                                    </Form.Item>
                                </div>
                            }
                            {isScreen2 && //两屏
                                <div className="search-etd">
                                    <Form.Item name="etd" >
                                        <AnimDate label="开航日期" />
                                    </Form.Item>
                                </div>
                            }
                            {isScreen3 && //三屏
                                <div className="search-cargo">
                                    <Form.Item name="weight" >
                                        <AnimInputNumber label="货重" />
                                    </Form.Item>
                                </div>
                            }
                            <Button htmlType="submit" className="search-btn" loading={searchLoading}>查 询</Button>

                        </Row>

                        <Row style={{ marginTop: '15px' }}>
                            {!isScreen &&
                                <div className="search-carriers-online margin-r">
                                    <Form.Item name="shipCompanys" >
                                        <AnimCarrier label="船公司" pod={podEdi} mode='multiple' />
                                    </Form.Item>
                                </div>
                            }
                            {!isScreen2 && //三屏
                                <div className="search-etd">
                                    <Form.Item name="etd" >
                                        <AnimDate label="开航日期" />
                                    </Form.Item>
                                </div>
                            }
                            {!isScreen3 && //三屏                                 
                                <div className="search-cargo">
                                    <Form.Item name="weight" >
                                        <AnimInputNumber label="货重" />
                                    </Form.Item>
                                </div>
                            }
                        </Row>
                    </div>

                    <div className='search-tool'>
                        <div className="rate-history">
                            {freightHistorys.map((r) => <span key={r.id} onClick={() => {
                                setPol({ enName: r.polEnName, cnName: r.polCnName, portEdi: r.pol });
                                setPod({ enName: r.podEnName, cnName: r.podCnName, portEdi: r.pod });
                                setPodEdi(r.pod);
                                refPol.current?.setValue({ portName: r.polName });
                                refPod.current?.setValue({ portName: r.podName });
                                searchForm.setFieldsValue({ pol: r.pol, pod: r.pod });
                            }}>
                                {r.id}
                            </span>
                            )}
                            <span style={{ display: freightHistorys.length > 0 ? '' : 'none' }} onClick={() => {
                                localStorage.removeItem(freight_histor_key);
                                setFreightHistorys([]);
                            }}>清空记录</span>

                            <span style={{ color: 'red' }} onClick={() => history.push('/me/business/searchHistory')}>
                                查询历史
                            </span>
                        </div>

                        <div className="tool-btn">
                            <Button type="text" icon={<RedoOutlined />} size="small" htmlType="reset">
                                重置
                            </Button>
                        </div>
                    </div>
                </Form>

            </div>
            <div className='rate-list grid-rate'>
                <div className="rate-tools">
                    <div className="rate-filter">
                        <Form layout="inline" form={filterForm} onFieldsChange={() => changeListData(originalDatas)}>
                            <Form.Item name="sorted">
                                <Radio.Group buttonStyle="solid">
                                    <Radio.Button value="price">价格最低</Radio.Button>
                                    <Radio.Button value="etd">最早开船</Radio.Button>
                                    <Radio.Button value="schedule">航程最短</Radio.Button>
                                </Radio.Group>
                            </Form.Item>


                            <Form.Item name='ttOrDt'><Select placeholder="中转/直达" options={[{ label: '中转', value: '中转' }, { label: '直达', value: '直达' }]} mode='multiple' allowClear style={{ width: '155px' }}></Select></Form.Item>
                            <Form.Item name='shipSchedules'>
                                <Select mode='multiple' allowClear maxTagCount={1} placeholder='班期' style={{ width: '155px' }}>
                                    {shipSchedulesSelect.map((item) => {
                                        return <Option key={item.key} value={item.key}>{item.value}</Option>
                                    })}
                                </Select>
                            </Form.Item>
                            <Form.Item name='shipDays' label=''><ShipSchedule placeholder='请筛选航程' style={{ width: '100px' }} /></Form.Item>
                        </Form>
                    </div>
                    <div className="rate-tools-btn">
                        <Space>
                            <CompareBtn onClick={addNum} dataNum={selectedRows.length} />
                            <Button type="text" icon={<BarsOutlined style={listType == 'table' ? { color: '#B4141B' } : {}} />} onClick={() => changeType('table')}></Button>
                            <Button type="text" icon={<AppstoreOutlined style={listType == 'card' ? { color: '#B4141B' } : {}} />} onClick={() => changeType('card')}></Button>
                        </Space>
                    </div>
                </div>

                {/* 列表式 */}
                {/* {selectedRowKeys.length > 0 && <Alert message={`已选择 ${selectedRowKeys.length} 项`} type="warning" closable style={{ marginTop: '16px' }} />} */}

                {listType == 'table' ?
                    <ConfigProvider renderEmpty={() => <NoData />}>
                        <div className='rate-table'>
                            <Table
                                className={styles.customTable}
                                scroll={{ y: 55 * 13 }}
                                rowKey='id'
                                type='checkbox'
                                pagination={false}
                                style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
                                rowSelection={{ selectedRowKeys, onChange: onSelectChange, onSelect: onSelectNew }}
                                columns={rateColumns}
                                dataSource={freightPrices}
                                expandable={{
                                    expandedRowRender: (record) => (
                                        <div className='rate-expand'>
                                            <p><span>起运港：</span>{record.pol}  <span>目的港：</span>{record.pod}</p>
                                            <p> <span>船名航次： </span>{record.vessel} / {record.voyage} <span> 班期： </span>{shipScheduleMap[record.shipSchedule]}
                                                <span>生效日期：</span>{record.runDate}
                                                <span>失效日期：</span>{record.expireDate}
                                            </p>
                                            <p>   <span>更新时间：</span>{dateFormat(record.updateTime, 'yyyy-MM-dd hh:mm')}</p>
                                        </div>
                                    ),
                                    expandIcon: ({ expanded, onExpand, record }) =>
                                        expanded ?
                                            (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                                            :
                                            (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                                }}
                            />
                        </div>
                    </ConfigProvider>
                    :
                    <>
                        <div className='card-header card-header2'>
                            <div className='header-item' style={{ width: '650px', border: 0, textAlign: 'left' }}>
                                <Checkbox /> <span style={{ marginLeft: '16px' }}>基础信息</span>
                            </div>
                            <div className='header-item' style={{ width: '160px' }}>中转/直达</div>
                            <div className='header-item' style={{ width: '140px' }}>20GP</div>
                            <div className='header-item' style={{ width: '140px' }}>40GP</div>
                            <div className='header-item' style={{ width: '140px' }}>40HC</div>
                            <div className='header-item' style={{ width: '140px' }}>40NOR</div>
                            <div className='header-item' style={{ width: '140px' }}>45HC</div>
                            <div className='header-item' style={{ width: '180px' }}>操作</div>
                        </div>

                        {freightPrices.map((r) => <div className='card-list' key={r.id}>
                            <div className='card-item-top'>
                                <div className='card-item'>
                                    <div className='card-item-checkbox'>
                                        <Checkbox onChange={(e) => { addItem(r, e) }} />
                                    </div>
                                    <div className='card-item-logo'>
                                        <img src={shipmentIcon(r.shipCompany)} />
                                    </div>
                                    <div className='card-item-ports'>
                                        <p>{r.shipCompany}</p>
                                        <p style={{ color: '#999' }}>{r.polEnName} → {r.podEnName}</p>
                                    </div>
                                </div>
                                <div className='card-item-transfer'>
                                    {/* <p>{r.ttOrDt + (r.ttOrDt === '中转' ? ' ' + r.ttPol || '' : '直达')} </p> */}
                                    <p>{(r.ttOrDt === '中转' ? <span title={r.ttPol}>中转</span> : '直达')} </p>
                                    <p style={{ color: '#999' }}>{dateFormat(r.etd, 'MM.dd') + '-' + r.eta + ' (' + r.shipDays + '天)'}</p>
                                </div>
                                <div className='card-item-rate'>
                                    {boxTypes.indexOf("20GP") > -1 && r.gp20Price ? '$' + r.gp20Price : '-'} {boxTypes.indexOf("20GP") > -1 && r.gp20Price ? rateTrendMap[r.gp20Trend] : ''}
                                </div>
                                <div className='card-item-rate'>
                                    {boxTypes.indexOf("40GP") > -1 && r.gp40Price ? '$' + r.gp40Price : '-'} {boxTypes.indexOf("40GP") > -1 && r.gp40Price ? rateTrendMap[r.gp40Trend] : ''}
                                </div>
                                <div className='card-item-rate'>
                                    {boxTypes.indexOf("40HC") > -1 && r.hc40Price ? '$' + r.hc40Price : '-'} {boxTypes.indexOf("40HC") > -1 && r.hc40Price ? rateTrendMap[r.hc40Trend] : ''}
                                </div>
                                <div className='card-item-rate'>
                                    {boxTypes.indexOf("40NOR") > -1 && r.nor40Price ? '$' + r.nor40Price : '-'} {boxTypes.indexOf("40NOR") > -1 && r.nor40Price ? rateTrendMap[r.nor40Trend] : ''}
                                </div>
                                <div className='card-item-rate'>
                                    {boxTypes.indexOf("45HC") > -1 && r.hc45Price ? '$' + r.hc45Price : '-'} {boxTypes.indexOf("45HC") > -1 && r.hc45Price ? rateTrendMap[r.hc45Trend] : ''}
                                </div>
                                <div className='card-item-option'>
                                    <Space>                                        
                                        <Button type="text" danger onClick={() => showDetail(r)}>附加费</Button>
                                        {/* <Button type="text" danger onClick={() => onBooking(r)}>下 单</Button> */}
                                        <Button type="primary" size='small' onClick={() => showModal(r)}>复制报价</Button>
                                    </Space>
                                </div>

                            </div>
                            <div className='card-item-bottom'>
                                <p><span>船期：</span>{dateFormat(r.etd)}  <span>船名航次</span>{r.vessel} / {r.voyage}
                                    <span>生效日期：</span>{r.runDate} <span>失效日期：</span>{r.expireDate} <span>更新时间：</span>{dateFormat(r.updateTime, 'yyyy-MM-dd hh:mm')}
                                </p>
                                {expandCards[r.id] ?
                                    <>
                                        {/* <p> <span>航线代码：</span>{r.lineCode}  <span>附加费：</span> <a onClick={() => showDetail(r)}>查看</a></p> */}

                                        <Button type="text" icon={<UpOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>收起</Button>
                                    </>
                                    :
                                    <Button type="text" icon={<DownOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>展开</Button>}
                            </div>
                        </div>)}

                        {freightPrices.length == 0 && <NoData />}

                    </>}

            </div>

            <Quote
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                data={rateData}
                rateType='线上'
            />

            <Booking
                current={rateData}
                open={isBookingOpen}
                onCancel={() => setIsBookingOpen(false)}
                onSubmit={() => {
                    setIsBookingOpen(false);
                }}
            />

            <Detail
                current={rateData}
                open={isDetailOpen}
                onCancel={() => setIsDetailOpen(false)}
            />

        </div>
    );
}
