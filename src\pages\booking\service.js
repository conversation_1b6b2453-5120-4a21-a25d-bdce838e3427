import { constant, iget, ipost, iput, isearch, iupload, idelete, isget } from '@/common/utils';

//查询
export function searchBookOrder(params) {
  return isearch(constant.API_BOOK_ORDER + '/searchByCtm', params);
}

export function saveOrUpdate(data) {
  return data && data.id ? iput(constant.API_BOOK_ORDER, data)
    : ipost(constant.API_BOOK_ORDER, data);
}
//详情
export function getDetail(params) {
  return isget(constant.API_BOOK_ORDER + '/' + params);
}

//历史修改记录
export function listHistoryLogs(params) {
  return iget(constant.API_BOOK_ORDER + '/listHistory/' + params);
}

//删除文件
export function deleteDocument(params) {
  return idelete(constant.API_BOOK_ORDER + '/deleteDocument', params);
}
export function uploadDocument(params) {
  return iupload(constant.API_BOOK_ORDER + '/uploadDocument', params);
}
//提交
export function commit(params) {
  return ipost(constant.API_BOOK_ORDER + '/commit', params);
}
//批量提交
export function commits(params) {
  return ipost(constant.API_BOOK_ORDER + '/commits', params);
}
//批量撤销
export function revokes(params) {
  return ipost(constant.API_BOOK_ORDER + '/revokes', params);
}

//聊天
export function sendChat(params) {
  return ipost(constant.API_BOOK_ORDER + '/sendChat', params);
}

export function listChats(params) {
  return iget(constant.API_BOOK_ORDER + '/listChats/' + params);
}