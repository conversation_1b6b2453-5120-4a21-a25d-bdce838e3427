import React, { useState, useEffect } from 'react';
import { Row, Col, Image, Upload, Button, Descriptions, Avatar, Form, Input, Space, Spin, message } from 'antd';
import { BankOutlined, QrcodeOutlined, UserOutlined, EnvironmentOutlined, UnorderedListOutlined, UploadOutlined } from '@ant-design/icons';
import Company from '../components/Company';
import Menu from '../components/Menu';
import res01 from '@/assets/res01.jpg';// demo
import cert from '@/assets/cert.jpg';// demo
import { api, constant } from '@/common/utils';
import './index.less';
import objectAssign from 'object-assign';


export default () => {
  const [isEdit, setIsEdit] = useState(false);
  const certStatusArr = ['审核中', '已认证', '驳回申请'];
  const [currentTenant, setCurrentTenant] = useState({});
  const [avatar, setAvatar] = useState('');
  const [certUrl, setCertUrl] = useState('');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [flag, setFlag] = useState(false);
  const [certStatus, setCertStatus] = useState(0);
  const [otherCers, setOtherCers] = useState([]);

  useEffect(() => {
    getCurrentTenant();
  }, []);

  const getCurrentTenant = () => {
    const current = JSON.parse(sessionStorage.getItem(constant.KEY_CURRENT_USER));
    if (!current || !current.tenantId) {
      return
    }
    api.me.getTenant(current.tenantId).subscribe({
      next: (data) => {
        setIsEdit(false);
        setCurrentTenant(data);
        setAvatar(data.logoUrl);
        setCertUrl(data.cerUrl);
        setFlag(!flag);
        if (data.state === 'COMMIT') {
          setCertStatus(0);
        } else if (data.state === 'APPROVE') {
          setCertStatus(1);
        } else if (data.state === 'REJECT') {
          setCertStatus(2);
        }
        setOtherCers(data.documents);
      }
    });
  };


  const items = [
    {
      key: '1',
      label: <div className='basic-info-title'><BankOutlined /> 企业名称</div>,
      children: currentTenant.name,
    },
    {
      key: '2',
      label: <div className='basic-info-title'><QrcodeOutlined /> 社会信用代码</div>,
      children: currentTenant.taxNo,
    },
    {
      key: '3',
      label: <div className='basic-info-title'><UserOutlined /> 企业法人</div>,
      children: currentTenant.tenantOwner,
    },
    {
      key: '4',
      label: <div className='basic-info-title'><EnvironmentOutlined /> 注册地址</div>,
      children: currentTenant.address,
    },

    {
      key: '7',
      label: <div className='basic-info-title'><UnorderedListOutlined /> 企业简介</div>,
      children: currentTenant.tenantInfo,
    },
    {
      key: '8',
      label: <div className='basic-info-title'></div>,
      labelStyle: { color: '#fff' },
      children: <Button type='default' onClick={() => updateInfo()}>修改信息</Button>
    },
  ]


  const updateInfo = () => {
    form.setFieldsValue(currentTenant);
    setIsEdit(true)
  }

  // 保存修改
  const saveInfo = (data) => {
    if (!data.name) {
      message.warning('请填写企业名称!');
      return false;
    }
    if (!data.taxNo) {
      message.warning('请填写社会信用代码!');
      return false;
    }
    objectAssign(data, { id: currentTenant.id, logoUrl: avatar });
    setLoading(true);
    api.me.updateTenantInfo(data).subscribe({
      next: (data) => {
        getCurrentTenant();
      }
    }).add(() => {
      setLoading(false);
    });
  }

  const updateCer = (e) => {
    let data = { id: currentTenant.id, cerUrl: e };
    setLoading(true);
    api.me.updateTenantInfo(data).subscribe({
      next: (data) => {
        message.success('上传成功!');
      }
    }).add(() => {
      setLoading(false);
    });
  }

  return (
    <Spin spinning={loading}>
      <Menu selectKeys={["resource"]} openKeys={[]}>
        <div className='resourceContent'>

          <Company flag={flag} />

          {isEdit ?
            <>
              <Row style={{ margin: '24px 0' }}>
                <Col span={3} style={{ textAlign: 'right', paddingRight: '24px' }}>
                  <Avatar size={64} icon={<UserOutlined />} src={avatar ? constant.FILE_URL + avatar : ''} />
                </Col>
                <Col span={18}>
                  <Upload
                    showUploadList={false}
                    accept=".png, .jpg"
                    customRequest={async (options) => {
                      setLoading(true);
                      const { file, onSuccess, onError } = options;
                      const formData = new FormData();
                      formData.append('file', file);
                      api.base.upload(formData).subscribe({
                        next: (data) => {
                          onSuccess({ message: 'OK' });
                          message.success('上传成功!');
                          setAvatar(data.url);
                        },
                        error: (err) => onError(err),
                      }).add(() => {
                        setLoading(false);
                      });;
                    }}

                    beforeUpload={(file) => {
                      return new Promise((resolve, reject) => {
                        if (file.size / (1024 * 1024) < 3) {
                          resolve();
                        } else {
                          const err = '上传文件大小应小于3M';
                          message.error(err);
                          reject(new Error(err));
                        }
                      });
                    }}
                  >
                    <Button icon={<UploadOutlined />}>更换LOGO</Button>
                    <p style={{ margin: "5px" }}>支持图片类型：png, jpg</p>
                  </Upload>
                </Col>
              </Row>
              <Form onFinish={saveInfo} form={form}>
                <Form.Item label="企业名称" name="name" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                  <Input />
                </Form.Item>
                <Form.Item label="社会信用代码" name="taxNo" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                  <Input />
                </Form.Item>
                <Form.Item label="企业法人" name="tenantOwner" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                  <Input />
                </Form.Item>
                <Form.Item label="注册地址" name="address" labelCol={{ span: 3 }} wrapperCol={{ span: 9 }}>
                  <Input />
                </Form.Item>
                <Form.Item label="企业简介" name="tenantInfo" labelCol={{ span: 3 }} wrapperCol={{ span: 18 }}>
                  <Input.TextArea rows={4} />
                </Form.Item>
                <Form.Item wrapperCol={{ offset: 3 }}>
                  <Space>
                    <Button onClick={() => setIsEdit(false)}>
                      取消
                    </Button>
                    <Button type="primary" htmlType="submit">
                      保存
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </>
            :
            <Descriptions
              title="企业基本信息"
              column={1}
              items={items}
              style={{ margin: '30px 20px' }}
            />
          }

          <div className='resource-title'>企业营业执照</div>
          <Row gutter={20}>
            <Col>
              <Image
                width={200}
                height={300}
                src={certUrl ? constant.FILE_URL + certUrl : cert}
                style={{
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              />
              <div className={`certStatus status0${certStatus + 1}`}></div>
              {certStatus != 1 && <div className="certStatusText">{certStatusArr[certStatus]}</div>}
            </Col>
            <Col style={{ display: 'flex', flexDirection: 'column', justifyContent: 'flex-end' }}>
              <Upload
                showUploadList={false}
                accept=".png, .jpg"
                customRequest={async (options) => {
                  setLoading(true);
                  const { file, onSuccess, onError } = options;
                  const formData = new FormData();
                  formData.append('file', file);
                  api.base.upload(formData).subscribe({
                    next: (data) => {
                      onSuccess({ message: 'OK' });
                      setCertUrl(data.url);
                      updateCer(data.url);
                    },
                    error: (err) => onError(err),
                  }).add(() => {
                    setLoading(false);
                  });;
                }}

                beforeUpload={(file) => {
                  return new Promise((resolve, reject) => {
                    if (file.size / (1024 * 1024) < 3) {
                      resolve();
                    } else {
                      const err = '上传文件大小应小于3M';
                      message.error(err);
                      reject(new Error(err));
                    }
                  });
                }}
              >
                <Button icon={<UploadOutlined />}>上传营业执照</Button>
              </Upload>
              <div style={{ lineHeight: '32px' }}>支持图片类型：png,jpg</div>
            </Col>
          </Row>

          <div className='resource-title'>其他资质</div>
          <Row gutter={20}>
            {otherCers.map((r) =>
              <Col>
                <Image
                  width={200}
                  height={300}
                  src={constant.FILE_URL + r.fileUrl}
                  style={{
                    border: '1px solid #ccc',
                    borderRadius: '4px'
                  }}
                />
              </Col>
            )}

            <Col style={{ height: '300px', paddingLeft: '20px', display: 'flex', flexDirection: 'column', justifyContent: 'flex-end' }}>
              <Upload
                showUploadList={false}
                accept=".png, .jpg"
                customRequest={async (options) => {
                  setLoading(true);
                  const { file, onSuccess, onError } = options;
                  const formData = new FormData();
                  formData.append('file', file);
                  formData.append('tenantId', currentTenant.id);
                  api.base.uploadTenantDocument(formData).subscribe({
                    next: (data) => {
                      onSuccess({ message: 'OK' });
                      message.success('上传成功!');
                      setOtherCers(data);
                    },
                    error: (err) => onError(err),
                  }).add(() => {
                    setLoading(false);
                  });;
                }}

                beforeUpload={(file) => {
                  return new Promise((resolve, reject) => {
                    if (file.size / (1024 * 1024) < 3) {
                      resolve();
                    } else {
                      const err = '上传文件大小应小于3M';
                      message.error(err);
                      reject(new Error(err));
                    }
                  });
                }}
              >
                <Button icon={<UploadOutlined />}>上传资质</Button>
              </Upload>
              <div style={{ lineHeight: '32px' }}>支持图片类型：png,jpg</div>
            </Col>
          </Row>
        </div>
      </Menu>
    </Spin>
  );
};
