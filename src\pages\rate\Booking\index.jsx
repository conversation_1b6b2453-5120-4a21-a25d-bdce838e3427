import React, { useState, useEffect } from 'react';
import { Button, Space, Modal, Layout, Card, Tabs, Col, Form, Row, Input, Table, Radio, Upload, message, Spin } from 'antd';
import { DownOutlined, UpOutlined, UploadOutlined } from '@ant-design/icons';
import ChatGPT from '@/components/ChatGPT';
import BookingLog from '@/components/BookingLog';
import { api, dateFormat, constant } from '@/common/utils';
import ContainerEdit from '@/components/ContainerEdit';
import * as R from 'ramda';
import Dict from '@/components/Dict';
import DICTIONARY from '@/common/dictionary';
import objectAssign from 'object-assign';
import FileViewer from '@/components/FileViewer';


export default (props) => {
  const { current, onCancel, onSubmit } = props;
  const [isRateShow, setIsRateShow] = useState(false);
  const [isCargoShow, setIsCargoShow] = useState(false);
  const [isBillShow, setIsBillShow] = useState(false);
  const [isOtherShow, setIsOtherShow] = useState(false);
  const [form] = Form.useForm();
  const [rateDatas, setRateDatas] = useState([]);
  // const [boxList, setBoxList] = useState([{ id: Date.now() + '' }]);
  const [fileList, setFileList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [bookOrderId, setBookOrderId] = useState('');
  const [activeKey, setActiveKey] = useState("1");
  const [baseInfo, setBaseInfo] = useState(current);
  const [containerData, setContainerData] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [fileUrl, setFileUrl] = useState('');
  const [beOpen, setBeOpen] = useState(false);


  useEffect(() => {
    setBeOpen(props.open);
    setActiveKey("1");
    form.resetFields();
    setContainerData([]);
    setFileList([]);
    setRateDatas([]);
    if (props.open) {
      setFormData(current);
      setRateDatas([current]);
      setContainerData(current.boxs || []);
      setFileList(current.documents || []);
    }
  }, [current?.id, props.open]);

  const setFormData = (v) => {
    setBaseInfo(v);
    setBookOrderId(v.id);
    objectAssign(v, { etdStr: dateFormat(v.etd) });
    if (!v.id) {
      objectAssign(v, { shipClauseCode: 'CY-CY', shipPaymentCode: 'FREIGHT PREPAID', beMaster: 1, });
    }
    form.setFieldsValue(v);
  }


  const items = [
    {
      key: '1',
      label: '操作日志',
      children: <BookingLog
        id={bookOrderId}
        activeKey={activeKey}
        open={Date.now()}
        style={{
          padding: '2px',
          height: 'calc(100vh - 275px)',
          overflow: 'auto'
        }}
      />,
    },
    // {
    //   key: '2',
    //   label: '业务咨询',
    //   children: <ChatGPT
    //     id={bookOrderId}
    //     activeKey={activeKey}
    //     open={props.open}
    //     style={{
    //       height: 'calc(100vh - 355px)',
    //       overflow: 'auto'
    //     }} />,
    // }
  ];

  const rateColumns = [
    { title: '船公司', dataIndex: 'shipCompany', width: '160', align: 'center' },
    { title: '船名', dataIndex: 'vessel', width: '150', align: 'center' },
    { title: '航次', dataIndex: 'voyage', width: '100', align: 'center' },
    { title: '船期', dataIndex: 'etdStr', align: 'center' },
    { title: '20GP', dataIndex: 'gp20Price', width: '200', align: 'center' },
    { title: '40GP', dataIndex: 'gp40Price', width: '200', align: 'center' },
    { title: '40HC', dataIndex: 'hc40Price', width: '200', align: 'center' },
    { title: '有效期', dataIndex: 'runDate', align: 'center' },
  ]

  const save = () => {
    const formData = form.getFieldValue();
    if (validateBook(formData)) {
      objectAssign(formData, { boxs: containerData, documents: fileList });
      setLoading(true);
      api.bookOrder.saveOrUpdate(formData).subscribe({
        next: (data) => {
          setFormData(data[0]);
          message.success('操作成功!');
        },
      }).add(() => {
        setLoading(false);
      });
    }
  }
  const commit = () => {
    const formData = form.getFieldValue();
    if (validateBook(formData)) {
      objectAssign(formData, { boxs: containerData, documents: fileList });
      setLoading(true);
      api.bookOrder.commit(formData).subscribe({
        next: (data) => {
          message.success('操作成功!');
          setBeOpen(false);
          onSubmit();
        },
      }).add(() => {
        setLoading(false);
      });
    }
  }


  //验证
  const validateBook = (data) => {
    let flag = true;
    if (data.bookEmail && !constant.REGEX_EMAIL.test(data.bookEmail)) {
      message.warning('请填写正确的邮箱!');
      return false;
    }
    if (data.bookMobilePhone && !constant.REGEX_MOBILE.test(data.bookMobilePhone)) {
      message.warning('请填写正确的手机!');
      return false;
    }

    if (containerData.length === 0) {
      message.warning('请至少添加一种箱型!');
      return false;

    }
    if (uploading) {
      message.warning('文件正在上传...!');
      return false;
    }
    R.forEach((v) => {
      if (!v.boxType && flag) {
        message.warning('请选择箱型!');
        flag = false;
      }
      if (!v.boxNum && flag) {
        message.warning('请填写数量!');
        flag = false;
      }
    }, containerData)
    return flag;
  }


  const onChange = (key) => {
    setActiveKey(key)
  };

  //删除
  const deleteFile = (file) => {
    let ids = [];
    ids.push(file.uid);
    let param = { bookOrderId: bookOrderId, documentIds: ids, documentUrl: file.url };
    Modal.confirm({
      title: `确认要删除此文件吗？`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        api.bookOrder.deleteDocument(param).subscribe({
          next: (data) => {
            message.success('删除成功!');
            let temp = fileList.filter(r => r.uid != file.uid);
            setFileList(temp);
          },
        });
      },
    });
  }

  //预览
  const previewFile = (file) => {
    setFileUrl(file.url);
    setPreviewVisible(true);
  }

  //箱型修改
  const boxChange = (datas) => {
    let weight = 0;
    R.forEach((v) => {
      if (v.weight) {
        weight += parseFloat(v.weight);
      }
    }, datas)
    form.setFieldsValue({ cargoWeight: weight });
    setContainerData(datas);
  }

  return (
    <>
      <Modal
        title={"在线下单" + (bookOrderId ? (' - ' + baseInfo.bookCode) : '')}
        className='odm-modal'
        width={1200}
        open={props.open}
        onCancel={() => {
          setBeOpen(false);
          props.onCancel();
        }}
        styles={{
          body: {
            height: 'calc(100vh - 200px)',
          }
        }}
        maskClosable={false}
        footer={null}
        centered
        modalRender={(modal) => (
          <Spin spinning={loading} delay={10}>
            {modal}
          </Spin>
        )}
      >
        <Layout style={{ height: '100%', background: '#fff' }}>
          <Layout>
            <Layout.Content style={{ paddingRight: '10px', background: '#fff', overflow: "auto" }}>
              <Form form={form}>
                <Space direction="vertical" size={10} style={{ width: '100%' }}>
                  <Card
                    title="订舱信息"
                    size='small'
                    styles={{ header: { background: '#fafafa' } }}
                  >
                    <Row>
                      <Col span={8}>
                        <Form.Item label="起运港" labelCol={{ flex: '80px' }} name='polEnName'>
                          <Input disabled />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item label="目的港" labelCol={{ flex: '80px' }} name='podEnName'>
                          <Input disabled />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item label="船期" labelCol={{ flex: '80px' }} name='etdStr'>
                          <Input disabled />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={8}>
                        <Form.Item label="船公司" labelCol={{ flex: '80px' }} name='shipCompany'>
                          <Input disabled />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item label="运输条款" labelCol={{ flex: '80px' }} name="shipClauseCode">
                          <Dict dictCode={DICTIONARY.DICT_SHIP_CLAUSE_TAG} placeholder="请选择运输条款" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item label="付款方式" labelCol={{ flex: '80px' }} name="shipPaymentCode" >
                          <Dict placeholder="请选择付费方式" dictCode={DICTIONARY.DICT_PAY_METHOD_TAG} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                  <Card
                    title="箱型运价"
                    size='small'
                    styles={{ header: { background: '#fafafa' }, body: { display: isRateShow ? 'none' : 'block', padding: '1px' } }}
                    extra={<Button size='small' type='text' icon={isRateShow ? <DownOutlined /> : <UpOutlined />} onClick={() => setIsRateShow(!isRateShow)} />}
                  >
                    <Table
                      rowKey='id'
                      size='small'
                      pagination={false}
                      columns={rateColumns}
                      dataSource={rateDatas}
                    />
                  </Card>
                  <Card
                    title="货物信息"
                    size='small'
                    styles={{ header: { background: '#fafafa' }, body: { display: isCargoShow ? 'none' : 'block' } }}
                    extra={<Button size='small' type='text' icon={isCargoShow ? <DownOutlined /> : <UpOutlined />} onClick={() => setIsCargoShow(!isCargoShow)} />}
                  >
                    <Form.Item name="containers">
                      <ContainerEdit data={containerData} onChange={(e) => boxChange(e)} />
                    </Form.Item>
                    <Row gutter={10}>
                      <Col span={16}>
                        品名
                      </Col>
                      <Col span={8}>
                        总货重（KGS）
                      </Col>
                    </Row>
                    <Row gutter={10}>
                      <Col span={16}>
                        <Form.Item name='cnTradeName'>
                          <Input.TextArea style={{ height: '90px' }} />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item name='cargoWeight'>
                          <Input readOnly />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                  <Card
                    title="提单信息"
                    size='small'
                    styles={{ header: { background: '#fafafa' }, body: { display: isBillShow ? 'none' : 'block' } }}
                    extra={<Button size='small' type='text' icon={isBillShow ? <DownOutlined /> : <UpOutlined />} onClick={() => setIsBillShow(!isBillShow)} />}
                  >
                    <Row>
                      <Col span={24}>
                        <Form.Item label="提单方式" labelCol={{ flex: '80px' }} name='beMaster'>
                          <Radio.Group>
                            <Radio value={1}>MB/L</Radio>
                            <Radio value={0}>HB/L</Radio>
                          </Radio.Group>
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row gutter={10}>
                      <Col span={8}>
                        发货人 SHIPPER
                      </Col>
                      <Col span={8}>
                        收货人 CONSIGNEE
                      </Col>
                      <Col span={8}>
                        通知人 NOTIFY PARTY
                      </Col>
                    </Row>
                    <Row gutter={10}>
                      <Col span={8}>
                        <Form.Item name='shipper'>
                          <Input.TextArea style={{ height: '120px' }} onBlur={() => form.setFieldsValue({ shipper: form.getFieldValue().shipper?.toUpperCase() })} />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item name='consignee'>
                          <Input.TextArea style={{ height: '120px' }} onBlur={() => form.setFieldsValue({ consignee: form.getFieldValue().consignee?.toUpperCase() })} />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item name='notify'>
                          <Input.TextArea style={{ height: '120px' }} onBlur={() => form.setFieldsValue({ notify: form.getFieldValue().notify?.toUpperCase() })} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                  <Card
                    title="其他信息"
                    size='small'
                    styles={{ header: { background: '#fafafa' }, body: { display: isOtherShow ? 'none' : 'block' } }}
                    extra={<Button size='small' type='text' icon={isOtherShow ? <DownOutlined /> : <UpOutlined />} onClick={() => setIsOtherShow(!isOtherShow)} />}
                  >
                    <Row gutter={10}>
                      <Col span={6}>
                        订舱联系人
                      </Col>
                      <Col span={6}>
                        邮箱
                      </Col>
                      <Col span={6}>
                        手机
                      </Col>
                      <Col span={6}>
                        电话
                      </Col>
                    </Row>
                    <Row gutter={10}>
                      <Col span={6}>
                        <Form.Item name='bookContact'>
                          <Input />
                        </Form.Item>
                      </Col>
                      <Col span={6} >
                        <Form.Item name='bookEmail' rules={[
                          {
                            pattern: new RegExp(constant.REGEX_EMAIL, 'g'),
                            message: '请输入正确的邮箱',
                          },
                        ]}>
                          <Input />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item name='bookMobilePhone' rules={[
                          {
                            pattern: new RegExp(constant.REGEX_MOBILE, 'g'),
                            message: '请输入正确的手机号',
                          },
                        ]} >
                          <Input />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item name='bookPhone' rules={[
                          // {
                          //   pattern: new RegExp(/^0\d{2,3}-\d{7,8}$/, 'g'),
                          //   message: '请输入正确的电话',
                          // },
                        ]} >
                          <Input />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={24}>
                        上传附件（最多可上传10份附件）
                      </Col>
                    </Row>
                    <Row>
                      <Col span={24}>
                        <Form.Item >
                          <Upload fileList={fileList}
                            //  multiple maxCount={10}
                            accept=".png, .jpg, .doc, .docx, .pdf, .xls, .xlsx, .zip, .rar,"
                            onPreview={(file) => previewFile(file)}
                            onRemove={(file) => deleteFile(file)}
                            customRequest={async (options) => {
                              setUploading(true);
                              const { file, onSuccess, onError } = options;
                              const formData = new FormData();
                              formData.append('businessId', bookOrderId || '');
                              formData.append('file', file);
                              formData.append('path', 'booking');
                              api.bookOrder.uploadDocument(formData).subscribe({
                                next: (data) => {
                                  onSuccess({ message: 'OK' });
                                  message.success('上传成功!');
                                  let list = {
                                    uid: data.id,
                                    name: data.originalFilename,
                                    url: data.url,
                                    size: data.size,
                                    ext: data.ext,
                                    id: data.id
                                  }
                                  let temps = [...fileList, list];
                                  setFileList(temps);
                                },
                                error: (err) => onError(err),
                              }).add(() => {
                                setUploading(false);
                              });;
                            }}

                            beforeUpload={(file) => {
                              return new Promise((resolve, reject) => {
                                if (file.size / (1024 * 1024) < 3) {
                                  resolve();
                                } else {
                                  const err = '上传文件大小应小于3M';
                                  message.error(err);
                                  reject(new Error(err));
                                }
                              });
                            }}
                          >
                            <Button icon={<UploadOutlined />} loading={uploading}>选择文件</Button>
                          </Upload>
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={24}>
                        备注说明
                      </Col>
                    </Row>
                    <Row>
                      <Col span={24}>
                        <Form.Item name='note'>
                          <Input.TextArea style={{ height: '60px' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                </Space>
              </Form>
            </Layout.Content>
            <Layout.Footer style={{ textAlign: 'right', padding: '10px 20px 0 0', background: '#fff' }}>
              <Space>
                <Button type="default" onClick={() => save()} >保存草稿</Button>
                <Button type="primary" onClick={() => commit()}>提交订舱</Button>
              </Space>
            </Layout.Footer>
          </Layout>
          {
            bookOrderId &&
            <Layout.Sider theme="light" width={300} style={{ padding: '0 10px', border: '1px solid #e8e8e8', marginLeft: '10px', overflow: 'hidden' }}>
              <Tabs activeKey={activeKey} items={items} onChange={onChange} />
            </Layout.Sider>
          }

        </Layout>
      </Modal >

      <FileViewer
        visible={previewVisible}
        fileUrl={fileUrl}
        onClose={() => setPreviewVisible(false)}
      />
    </>

  );
};
