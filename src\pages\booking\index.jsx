import React, { useState } from 'react';
import { Button, Row, Col, Form, Input, Select, Space, Divider, Table, Tag, Checkbox, Pagination, Radio, Spin, message } from 'antd';
import { RedoOutlined, DownOutlined, UpOutlined, RightOutlined, AppstoreOutlined, BarsOutlined, FallOutlined, RiseOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import Texty from 'rc-texty';
import QueueAnim from 'rc-queue-anim';
import AnimDate from '@/components/AnimDate';
import AnimPorts from '@/components/AnimPorts';
import AnimInput from '@/components/AnimInput';
import Port from '@/components/Port';
import ShipCompany from '@/components/ShipCompany';
import './index.less';
import { api, copyObject, dateFormat, constant } from '@/common/utils';
import { shipmentIcon } from '@/common/common';
import Booking from '../rate/Booking';
import NoData from '@/components/NoData';
import objectAssign from 'object-assign';
import * as R from 'ramda';
import moment from 'moment';

export default () => {

  const [listType, setListType] = useState('card');
  const [isExpand, setIsExpand] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedCardKeys, setSelectedCardKeys] = useState([]);
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [state, setState] = useState('UNCOMMITED');
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchForm] = Form.useForm();
  const [bookOrders, setBookOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [bookData, setBookData] = useState({});
  const [isBookingOpen, setIsBookingOpen] = useState(false); // 下单
  const [randomId, setRandomId] = useState('');
  const [checkedAll, setCheckedAll] = useState(false); // 下单

  const stateOptions = [
    {
      label: <span className='book-radio'>未提交</span>,
      value: 'UNCOMMITED',
    },
    {
      label: <span className='book-radio'>提交</span>,
      value: 'COMMITED',
    },
    {
      label: <span className='book-radio'>驳回</span>,
      value: 'REJECTED',
    },
    {
      label: <span className='book-radio'>订舱</span>,
      value: 'ORDERING',
    },
    {
      label: <span className='book-radio'>取消</span>,
      value: 'CANCELD',
    },
    {
      label: <span className='book-radio'>放号</span>,
      value: 'HAVED',
    },
    {
      label: <span className='book-radio'>退舱</span>,
      value: 'CANCELD_SHIP',
    },
  ];

  // 开票中每项的展开状态
  const [expandCards, setExpandCards] = useState({});
  const toggleItem = (id) => {
    setExpandCards(prevState => ({
      ...prevState,
      [id]: !prevState[id],
    }));
  };

  const bookColumns = [
    { title: '订舱编号', dataIndex: 'bookCode', width: '160' },
    { title: '船公司', dataIndex: 'shipCompany', width: '100' },
    { title: '中转直达', dataIndex: 'ttOrDt', render: (text, record) => <>{text === '中转' ? (record.ttPol || '') + ' 中转' : '直达'}</> },
    { title: '箱型箱量', dataIndex: 'boxInfo', width: '100' },
    { title: '价格', dataIndex: 'price', width: '200', render: (text, record) => <> {'$ ' + text}</>, sorter: (a, b) => a.price - b.price },
    { title: '件数', dataIndex: 'cargoPkgs', width: '100', render: (text, record) => <div ><span>{text}</span><span className='book-cargo'>{' ' + (record.cargoUnits || '')}</span></div> },
    { title: '重量', dataIndex: 'cargoWeight', width: '100', render: (text, record) => <div ><span>{text}</span><span className='book-cargo'>{' KGS'}</span></div> },
    { title: '尺寸', dataIndex: 'cargoSize', width: '100', render: (text, record) => <div ><span>{text}</span><span className='book-cargo'>{' CBM'}</span></div> },
    // { title: '状态', dataIndex: 'state', width: '100' },
    {
      title: '操作', dataIndex: 'option', width: '200', align: 'center', render: (text, record) => <Space>
        {/* <Button size='small' type='text' danger>进度</Button> */}
        <Button size='small' type='primary' onClick={() => getDetail(record)}>查看</Button></Space>
    },
  ]

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const onSearch = (pageNo, pageSize, state) => {
    setSelectedRowKeys([]);
    setPageNo(pageNo);
    setPageSize(pageSize);
    setSearchLoading(true);
    let searchData = searchForm.getFieldValue();
    copyObject(searchData, { state: state });
    if (searchData.etdRange) {
      copyObject(searchData, { etdStart: searchData.etdRange[0], etdEnd: searchData.etdRange[1] });
    } else {
      copyObject(searchData, { etdStart: null, etdEnd: null });
    }
    let param = { dto: searchData, pageNo: pageNo, pageSize: pageSize };
    api.bookOrder.searchBookOrder(param).subscribe({
      next: (data) => {
        setTotal(data.total);
        setBookOrders(data.data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };

  const onShowSizeChange = (current, pageSize) => {
    onSearch(current, pageSize, state);
  };


  const onStateChange = ({ target: { value } }) => {
    setState(value);
    onSearch(1, pageSize, value);
  };
  //查看明细
  const getDetail = (v) => {
    api.bookOrder.getDetail(v.id).subscribe({
      next: (data) => {
        setBookData(data);
        setIsBookingOpen(true);
      }
    }).add(() => {

    });
  };
  //提交
  const commits = () => {
    let selectKeys = listType === 'table' ? selectedRowKeys : selectedCardKeys;
    if (selectKeys.length === 0) {
      message.warning('请至少选择一条数据!');
      return;
    }
    setLoading(true);
    api.bookOrder.commits(selectKeys).subscribe({
      next: (data) => {
        message.success('操作成功!');
        setSelectedCardKeys([]);
        setCheckedAll(false);
        onSearch(1, pageSize, state);
      }
    }).add(() => {
      setLoading(false);
    });
  }
  const revokes = () => {
    let selectKeys = listType === 'table' ? selectedRowKeys : selectedCardKeys;
    if (selectKeys.length === 0) {
      message.warning('请至少选择一条数据!');
      return;
    }
    setLoading(true);
    api.bookOrder.revokes(selectKeys).subscribe({
      next: (data) => {
        message.success('操作成功!');
        setSelectedCardKeys([]);
        setCheckedAll(false);
        onSearch(1, pageSize, state);
      }
    }).add(() => {
      setLoading(false);
    });
  }
  const changeCardKey = (e, r) => {
    let temps = selectedCardKeys;
    if (e) {
      temps.push(r.id);
    } else {
      temps = temps.filter(item => item !== r.id);
    }
    R.forEach((v) => {
      if (v.id === r.id) {
        objectAssign(v, { checked: e });
      }
    }, bookOrders);
    setRandomId(moment());
    setBookOrders(bookOrders);
    setSelectedCardKeys(temps);
  }
  const changeCardKeys = (e) => {
    R.forEach((v) => {
      objectAssign(v, { checked: e });
    }, bookOrders);
    setRandomId(moment());
    setBookOrders(bookOrders);
    setCheckedAll(e);
    var rowKeys = R.pluck('id', bookOrders);
    setSelectedCardKeys(e ? rowKeys : []);
  }

  return (
    <>
      <Spin spinning={loading}>
        <div className="za-content book">
          <div className="book-content">
            <div className="book-slogan">
              <div className="slogan-title">
                <Texty delay={1000}>订单管理</Texty>
              </div>
              <div className="slogan-sub-title">
                <Texty delay={2000} type="flash">在线订舱,方便快捷,安全可靠,服务好,专业物流,更省心。</Texty>
              </div>
            </div>

            <div className="book-search">
              <Form
                form={searchForm}
                name="searchRef"
                onFinish={() => onSearch(1, pageSize, state)}
              >
                <div className='search-default'>
                  <div className="search-ports">
                    <div className='search-pol'>
                      <Form.Item name="blNo" >
                        <AnimInput label="提单号" />
                      </Form.Item>
                    </div>
                  </div>
                  <div className="search-ports" style={{ margin: '0 0 0 15px' }}>
                    <div className='search-pod'>
                      <Form.Item name="pod" >
                        <AnimPorts label="目的港" />
                      </Form.Item>
                    </div>
                  </div>
                  <div className="search-etd">
                    <Form.Item name="etdRange" >
                      <AnimDate label="船期" beRange={true} />
                    </Form.Item>
                  </div>
                  <Button htmlType="submit" className="search-btn" loading={searchLoading}>查 询</Button>
                </div>

                <QueueAnim className="search-expand">
                  {isExpand ? [
                    <Row gutter={[16, 0]} key="a">
                      <Col span={6}>
                        <Form.Item name="pol" label="起运港" labelCol={{ flex: '80px' }}>
                          <Port label="起运港" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item name="shipCompany" label="船公司" labelCol={{ flex: '80px' }}>
                          <ShipCompany />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item name="bookCode" label="订舱编号" labelCol={{ flex: '80px' }}>
                          <Input />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item name="vessel" label="船名" labelCol={{ flex: '80px' }}>
                          <Input />
                        </Form.Item>
                      </Col>
                    </Row>,
                    <Row gutter={[16, 0]} key="b">
                      <Col span={6}>
                        <Form.Item name="voyage" label="航次" labelCol={{ flex: '80px' }}>
                          <Input />
                        </Form.Item>
                      </Col>
                    </Row>
                  ] : null}
                </QueueAnim>

                <div className='search-tool'>
                  <div className="tool-btn">
                    <Button type="text" icon={<RedoOutlined />} size="small" htmlType="reset">
                      重置
                    </Button>
                    {!isExpand ?
                      <Button type="text" icon={<DownOutlined />} size="small" onClick={() => setIsExpand(true)}>展开</Button>
                      :
                      <Button type="text" icon={<UpOutlined />} size="small" onClick={() => setIsExpand(false)}>收起</Button>
                    }
                  </div>
                </div>
              </Form>
              <Divider style={{ margin: '20px 0' }} />
              <div className='book-list'>
                <div className="book-tools">
                  <div className="book-filter">
                    <Radio.Group options={stateOptions} onChange={onStateChange} value={state} optionType="button" style={{}} />
                  </div>
                  <div className="book-tools-btn">
                    <Space>
                      <Button type='primary' onClick={() => setIsBookingOpen(true)}>下单</Button>
                      <Button type='primary' onClick={() => commits()}>提交</Button>
                      <Button type='primary' onClick={() => revokes()}> 撤销</Button>
                      <Button type="text" icon={<BarsOutlined style={listType == 'table' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('table')}></Button>
                      <Button type="text" icon={<AppstoreOutlined style={listType == 'card' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('card')}></Button>
                    </Space>
                  </div>
                </div>
                {listType == 'table' ?
                  <Table
                    className='book-table'
                    rowKey='id'
                    pagination={false}
                    style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
                    rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
                    columns={bookColumns}
                    dataSource={bookOrders}
                    expandable={{
                      expandedRowRender: (record) => (
                        <div className='book-expand'>
                          <p><span>起运港：</span>{record.pol}  <span>目的港：</span>{record.pod}  <span>航程：</span>{record.shipDays}天   <span>班期：</span>{record.shipSchedule} </p>
                          <p><span>船名航次：</span>{record.vessel}  /{record.voyage}   <span>ETD：</span>{dateFormat(record.etd)}  <span>提单号：</span>{record.blNo}
                            <span>下单时间：</span>{dateFormat(record.createTime, 'yyyy-MM-dd hh:mm')}
                          </p>
                        </div>
                      ),
                      expandIcon: ({ expanded, onExpand, record }) =>
                        expanded ?
                          (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                          :
                          (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                    }}
                  />
                  :
                  <>
                    <div className='card-header'>
                      <div className='header-item' style={{ width: '340px', border: 0, textAlign: 'left' }}>
                        <Checkbox onChange={(e) => changeCardKeys(e.target.checked)} checked={checkedAll} /> <span style={{ marginLeft: '16px' }}>基础信息</span>
                      </div>
                      <div className='header-item' style={{ width: '140px' }}>中转/直达</div>
                      <div className='header-item' style={{ width: '120px' }}>价格</div>
                      <div className='header-item' style={{ width: '100px' }}>件数</div>
                      <div className='header-item' style={{ width: '100px' }}>重量</div>
                      <div className='header-item' style={{ width: '100px' }}>尺寸</div>
                      <div className='header-item' style={{ width: '120px' }}>操作</div>
                    </div>

                    {bookOrders.map((r) => <div className='card-list'>
                      <div className='card-item-top'>
                        <div className='card-item-checkbox'>
                          <Checkbox onChange={(e) => changeCardKey(e.target.checked, r)} checked={r.checked} />
                        </div>
                        <div className='card-item-logo'>
                          <img src={shipmentIcon(r.shipCompany)} />
                        </div>
                        <div className='card-item-ports'>
                          <p>{r.shipCompany}</p>
                          <p style={{ color: '#999' }}>{r.polEnName} → {r.podEnName}</p>
                        </div>
                        <div className='card-item-transfer'>
                          <p>{r.ttOrDt || '直达' + (r.ttOrDt === '中转' ? ' ' + r.ttPol : '')} </p>
                          <p style={{ color: '#999' }}>{dateFormat(r.etd, 'MM.dd') + '-' + dateFormat(r.eta, 'MM.dd') + ' (' + r.shipDays + '天)'}</p>
                        </div>
                        <div className='card-item-price'>
                          <p>$ {r.price}</p>
                          <p style={{ color: '#999' }}>{r.boxInfo}</p>
                        </div>
                        <div className='card-item-book'>
                          <span>{r.cargoPkgs}</span><span className='book-cargo'>{' ' + (r.cargoUnits || '')}</span>
                        </div>
                        <div className='card-item-book'>
                          <span>{r.cargoWeight}</span><span className='book-cargo'>{' ' + 'KGS'}</span>
                        </div>
                        <div className='card-item-book'>
                          <span>{r.cargoSize}</span><span className='book-cargo'>{' ' + 'CBM'}</span>
                        </div>
                        <div className='card-item-option'>
                          <Space>
                            {/* <Button type='text' danger>进度</Button> */}
                            <Button type="primary" onClick={() => getDetail(r)}>查看</Button>
                          </Space>
                        </div>

                      </div>
                      <div className='card-item-bottom'>
                        <p><span>订舱编号：</span>{r.bookCode} <span>船名航次：</span>{r.vessel} /{r.voyage}  <span>航线代码：</span>{r.lineCode}  <span>ETD：{dateFormat(r.etd)}</span>
                          <span>提单号：{r.blNo}</span>
                        </p>
                        {expandCards[r.id] ?
                          <>
                            <p><span>下单时间：</span>{dateFormat(r.createTime, 'yyyy-MM-dd hh:mm')}</p>
                            <Button type="text" icon={<UpOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>收起</Button>
                          </>
                          :
                          <Button type="text" icon={<DownOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>展开</Button>}
                      </div>
                    </div>)}

                    {bookOrders.length == 0 && <NoData />}

                  </>}
              </div>
              <div className='book-pagination'>
                <Pagination
                  pageSizeOptions={[10, 20, 50]}
                  showSizeChanger
                  onShowSizeChange={onShowSizeChange}
                  onChange={onShowSizeChange}
                  // defaultCurrent={p}
                  total={total || 0}
                  current={pageNo || 0}
                  pageSize={pageSize || 10}
                  showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`}
                />

              </div>
            </div>
          </div>
        </div >
      </Spin>
      <Booking
        current={bookData}
        open={isBookingOpen}
        onCancel={() => setIsBookingOpen(false)}
        onSubmit={() => {
          setIsBookingOpen(false);
          onSearch(1, pageSize, state);
        }}
      />

    </>


  );
};
