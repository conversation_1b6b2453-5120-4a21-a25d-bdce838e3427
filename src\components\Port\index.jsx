import React, { useRef, useEffect, useState, useImperativeHandle } from 'react';
import { Select } from 'antd';
import { api, constant } from '@/common/utils';
import debounce from 'lodash/debounce';

export default (props) => {

    const [value, setValue] = useState();
    const delayedQuery = useRef(debounce((value) => searchData(value), 300)).current;
    const [points, setPoints] = useState([]);

    useEffect(() => {
        if (props.label && props.label === '起运港') {
            setPoints(constant.INIT_POLS);
        } else {
            searchData('');
        }
    }, []);

    const searchData = (keywords) => {
        api.base.listPort(keywords).subscribe({
            next: (body) => {
                let data = [];
                data = body.map((item) => ({
                    label: item.enName + "," + item.countryCode + ' ' + item.cnName + " | " + item.countryName,
                    value: item.portEdi,
                }));
                setPoints(data);
            },
        });
    };

    const handleChange = (selectItem) => {
        props.onChange(selectItem?.value || '');
    };


    return (
        <Select {...props}
            value={value}
            allowClear style={{ width: '100%', border: 'none' }}
            showSearch
            labelInValue
            filterOption={false}
            onClear={() => { handleChange() }}
            onChange={(selectItem) => { handleChange(selectItem) }}
            onSearch={(v) => {
                delayedQuery(v);
            }}
        >
            {points.map((item) => (
                <Select.Option key={item.value}>{item.label}</Select.Option>
            ))}
        </Select>
    );
}
