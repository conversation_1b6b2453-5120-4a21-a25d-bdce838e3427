// 运价
@media screen and (max-width: 1280px) {
    .search-carriers {
        width: 40% !important;
    }

    .search-container {
        width: 37% !important;
    }

    .search-ports {
        width: 40% !important;
    }
}

@media screen and (max-width: 1440px) and (min-width: 1280px) {
    .search-carriers {
        width: 25% !important;
    }

    .search-line {
        width: 25% !important;
    }

    //线上
    .change-ports {
        width: 40% !important;
    }

    .search-container {
        width: 37% !important;
    }
}

@media screen and (min-width: 1441px)and(max-width:1560px) {

    //线上
    .change-ports {
        width: 19% !important;
    }

    .search-container {
        width: 19% !important;
    }
}

@media screen and (min-width:1561px) and (max-width:1680px) {
    .search-container {
        width: 20% !important;
    }
}


.rate {
    background: url('../../assets/rate-bg.png');
    background-repeat: no-repeat;
    background-position: top center;

    .rate-biaoji {
        position: fixed;
        top: 56px;
        right: 0;
        color: #fff;
        z-index: 999;
    }

    .rate-content {
        width: 100%;
        // margin: 0 auto;

        .rate-slogan {
            width: 1200px;
            margin: 0 auto;
            height: 190px;
            overflow: hidden;

            .slogan-title {
                font-weight: bold;
                font-size: 38px;
                color: #482828;
                line-height: 53px;
                margin-top: 50px;
            }

            .slogan-sub-title {
                font-weight: 400;
                font-size: 20px;
                color: #482828;
                line-height: 28px;
                margin-top: 14px;
            }
        }

        .rate-tabs {
            // width: 1200px;
            width: calc(100% - 40px - 60px);
            min-width: 1200px;
            margin: 0 auto;
            display: flex;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(8px);
            border-radius: 10px 10px 0 0;
            overflow: hidden;

            .tab-item {
                user-select: none;
                font-size: 16px;
                font-weight: bold;
                line-height: 60px;
                user-select: none;
                padding: 0 30px;
                margin: 0 30px 0 15px;
            }

            .active {
                position: relative;
                background: #fff;
            }

            .active::before {
                content: '';
                position: absolute;
                left: -60px;
                clip-path: path('M 60,0 C 50,0 48.4,4.4 46.4,10 L 32.6,50 C 30.8,55.6 24.8,60 19.2,60 L 60,60 Z');
                background: #fff;
                width: 60px;
                height: 60px;
            }

            .active::after {
                content: '';
                position: absolute;
                right: -60px;
                clip-path: path('M 0,0 C 10,0 11.6,4.4 13.6,10 L 27.4,50 C 29.2,55.6 35.2,60 40.8,60 L 0,60 Z');
                background: #fff;
                width: 60px;
                height: 60px;
            }

            .active span {
                color: #B4141B;
            }

            .unactive span {
                color: #000;
            }
        }

        .rate-search {
            // width: 1160px;
            width: calc(100% - 80px - 60px);
            min-width: 1160px;
            min-height: 100px;
            margin: 0 auto;
            background: #fff;
            border-radius: 0 0 10px 10px;
            box-shadow: 0px 0px 20px #ddd;
            padding: 20px;

            .search-form {
                // width: 1160px;
                // margin: 0 auto;
            }

            .search-default {
                // display: flex;
                margin-bottom: 24px;
            }

            .search-ports {
                width: 35%;
                height: 60px;
                margin-right: 15px;
                border: 1px solid #D9D9D9;
                border-radius: 4px;
                display: flex;

                .search-pol,
                .search-pod {
                    width: 43%;
                }

                .ports-btn {
                    width: 60px;

                    img {
                        width: 36px;
                        height: 36px;
                        margin: 12px;
                        border-radius: 50%;
                        box-shadow: 0 0 5px #ccc;
                    }
                }
            }

            .change-ports {
                width: 25%;
            }

            .search-carriers {
                width: 17%;
                height: 60px;
                border: 1px solid #D9D9D9;
                border-radius: 4px;
                margin-right: 15px;
            }

            .search-line {
                width: 17%;
                height: 60px;
                border: 1px solid #D9D9D9;
                border-radius: 4px;
                margin-right: 15px;
            }

            .search-etd {
                width: 250px;
                height: 60px;
                border: 1px solid #D9D9D9;
                border-radius: 4px;
                margin-right: 15px;
            }

            .search-cargo {
                width: 250px;
                height: 60px;
                border: 1px solid #D9D9D9;
                border-radius: 4px;
                margin-right: 15px;
            }

            .search-carriers-online {
                width: 245px;
                height: 60px;
                margin-left: 15px;
                border: 1px solid #D9D9D9;
                border-radius: 4px;
                margin-right: 15px;
            }

            .margin-r {
                margin-left: 0px;
            }

            .search-container {
                width: 15%;
                height: 60px;
                border: 1px solid #D9D9D9;
                border-radius: 4px;
                margin-right: 15px;
            }

            .search-btn {
                width: 230px;
                height: 60px;
                background: rgba(180, 20, 27, 1);
                color: #fff;
                border-radius: 4px;
                font-size: 22px;
                line-height: 60px;
                text-align: center;
                user-select: none;
            }

            .search-btn:hover {
                color: #fff;
                cursor: pointer;
                background: rgba(180, 20, 27, 0.9);
            }

            .search-expand {
                margin: 0;
                padding: 0;

                .search-more {
                    display: flex;
                    flex-wrap: wrap;

                    // justify-content: space-between;
                    .search-more-form {
                        // width: 24%;
                        width: 420px;
                        margin-left: 15px;
                    }
                }
            }

            .search-tool {
                display: flex;
                justify-content: space-between;
                margin: 0;

                .rate-history {
                    width: 960px;
                    display: flex;

                    span {
                        font-weight: 400;
                        font-size: 12px;
                        color: #BFBFBF;
                        line-height: 20px;
                        margin-right: 16px;
                        cursor: pointer;
                    }
                }

                .tool-btn {
                    width: 150px;
                    display: flex;
                    justify-content: end;

                }

            }
        }

        .rate-list-height {
            height: calc(100vh - (56px + 50px + 60px));
        }

        .card-list-height {
            height: calc(100vh - (56px + 50px + 60px));
            overflow-y: auto;
        }

        .box {
            min-height: 50vh;
            max-height: calc(100vh - (56px + 50px + 60px));
            overflow-y: auto;
            overflow-x: hidden;
            /* 新增此行 */
            margin-top: 10px;
            padding-right: 7px;
            box-sizing: border-box;
        }

        .rate-list {
            min-width: 1160px;
            // width: calc(100% - 80px - 120px);
            margin: 0 auto;
            margin-top: 20px;
            margin-bottom: 13px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0px 0px 20px #ddd;
            padding: 20px;
            // overflow-y: auto;
            position: relative;

            .rate-tools {
                display: flex;
                justify-content: space-between;

                .rate-tools-btn {
                    width: 200px;
                    display: flex;
                    justify-content: end;
                }
            }

            .rate-expand {
                padding: 0 0px;

                p {
                    margin: 0;
                    line-height: 32px;

                    span {
                        color: #999;
                        margin-left: 30px;
                    }
                }
            }

            // .rate-table {
            //     position: relative;

            //     .ant-table-thead {
            //         position: sticky;
            //         top: 0;
            //         z-index: 99;
            //     }

            // }

            .card-header {
                width: 100%;
                background: #FAFAFA;
                border: 1px solid #E8E8E8;
                margin-top: 16px;
                border-radius: 6px 6px 0 0;
                display: flex;
                justify-content: start;
                position: sticky;
                top: 0px;
                z-index: 999;

                .header-item {
                    font-family: 'Arial';
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.88);
                    line-height: 28px;
                    margin: 10px 0;
                    padding: 0 10px 0 9px;
                    border-left: 1px solid #f0f0f0;
                    text-align: center;
                }

            }

            .card-header2 {
                top: 56px;
            }

            .card-list {
                width: 100%;
                border: 1px solid #E8E8E8;
                border-radius: 6px;
                margin-top: 20px;

                .card-item-top {
                    display: flex;
                    justify-content: start;
                    padding: 16px 0;

                    p {
                        margin: 0;
                        line-height: 24px;
                    }

                    .card-item {
                        width: 654px;
                        display: flex;
                    }

                    .card-item-checkbox {
                        width: 16px;
                        margin: 12px 10px;

                    }

                    .card-item-logo {
                        width: 44px;
                        height: 44px;
                        border-radius: 4px;
                        border: 1px solid #E8E8E8;
                        overflow: hidden;

                        img {
                            width: 44px;
                            height: 44px;
                        }
                    }

                    .card-item-ports {
                        width: 300px;
                        padding: 0 16px;

                    }

                    .card-item-transfer {
                        width: 175px;
                        text-align: center;
                    }

                    .card-item-rate {
                        width: 159px;
                        color: #333;
                        font-size: 16px;
                        font-weight: bold;
                        line-height: 45px;
                        text-align: center;
                    }

                    .card-item-note {
                        width: 20%;
                        min-width: 60px;
                        height: 48px;
                        color: #666;
                        font-size: 12px;
                        line-height: 20px;
                        text-align: left;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .card-item-option {
                        width: 180px;
                        text-align: center;
                        padding: 6px 0;
                    }
                }

                .card-item-bottom {
                    border-top: 1px solid #E8E8E8;
                    background: #fafafa;
                    overflow: hidden;
                    position: relative;

                    p {
                        margin: 12px 0;
                        line-height: 20px;
                        color: #999;

                        span {
                            color: #AAA;
                            margin-left: 30px;
                        }

                        a {
                            color: #B4141B;
                        }

                        a:hover {
                            color: #000;
                        }
                    }

                    .card-expand-btn {
                        position: absolute;
                        right: 20px;
                        bottom: 8px;
                        color: #999;
                    }

                }
            }

            .card-list:hover {
                background: #fffbfb;

                .card-item-bottom {
                    background: transparent;
                }
            }
        }

        .grid-rate {
            width: calc(100% - 80px - 60px);
            transition: width 0.5s;
        }

        .table-rate {
            // max-height: 800px;
            // height: 500px;
            position: relative;
            width: calc(100% - 80px - 60px);
            transition: width 0.5s;
        }
    }
}