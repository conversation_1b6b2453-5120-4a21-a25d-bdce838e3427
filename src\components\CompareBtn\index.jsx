import { Button, message } from 'antd';

import './index.less';

export default (props) => {
    const { disabled, dataNum } = props;
    // 动态修改样式
    const changeAnimationDuration = (left, top) => {
        const styleSheet = document.styleSheets[0];
        // 清理样式
        styleSheet.deleteRule(0);
        styleSheet.deleteRule(1);
        // 修改样式表        
        styleSheet.insertRule(`@keyframes xrun { from{ opacity: 0.1; left: 90px; }  to{ opacity: 1; left: ${left + 100}px; } }`, 0);
        styleSheet.insertRule(`@keyframes yrun { from{ top: 10px; }  to{ top: ${top}px; } }`, 1);
    };

    // 加入动画
    const ballRun = () => {
        if (dataNum > 0) {

            const ball = document.getElementById('ball');
            const basket = document.getElementById('basket');

            var ballBasket = ball.getBoundingClientRect();
            var rectBasket = basket.getBoundingClientRect();

            changeAnimationDuration(rectBasket.left - ballBasket.left, rectBasket.top - ballBasket.top);

            ball.className = 'ball parabola';
            setTimeout(() => {
                ball.className = 'ball';
                props.onClick();
            }, 500)
        } else {
            message.warning('请至少选择1条对比数据');
        }
    }

    return <div className='compareRate'>
        <Button onClick={ballRun} disabled={disabled}>加入对比</Button>
        <div className='ball' id="ball"></div>
    </div>
}
