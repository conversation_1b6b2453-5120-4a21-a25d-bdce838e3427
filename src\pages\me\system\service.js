import { constant, isearch, ipost, iget, isget, iput, idelete } from '@/common/utils';



//用户查询
export function searchTenantUser(params) {
  return isearch(constant.API_USER + '/search', params);
}

//激活
export function active(params) {
  return ipost(constant.API_USER + '/active', params);
}
//停用
export function stop(params) {
  return ipost(constant.API_USER + '/stop', params);
}
//启用
export function unstop(params) {
  return ipost(constant.API_USER + '/unstop', params);
}

export function deletes(params) {
  return idelete(constant.API_USER, params);
}

//明细
export function getTenantUser(id) {
  return isget(constant.API_USER + '/' + id);
}

//新增
export function saveOrUpdateTenantUser(params) {
  return params && params.id ? iput(constant.API_USER, params) : ipost(constant.API_USER, params);
}