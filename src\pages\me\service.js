import { constant, getCache, hasCache, iget, isget, ilogin, ipost, iput, opost, rmap, setCache } from '@/common/utils';

//公司信息
export function getTenant(param) {
  return isget(constant.API_USER + '/getTenant/' + param);
}

//更新用户信息
export function updateTenantUser(param) {
  return iput(constant.API_USER, param);
}

//更新企业资质
export function updateTenantInfo(param) {
  return ipost(constant.API_USER + '/updateTenantInfo', param);
}

//修改密码
export function changePassword(param) {
  return ipost(constant.API_USER + '/changePassword', param);
}

//修改手机号
export function changeMobile(param) {
  return ipost(constant.API_USER + '/changeMobile', param);
}

//绑定微信
export function bindWx(param) {
  return ipost(constant.API_USER + '/bindWx', param);
}


