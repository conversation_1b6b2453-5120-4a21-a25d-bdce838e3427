/**
 * Chatwoot用户信息管理工具
 */

/**
 * 获取当前登录用户信息
 */
export const getCurrentUser = () => {
  try {
    const currentUserStr = sessionStorage.getItem('_CURRENT_USER');
    return currentUserStr ? JSON.parse(currentUserStr) : null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

/**
 * 构建Chatwoot用户信息对象
 */
export const buildChatwootUserInfo = (currentUser) => {
  if (!currentUser) return null;
  
  return {
    email: currentUser.userEmail || `${currentUser.userName}@company.com`,
    name: currentUser.userRealCnName || currentUser.userName,
    identifier: currentUser.userName,
    phone: currentUser.userPhone || '',
    company: currentUser.companyName || '',
    department: currentUser.departmentName || '',
    // 可以根据需要添加更多字段
    userCode: currentUser.userCode,
    userId: currentUser.userId
  };
};

/**
 * 构建Chatwoot自定义属性
 */
export const buildChatwootCustomAttributes = (currentUser) => {
  if (!currentUser) return {};
  
  return {
    userId: currentUser.userId,
    userCode: currentUser.userCode,
    loginTime: new Date().toISOString(),
    systemTag: sessionStorage.getItem('_USER_SYSTEM_TAG') || 'business',
    userType: currentUser.userType || 'customer',
    // 可以根据需要添加更多自定义属性
    lastLoginTime: currentUser.lastLoginTime,
    userStatus: currentUser.userStatus
  };
};

/**
 * 设置Chatwoot用户信息
 */
export const setChatwootUser = (currentUser = null) => {
  if (!window.chatwootSDK) {
    console.warn('Chatwoot SDK not available');
    return;
  }

  try {
    const user = currentUser || getCurrentUser();
    
    if (user) {
      const userInfo = buildChatwootUserInfo(user);
      const customAttributes = buildChatwootCustomAttributes(user);
      
      console.log('Setting Chatwoot user:', userInfo);
      
      // 设置用户信息
      window.chatwootSDK.setUser(user.userName, userInfo);
      
      // 设置自定义属性
      window.chatwootSDK.setCustomAttributes(customAttributes);
      
      console.log('Chatwoot user info updated successfully');
    } else {
      console.log('No user logged in, using anonymous mode');
    }
  } catch (error) {
    console.error('Error setting Chatwoot user:', error);
  }
};

/**
 * 重置Chatwoot（用户登出时调用）
 */
export const resetChatwoot = () => {
  if (!window.chatwootSDK) {
    console.warn('Chatwoot SDK not available');
    return;
  }

  try {
    window.chatwootSDK.reset();
    console.log('Chatwoot reset successfully');
  } catch (error) {
    console.error('Error resetting Chatwoot:', error);
  }
};

/**
 * 更新Chatwoot用户的自定义属性
 */
export const updateChatwootCustomAttributes = (attributes) => {
  if (!window.chatwootSDK) {
    console.warn('Chatwoot SDK not available');
    return;
  }

  try {
    window.chatwootSDK.setCustomAttributes(attributes);
    console.log('Chatwoot custom attributes updated:', attributes);
  } catch (error) {
    console.error('Error updating Chatwoot custom attributes:', error);
  }
};
