import React, { useState } from 'react';
import { Form, Row, Col, Button, Pagination, Table, Tag, Input, DatePicker, Space, Modal, Badge } from 'antd';
import './index.less';
import Menu from '../../components/Menu';
import Port from '@/components/Port';
import { api, dateFormat } from '@/common/utils';
import ShipCompany from '@/components/ShipCompany';
import { DownOutlined, UpOutlined, RightOutlined, AppstoreOutlined, BarsOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import objectAssign from 'object-assign';
import { shipmentIcon } from '@/common/common';
import Booking from '@/pages/rate/Booking';
import Detail from '@/pages/order/detail';
import Track from '@/pages/order/track';
import Position from '@/components/Position';


export default () => {
  const [searchForm] = Form.useForm();
  const [datas, setDatas] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [listType, setListType] = useState('card');
  const [rateData, setRateData] = useState({});
  const [isBookingOpen, setIsBookingOpen] = useState(false); // 下单
  const [trackInfo, setTrackInfo] = useState({});
  const [showTrace, setShowTrace] = useState(false);
  const [showVessel, setShowVessel] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [vessel, setVessel] = useState();
  const [orderId, setOrderId] = useState('');

  const rateTrendMap = {
    "DOWN": <ArrowDownOutlined style={{ color: 'green' }} />,
    "UP": <ArrowUpOutlined style={{ color: 'red' }} />,
  }
  const shipScheduleMap = { '0': "周日", "1": "周一", "2": "周二", "3": "周三", "4": "周四", "5": "周五", "6": "周六", };
  const stateStatus = {
    TODO: { color: 'warning', text: '未下单' },
    DONE: { color: 'success', text: '已下单' },
    EXPIRED: { color: 'error', text: '已过期' },
  };

  const columns = [
    { title: '提单号', dataIndex: 'blNo', width: 140, ellipsis: true },
    // { title: '委托单位', dataIndex: 'entrustCustomer', width: 140, ellipsis: true },
    { title: '船公司', dataIndex: 'shipCompany', width: 100, ellipsis: true },
    { title: '船名航次', dataIndex: 'vessel', width: 160, ellipsis: true, render: (text, record) => <span>{(record.vessel || '') + ' / ' + (record.voyage || '')}</span> },
    { title: 'ATD', dataIndex: 'atd', align: 'center', width: 100, render: (text, record) => <span>{dateFormat(text)}</span> },
    // { title: '箱型箱量', dataIndex: 'boxInfo', align: 'center', width: 100 },
    { title: '预警信息', dataIndex: 'warnName', align: 'center', width: 100, render: (text, record) => <Tag color='error'>{text}</Tag> },
    {
      title: '操作栏', dataIndex: 'option', align: 'center', width: 220, render: (text, record) =>
        <>
          <Button size='small' type='text' danger onClick={() => openDetail(record.id)}>业务信息</Button>
          <Button size='small' type='text' danger onClick={() => openTrace(record.id)}>运踪状态</Button>
          <Button size='small' type='text' danger onClick={() => openVessel(record.vessel)}>船舶轨迹</Button>
        </>
    },
  ]


  const onSearch = (pageNo, pageSize) => {
    setSearchLoading(true);
    setPageNo(pageNo);
    setPageSize(pageSize);
    let searchData = searchForm.getFieldValue();
    let param = { dto: searchData, pageNo: pageNo, pageSize: pageSize };
    api.order.searchWarnOrder(param).subscribe({
      next: (data) => {
        setTotal(data.total);
        setDatas(data.data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };


  const onShowSizeChange = (current, pageSize) => {
    onSearch(current, pageSize);
  };

  const onSelectChange = (selectedKeys, selectedRows) => {
    setSelectedRowKeys(selectedKeys);
  };


  // 开票中每项的展开状态
  const [expandCards, setExpandCards] = useState({});
  const toggleItem = (id) => {
    setExpandCards(prevState => ({
      ...prevState,
      [id]: !prevState[id],
    }));
  };

  // 下单
  const onBooking = (data) => {
    let bookData = {};
    objectAssign(bookData, {
      runDate: data.runDate && data.expireDate ? (data.runDate + '~' + data.expireDate.substring(5, 10)) : '', pol: data.pol, pod: data.pod, polEnName: data.polEnName, podEnName: data.podEnName, ttOrDt: data.ttOrDt, ttPol: data.ttPol,
      shipCompany: data.shipCompany, gp20Price: data.gp20Price, gp40Price: data.gp40Price, hc40Price: data.hc40Price, etd: data.etd,
      vessel: data.vessel, voyage: data.voyage, shipDays: data.shipDays, shipSchedule: data.shipSchedule, rateType: data.rateType, planId: data.id,
    });
    setRateData(bookData);
    setIsBookingOpen(true);
  }

  // 打开运踪详情
  const openTrace = (id) => {
    api.order.getOrderTrack(id).subscribe({
      next: (data) => {
        if (!data.pol || !data.pod) {
          message.warning("目前尚未获取到运踪信息,请稍后再试!");
          return;
        }
        objectAssign(data, { containerTotal: data.containers ? data.containers?.length : 0 });
        setTrackInfo(data);
        setShowTrace(true);
      }
    }).add(() => {

    });
  };

  // 打开船舶轨迹
  const openVessel = (value) => {
    if (!value) {
      message.warning("未获取到船名信息~");
      return;
    }
    setVessel(value);
    setShowVessel(true);
  };
  // 打开提单详情
  const openDetail = (id) => {
    setOrderId(id);
    setShowDetail(true);
  };

  return (<>

    <Menu selectKeys={["orderWarn"]} openKeys={["business"]} >
      <>
        <div className="orderWarn-search">
          <Form form={searchForm}>
            <Row wrap={false} gutter={20}>
              <Col flex={'auto'}>
                <Row>
                  <Col span={8}>
                    <Form.Item name="blNo" label="提单号" labelCol={{ flex: '90px' }}>
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="pol" label="起运港" labelCol={{ flex: '90px' }}>
                      <Port placeholder="请选择" label="起运港" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="pod" label="目的港" labelCol={{ flex: '90px' }}>
                      <Port placeholder="请选择" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="shipCompanys" label="船公司" labelCol={{ flex: '90px' }}>
                      <ShipCompany placeholder="请选择" mode='multiple' />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="etdStart" label="船期起" labelCol={{ flex: '90px' }}>
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="etdEnd" label="船期止" labelCol={{ flex: '90px' }}>
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col flex={'100px'}>
                <Space size={[10, 24]} wrap>
                  <Button type="primary" htmlType="submit" onClick={() => onSearch()} loading={searchLoading}> 查 询 </Button>
                  <Button type="default" htmlType="reset"> 重 置 </Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </div>
        <div className='orderWarn-list'>
          <div className="orderWarn-tools">
            <div className="orderWarn-filter">

            </div>
            <div className="orderWarn-tools-btn">
              <Space>
                <Button type="text" icon={<BarsOutlined style={listType == 'table' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('table')}></Button>
                <Button type="text" icon={<AppstoreOutlined style={listType == 'card' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('card')}></Button>
              </Space>
            </div>
          </div>
          {listType == 'table' ?
            <Table
              className='orderWarn-table'
              rowKey='id'
              pagination={false}
              style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
              // rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
              columns={columns}
              dataSource={datas}
              expandable={{
                expandedRowRender: (record) => (
                  <div className='orderWarn-expand'>
                    <p><span>起运港：</span>{record.polName}  <span>目的港：</span>{record.podName} <span>箱型箱量：</span>{record.boxInfo}
                      <span>截单时间：</span>{dateFormat(record.cot, 'yyyy-MM-dd hh:mm')}
                    </p>
                  </div>
                ),
                expandIcon: ({ expanded, onExpand, record }) =>
                  expanded ?
                    (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                    :
                    (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
              }}
            />
            :
            <>
              <div className='card-header'>
                <div className='header-item' style={{ width: '154px', textAlign: 'left', paddingLeft: '20px' }}>提单号</div>
                <div className='header-item' style={{ width: '279px' }}>基础信息</div>
                <div className='header-item' style={{ width: '220px' }}>船名航次</div>
                <div className='header-item' style={{ width: '100px' }}>ATD</div>
                <div className='header-item' style={{ width: '100px' }}>预警信息</div>
              </div>

              {datas.map((r) =>
                <Badge.Ribbon text={r.warnName}>
                  <div className='card-list'>
                    <div className='card-item-top'>
                      <div className='card-item-cus'>
                        <span>{r.blNo}</span><RightOutlined style={{ color: '#cccccc' }} onClick={() => openDetail(r.id)} />
                      </div>
                      <div className='card-item-logo'>
                        <img src={shipmentIcon(r.shipCompany)} />
                      </div>
                      <div className='card-item-ports'>
                        <p>{r.shipCompany} </p>
                        <p style={{ color: '#999' }}>{r.polEnName} → {r.podEnName}</p>
                      </div>
                      <div className='card-item-vess'>
                        <div className='vessel-text' title={r.vessel + ' / ' + r.voyage}>{r.vessel} / {r.voyage}</div>
                        <div>
                          <RightOutlined style={{ color: '#cccccc' }} onClick={() => openVessel(r.vessel)} />
                        </div>
                      </div>
                      <div className='card-item-atd'>
                        <p>{dateFormat(r.atd)} </p>
                      </div>
                      <div className='card-item-option'>
                        <Button size='small' type='primary' onClick={() => openTrace(r.id)}>运踪详情</Button>
                      </div>

                    </div>
                    <div className='card-item-bottom'>
                      <p>
                        <span style={{ marginLeft: '20px' }}>起运港：</span>{r.polName}  <span>目的港：</span>{r.podName}
                        <span>箱型箱量：</span>{r.boxInfo}
                        <span>截单时间：</span>{dateFormat(r.cot, 'yyyy-MM-dd hh:mm')}
                      </p>

                      {/* {expandCards[r.id] ?
                    <>
                      <p> 
                        <span>班期：</span>{shipScheduleMap[r.shipSchedule]} </p>
                      <Button type="text" icon={<UpOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>收起</Button>
                    </>
                    :
                    <Button type="text" icon={<DownOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>展开</Button>} */}
                    </div>
                  </div>
                </Badge.Ribbon>
              )}

            </>
          }
        </div>
        <div className='orderWarn-pagination'>
          <Pagination
            pageSizeOptions={[10, 20, 50]}
            showSizeChanger
            onShowSizeChange={onShowSizeChange}
            onChange={onShowSizeChange}
            // defaultCurrent={p}
            total={total || 0}
            current={pageNo || 0}
            pageSize={pageSize || 10}
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`}
          />

        </div>
      </>
    </Menu>

    <Booking
      current={rateData}
      open={isBookingOpen}
      onCancel={() => setIsBookingOpen(false)}
      onSubmit={() => {
        setIsBookingOpen(false);
        onSearch(1, pageSize);
      }}
    />

    {/* 运踪 */}
    <Track
      open={showTrace}
      current={trackInfo}
      onCancel={() => setShowTrace(false)}
    />
    {/* 船舶轨迹 */}
    <Modal
      title="船舶轨迹"
      open={showVessel}
      width={1400}
      styles={{ body: { height: 'calc(100vh - 200px)' } }}
      centered
      footer={null}
      onCancel={() => setShowVessel(false)}
    >
      <Position vessel={vessel} />
    </Modal>
    {/* 提单详情 */}
    <Detail
      open={showDetail}
      orderId={orderId}
      onCancel={() => { setOrderId(''); setShowDetail(false) }}
    />
  </>

  );
};
