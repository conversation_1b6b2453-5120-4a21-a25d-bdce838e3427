import React, { useRef, useState, useEffect, useCallback } from 'react';
import { Button, Row, Col, Form, Modal, Select, Space, message, Spin, Checkbox, DatePicker, ConfigProvider, Badge, Tag, Tooltip } from 'antd';
import { RedoOutlined, DownOutlined, UpOutlined, RightOutlined, AppstoreOutlined, BarsOutlined, ArrowUpOutlined, ArrowDownOutlined, ConsoleSqlOutlined, } from '@ant-design/icons';
import Grid from '@/components/Grid';
import QueueAnim from 'rc-queue-anim';
import './index.less';
import AnimCarrier from '@/components/AnimCarrier';
import AnimPorts from '@/components/AnimPortsMap';
import changeBtn from '@/assets/changeBtn.png';
import { api, copyObject, dateFormat, constant, getTimeInterval, dateDifferenceInDays } from '@/common/utils';
import { shipmentIcon } from '@/common/common';
import ShipSchedule from '@/components/ShipSchedule';
import Quote from './Quote';
import Booking from './Booking';
import NoData from '@/components/NoData';
import CompareBtn from '@/components/CompareBtn';
import { history } from "umi";
import Line from '@/components/Line';
import LineOff from '@/components/LineOff';
import Schedule from '@/components/schedule'
import * as R from 'ramda';
import objectAssign from 'object-assign';
import RateTrend from './rateTrend';


export default (props) => {

    const shipSchedulesSelect = [{ key: 0, value: '周日' }, { key: 1, value: "周一" }, { key: 2, value: "周二" }, { key: 3, value: "周三" }, { key: 4, value: "周四" }, { key: 5, value: "周五" }, { key: 6, value: "周六" }];
    const shipScheduleCnMap = { '周日': "0", "周一": "1", "周二": "2", "周三": "3", "周四": "4", "周五": "5", "周六": "6", };
    const cabinStateMap = { "充足": { color: 'success' }, "紧张": { color: 'warning' }, "爆仓": { color: 'error' }, "接甩": { color: 'default' } };

    const [listType, setListType] = useState('');
    const refPol = useRef();
    const refPod = useRef();
    const gridRef = useRef();
    const [isExpand, setIsExpand] = useState(false);
    const [isShowsear, setIsShowSear] = useState(true);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);//线下表格选中的数组
    const [searchLoading, setSearchLoading] = useState(false);
    const [originalDatas, setOriginalDatas] = useState([]);
    const [freightPrices, setFreightPrices] = useState([]);
    const current = JSON.parse(sessionStorage.getItem(constant.KEY_CURRENT_USER));
    const freight_histor_key = current?.id + "_history_freight";
    const offline_listType = "offline_listType";
    const [freightHistorys, setFreightHistorys] = useState([]);
    const [searchForm] = Form.useForm();
    const [filterForm] = Form.useForm();
    const [pol, setPol] = useState('');
    const [pod, setPod] = useState('');
    const [boxTypes, setBoxTypes] = useState([]);
    const [total, setTotal] = useState(0);
    const [pageNo, setPageNo] = useState(0);
    const [pageSize, setPageSize] = useState(10);
    const [angle, setAngle] = useState(0); // 初始旋转角度为0度

    const [isModalOpen, setIsModalOpen] = useState(false); // 报价方案
    const [rateData, setRateData] = useState([]);
    const [isBookingOpen, setIsBookingOpen] = useState(false); // 下单
    const [loading, setLoading] = useState(false);
    const [trendOpen, setTrendOpen] = useState(false);

    //定义宽度，根据不同屏幕尺寸显示不同列
    const [isScreen, setIsScreen] = useState(window.innerWidth >= 1280)  //大屏
    const [isBigScreen, setIsBigScreen] = useState(window.innerWidth >= 1440)  //大屏
    useEffect(() => {
        const handleResize = () => {
            const shouldBeLarge = window.innerWidth >= 1280
            const shouldBigBeLarge = window.innerWidth >= 1440
            if (shouldBeLarge !== isScreen) {
                setIsScreen(shouldBeLarge)
            }
            if (shouldBigBeLarge !== isBigScreen) {
                setIsBigScreen(shouldBigBeLarge)
            }
        }
        handleResize();
        window.addEventListener('resize', handleResize)
        return () => {
            window.removeEventListener('resize', handleResize)
        }
    })

    const handleImageClick = () => {
        setAngle(angle + 360); // 每次点击增加360度
        if (pol || pod) {
            let polTemp = pol, podTemp = pod;
            setPol(podTemp);
            setPod(polTemp);
            refPol.current?.setValue({ portName: pod ? (pod.enName + ' ' + pod.cnName) : '' });
            refPod.current?.setValue({ portName: pol ? (pol.enName + ' ' + pol.cnName) : '' });
            searchForm.setFieldsValue({ pol: pod ? pod.portEdi : '', pod: pol ? pol.portEdi : '' });
        }
    };

    //设置查询历史
    useEffect(() => {
        let storedString = localStorage.getItem(freight_histor_key);
        let items = storedString ? JSON.parse(storedString) : [];
        setFreightHistorys(items);
        let boxs = ["20GP", "40GP", "40HC", "40NOR", "45HC"], states = ["有效"];
        filterForm.setFieldsValue({ boxType: boxs, state: states });
        setBoxTypes(boxs);
        setListType(localStorage.getItem(offline_listType) || 'table');
    }, []);


    // 开票中每项的展开状态
    const [expandCards, setExpandCards] = useState({});
    const toggleItem = (id) => {
        setExpandCards(prevState => ({
            ...prevState,
            [id]: !prevState[id],
        }));
    };

    const rateColumns = [
        { headerName: '选择框', field: 'id', width: '30', checkboxSelection: true, headerCheckboxSelection: true, pinned: 'left', resizable: false },
        { headerName: '状态', field: 'freightState', width: '100', cellRenderer: (x) => { return <>{getTimeInterval(Date.now(), x.data.updateTime)}</> } },
        { headerName: '航线', field: 'lineName', width: '90', },
        { headerName: '航线代码', field: 'lineCode', width: '90', },
        { headerName: '船公司', field: 'shipCompany', width: '90' },
        { headerName: '起运港', field: 'polName', width: '150', cellRenderer: (x) => { return <> {x.value ? <Tooltip placement="bottomLeft" title={x.value}> {x.value} </Tooltip> : ''}</> } },
        { headerName: '目的港', field: 'podName', width: '180', cellRenderer: (x) => { return <> {x.value ? <Tooltip placement="bottomLeft" title={x.value}> {x.value} </Tooltip> : ''}</> } },
        { headerName: '目的港挂靠', field: 'podAttachment', width: '100' },
        { headerName: '班期', field: 'shipSchedule', width: '90' },
        { headerName: '船期', field: 'etd', width: '120', sortable: true, valueFormatter: (x) => dateFormat(x.value, 'yyyy-MM-dd'), },
        { headerName: '航程', field: 'shipDays', width: '90', sortable: true, cellRenderer: (x) => { return <>{x.value}天</> } },
        { headerName: '中转/直达', field: 'ttOrDt', width: '100', cellRenderer: (x) => <>{x.value === '中转' ? '中转' : '直达'}</> },
        { headerName: '中转港', field: 'ttPol', width: '160', cellRenderer: (x) => { return <> {x.value ? <Tooltip placement="bottomLeft" title={x.value}> {x.value} </Tooltip> : ''}</> } },
        { headerName: '20GP', field: 'gp20Price', width: '100', sortable: true, cellRenderer: (x) => <>{x.value ? '$' + x.value : '-'} {x.value ? rateTrendMap[x.data.gp20Trend] : ""}</> },
        { headerName: '40GP', field: 'gp40Price', width: '100', sortable: true, cellRenderer: (x) => <>{x.value ? '$' + x.value : '-'} {x.value ? rateTrendMap[x.data.gp40Trend] : ""}</> },
        { headerName: '40HC', field: 'hc40Price', width: '100', sortable: true, cellRenderer: (x) => <>{x.value ? '$' + x.value : '-'} {x.value ? rateTrendMap[x.data.hc40Trend] : ""}</> },
        { headerName: '40NOR', field: 'nor40Price', width: '100', sortable: true, cellRenderer: (x) => <>{x.value ? '$' + x.value : '-'} {x.value ? rateTrendMap[x.data.nor40Trend] : ""}</> },
        { headerName: '45HC', field: 'hc45Price', width: '100', sortable: true, cellRenderer: (x) => <>{x.value ? '$' + x.value : '-'} {x.value ? rateTrendMap[x.data.hc45Trend] : ""}</> },
        { headerName: '舱位情况', field: 'cabinState', width: '100', cellRenderer: (x) => <Tag color={cabinStateMap[x.value]?.color}>{x.value}</Tag> },
        { headerName: '箱使说明', field: 'podContainerFreeDays', width: '150', cellRenderer: (x) => { return <> {x.value ? <Tooltip placement="bottomLeft" title={x.value}> {x.value} </Tooltip> : ''}</> } },
        { headerName: '订舱备注', field: 'bookNote', width: '150', cellRenderer: (x) => { return <> {x.value ? <Tooltip placement="bottomLeft" title={<p style={{ whiteSpace: 'pre-line', margin: 0 }}>{x.data.bookNote}<br />{x.data.weightLimitNote}</p>}> {x.data.bookNote} {x.data.weightLimitNote} </Tooltip> : ''}</> } },
        { headerName: '生效日期', field: 'effectiveDate', width: '120', sortable: true, valueFormatter: (x) => dateFormat(x.value, 'yyyy-MM-dd') },
        { headerName: '失效日期', field: 'ineffectiveDate', width: '120', sortable: true, valueFormatter: (x) => dateFormat(x.value, 'yyyy-MM-dd') },
        { headerName: '更新时间', field: 'updateTime', width: '160', sortable: true, valueFormatter: (x) => dateFormat(x.value, 'yyyy-MM-dd hh:mm') },
        {
            headerName: '　　　操作', field: 'option', width: '160', pinned: 'right', resizable: false,
            cellRenderer: (x) => <Space>
                <Button size='small' type='text' danger onClick={() => showTrend(x.data)}>走势</Button>
                {/* <Button size='small' type="primary" onClick={() => onBooking(x.data)}>下单</Button> */}
                <Button size='small' type='primary' onClick={() => showModal(x.data)}>复制报价</Button>
            </Space>
        },
    ]


    const tags = [{ color: 'error', text: '爆仓' }, { color: 'success', text: '充足' }, { color: 'warning', text: '紧张' }]
    const rateTrendMap = {
        "DOWN": <ArrowDownOutlined style={{ color: 'green' }} />,
        "UP": <ArrowUpOutlined style={{ color: 'red' }} />,
    }


    const onSelectedChanged = (rows) => {
        var rowKeys = R.pluck('id', rows);
        setSelectedRowKeys(rowKeys);
        let list = []
        rows.map(item => {
            list.push({ ...item, priceType: 0 })
        })
        setSelectedRows(list);
    };

    const onSearch = () => {
        searchOffline(1, pageSize);
        if (pol && pod) {
            let newItem = {
                pol: pol.portEdi,
                pod: pod.portEdi,
                polName: pol.enName + ' ' + pol.cnName,
                podName: pod.enName + ' ' + pod.cnName,
                polEnName: pol.enName,
                polCnName: pol.cnName,
                podEnName: pod.enName,
                podCnName: pod.cnName,
                id: pol.enName + ' - ' + pod.enName,
            }
            updateLocalStorageArray(freight_histor_key, newItem);
        }
    };

    const searchOffline = (pageNo, pageSize) => {
        let value = searchForm.getFieldValue();
        setPageNo(pageNo);
        setPageSize(pageSize);
        setSearchLoading(true);
        let param = { dto: value, pageNo: pageNo, pageSize: pageSize };
        api.freightPrice.searchOffline(param).subscribe({
            next: (data) => {
                setTotal(data.total);
                // setOriginalDatas(oriData);
                setFreightPrices(data.data);
            }
        }).add(() => {
            setSearchLoading(false);
        });
    }


    function updateLocalStorageArray(key, newItem) {
        let storedString = localStorage.getItem(key);
        let items = storedString ? JSON.parse(storedString) : [];
        for (let i = 0; i < items.length; i++) {
            if (items[i].id === newItem.id) {
                //存在 则删除
                items.splice(i, 1);
                break;
            }
        }
        //插入
        items.unshift(newItem);
        //保留3个
        if (items.length > 3) {
            items.pop();
        }
        setFreightHistorys(items);
        let updatedString = JSON.stringify(items);
        //设置缓存
        localStorage.setItem(key, updatedString);
    }


    const changeType = (type) => {
        setSelectedRowKeys([]);
        setSelectedRows([]);
        setListType(type);
        localStorage.setItem(offline_listType, type);
    }

    // 报价窗口
    const showModal = (current) => {
        console.log(current)
        setIsModalOpen(true);
        if (current == "multi") {
            setRateData(selectedRows)
        } else {
            setRateData([current]);
        }
    }
    const showTrend = (current) => {
        setTrendOpen(true);
        setRateData(current);
    }

    //大框航线选中
    const handleLineChange = (lineId) => {

    }

    // 下单
    const onBooking = (data) => {
        if (data.etd && dateDifferenceInDays(Date.now(), data.etd) <= 5) {
            let dateDiff = dateDifferenceInDays(Date.now(), data.etd);
            if (dateDiff <= 0) {
                message.error("开船日必须大于今天~");
            } else {
                Modal.confirm({
                    title: '距离开船日仅剩' + dateDiff + '天,您确定要下单吗？',
                    okText: '确定',
                    okType: 'danger',
                    cancelText: '取消',
                    onOk() {
                        confirmBooking(data);
                    },
                });
            }
        } else {
            confirmBooking(data);
        }
    }
    const confirmBooking = (data) => {
        let bookData = {};
        copyObject(bookData, {
            runDate: data.effectiveDateStr, pol: data.pol, pod: data.pod, polEnName: data.polEnName, podEnName: data.podEnName, ttOrDt: data.ttOrDt, ttPol: data.ttPol,
            polId: data.polId, podId: data.podId, shipCompanyId: data.shipCompanyId, shipCompany: data.shipCompany,
            gp20Price: data.gp20Price, gp40Price: data.gp40Price, hc40Price: data.hc40Price, etd: data.etd,
            otherPrice: data.nor40Price, otherPriceOne: data.hc45Price,
            vessel: data.vessel, voyage: data.voyage, shipDays: data.shipDays, shipSchedule: shipScheduleCnMap[data.shipSchedule], rateType: '线下'
        });
        setRateData(bookData);
        setIsBookingOpen(true);
    }


    //运价导出
    const doExports = (data) => {
        let searchData = searchForm.getFieldValue();
        objectAssign(searchData, { ids: selectedRowKeys });

        if (!searchData.pol) {
            message.error("请选择起运港!");
            return;
        }
        if (!searchData.pod) {
            message.error("请选择目的港!");
            return;
        }
        let fileName = "运价-" + dateFormat(Date.now(), 'yyyy-MM-dd hh:mm') + ".xlsx";
        setLoading(true);
        api.freightPrice.exports(searchData).subscribe({
            next: (res) => {
                const blob = new Blob([res], {
                    type: 'application/octet-stream',
                });
                if (window.navigator.msSaveOrOpenBlob) {
                    navigator.msSaveBlob(blob, fileName);
                } else {
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    // 释放内存
                    window.URL.revokeObjectURL(link.href);
                }
            }
        }).add(() => {
            setLoading(false);
        });

    }




    //滚动事件监听
    const contentRef = useRef(null);
    const [isTouching, setIsTouching] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            if (!contentRef.current) return;
            const contentTop = contentRef.current.getBoundingClientRect().top;
            const navHeight = 56;
            setIsTouching(contentTop <= navHeight);
        };

        // 节流函数
        const throttle = (func, limit) => {
            let inThrottle;
            return (...args) => {
                if (!inThrottle) {
                    func(...args);
                    inThrottle = true;
                    setTimeout(() => (inThrottle = false), limit);
                }
            };
        };

        const throttledScroll = throttle(handleScroll, 100);
        window.addEventListener('scroll', throttledScroll);
        return () => window.removeEventListener('scroll', throttledScroll);
    }, []);

    //加入对比
    const addNum = () => {
        console.log(selectedRows)
        let arr = JSON.parse(localStorage.getItem('allData')) || [];//获取本地存储数组
        let idSet = new Set(arr.map(item => item.id));//将数组转为set
        let flag = selectedRows.some(item => idSet.has(item.id))
        let allArray = [] //合并后的数据
        if (selectedRows.length > 0) {
            if (arr.length > 5 || selectedRows.length > 5 || cardList.length > 5) {
                message.error('最多只能对比5条')
                return
            } else {
                if (listType == 'table') {
                    if (flag == true) {
                        message.error('已有数据加入对比')
                        gridRef.current.onClean()
                    } else {
                        allArray = arr.concat(selectedRows)
                        if (allArray.length <= 5) {
                            const num = allArray.length || 0; // 获取本地存储的数组长度（对比数量）
                            localStorage.setItem('compareCount', num);
                            props.onCompare(num)
                            localStorage.setItem('allData', JSON.stringify(allArray));
                            gridRef.current.onClean()
                        } else {
                            message.error('最多只能对比5条')
                            gridRef.current.onClean()
                        }
                    }
                } else {
                    if (flag == true) {
                        message.error('已有数据加入对比')
                    } else {
                        allArray = arr.concat(selectedRows)
                        if (allArray.length <= 5) {
                            const num = allArray.length || 0; // 获取本地存储的数组长度（对比数量）
                            localStorage.setItem('compareCount', num);
                            props.onCompare(num)
                            localStorage.setItem('allData', JSON.stringify(allArray));
                        } else {
                            message.error('最多只能对比5条')
                        }
                    }
                }
            }
        }
    }
    const [cardList, setCardList] = useState([])
    //卡片式选中
    // const addItem = (r, e) => {
    //     const flag = e.target.checked;
    //     let arr = [...cardList];
    //     if (flag == true) {
    //         arr.push(r);
    //     } else {
    //         arr = arr.filter((item) => item.id !== r.id);
    //     }
    //     setCardList(arr);
    //     setSelectedRows(arr)
    // }


    const cardSelectedALl = (e) => {
        if (freightPrices.length === 0) {
            message.error("请至少选择一条数据!");
            return;
        }
        let temps = freightPrices;
        R.forEach((v) => {
            objectAssign(v, { checked: e })
        })(temps);
        setFreightPrices(temps);
        if (e) {
            var rowKeys = R.pluck('id', temps);
            setSelectedRowKeys(rowKeys);
            setSelectedRows(temps);
        } else {
            setSelectedRowKeys([]);
            setSelectedRows([]);
        }

    }

    const cardSelected = (e, r) => {
        if (freightPrices.length === 0) {
            message.error("请至少选择一条数据!");
            return;
        }
        let temps = [];
        freightPrices.map((v) => {
            temps.push({ ...v, priceType: 0 })
        })
        R.forEach((v) => {
            if (r.id === v.id) {
                objectAssign(v, { checked: e })
            }
        })(temps);
        setFreightPrices(temps);
        let selectedRowTemps = temps.filter((item) => item.checked);
        var rowKeys = R.pluck('id', selectedRowTemps);
        setSelectedRowKeys(rowKeys);
        setSelectedRows(selectedRowTemps);
    }


    return (
        <>
            <Spin spinning={loading}>
                <div style={props.style}>
                    {/* <div className='rate-biaoji' >标记：{isTouching ? '已滚动' : '未滚动'}</div> */}
                    <div className="rate-search">
                        <Form
                            form={searchForm}
                            name="searchRef1"
                            onFinish={onSearch}
                            className='search-form'
                        >
                            <div className='search-default' style={{ display: 'flex' }}>
                                <div className="search-ports">
                                    <div className='search-pol'>
                                        <Form.Item name="pol" >
                                            <AnimPorts label="起运港" getSelectItem={(e) => setPol(e)} childRef={refPol} />
                                        </Form.Item>
                                    </div>
                                    <div className='ports-btn'>
                                        <img src={changeBtn} style={{ transition: 'transform 0.5s', transform: `rotate(${angle}deg)` }}
                                            onClick={handleImageClick}
                                        />
                                    </div>
                                    <div className='search-pod'>
                                        <Form.Item name="pod" >
                                            <AnimPorts label="目的港" labelalgin="right" getSelectItem={(e) => setPod(e)} childRef={refPod} />
                                        </Form.Item>
                                    </div>

                                </div>
                                <div className="search-carriers">
                                    <Form.Item name="shipCompanys" >
                                        <AnimCarrier label="船公司" mode='multiple' />
                                    </Form.Item>
                                </div>
                                {isScreen ?
                                    <>
                                        <div className='search-line'>
                                            <Form.Item name='shipSchedules'>
                                                <Schedule label='班期' />
                                            </Form.Item>
                                        </div>
                                    </>
                                    : null}
                                {isBigScreen ?
                                    <>
                                        <div className="search-line">
                                            <Form.Item name='lineId'>
                                                <LineOff label='航线' />
                                            </Form.Item>
                                        </div>
                                    </>
                                    : null}
                                <Button htmlType="submit" className="search-btn" loading={searchLoading}>查 询</Button>
                            </div>
                            <QueueAnim className="search-expand">
                                {isExpand ? [
                                    <Row gutter={[16, 0]} key="a">

                                        {!isScreen ?
                                            <>
                                                <Col xs={8} md={6} xl={4}>
                                                    <Form.Item label="班期" labelCol={{ flex: '80px' }} name='shipSchedules'>
                                                        <Select mode='multiple' allowClear maxTagCount={1}>
                                                            {shipSchedulesSelect.map((item) => {
                                                                return <Option key={item.key} value={item.key}>{item.value}</Option>
                                                            })}
                                                        </Select>
                                                    </Form.Item>
                                                </Col>
                                            </> : null}
                                        {!isBigScreen ?
                                            <>
                                                {/* 航线 */}
                                                <Col xs={8} md={6} xl={4}>
                                                    <Form.Item label="航线" labelCol={{ flex: '80px' }} name='lineId'>
                                                        <Line placeholder="请选择航线" />
                                                    </Form.Item>
                                                </Col>
                                            </> : null}

                                        <Col xs={8} md={6} xl={4}>
                                            <Form.Item label="从开航日" labelCol={{ flex: '80px' }} name='etdStart'>
                                                <DatePicker placeholder="请选择开航日期" style={{ width: '100%' }} />
                                            </Form.Item>
                                        </Col>
                                        <Col xs={8} md={6} xl={4}>
                                            <Form.Item label="到开航日" labelCol={{ flex: '80px' }} name='etdEnd'>
                                                <DatePicker placeholder="请选择开航日期" style={{ width: '100%' }} />
                                            </Form.Item>
                                        </Col>
                                        <Col xs={8} md={6} xl={4}>
                                            <Form.Item label="从生效日" labelCol={{ flex: '80px' }} name='effectiveDateStart'>
                                                <DatePicker placeholder="请选择生效日期" style={{ width: '100%' }} />
                                            </Form.Item>
                                        </Col>
                                        <Col xs={8} md={6} xl={4}>
                                            <Form.Item label="到生效日" labelCol={{ flex: '80px' }} name='effectiveDateEnd'>
                                                <DatePicker placeholder="请选择生效日期" style={{ width: '100%' }} />
                                            </Form.Item>
                                        </Col>
                                        <Col xs={8} md={6} xl={4}>
                                            <Form.Item label="从失效日" labelCol={{ flex: '80px' }} name='ineffectiveDateStart'>
                                                <DatePicker placeholder="请选择失效日期" style={{ width: '100%' }} />
                                            </Form.Item>
                                        </Col>
                                        <Col xs={8} md={6} xl={4}>
                                            <Form.Item label="到失效日" labelCol={{ flex: '80px' }} name='ineffectiveDateEnd'>
                                                <DatePicker placeholder="请选择失效日期" style={{ width: '100%' }} />
                                            </Form.Item>
                                        </Col>
                                        <Col xs={8} md={6} xl={4}>
                                            <Form.Item label='中转/直达' labelCol={{ flex: '80px' }} name='ttOrDts'>
                                                <Select options={[{ label: '中转', value: '中转' }, { label: '直达', value: '直达' }]}
                                                    mode='multiple' allowClear style={{ width: '100%' }}></Select>
                                            </Form.Item>
                                        </Col>
                                        <Col xs={8} md={6} xl={4}>
                                            <Form.Item label='有效/过期' labelCol={{ flex: '80px' }} name='states'>
                                                <Select options={[{ label: '有效', value: '有效' }, { label: '过期', value: '过期' }]}
                                                    mode='multiple' allowClear style={{ width: '100%' }}></Select>
                                            </Form.Item>
                                        </Col>
                                        <Col xs={8} md={6} xl={4}>
                                            <Form.Item label='航程' labelCol={{ flex: '80px' }} name='shipDayRange' >
                                                <ShipSchedule placeholder='请筛选航程' style={{ width: '100%' }} />
                                            </Form.Item>
                                        </Col>
                                    </Row>
                                ] : null}
                            </QueueAnim>

                            <div className='search-tool'>

                                <div className="rate-history">
                                    {freightHistorys.map((r) => <span key={r.id} onClick={() => {
                                        setPol({ enName: r.polEnName, cnName: r.polCnName, portEdi: r.pol });
                                        setPod({ enName: r.podEnName, cnName: r.podCnName, portEdi: r.pod });
                                        refPol.current?.setValue({ portName: r.polName });
                                        refPod.current?.setValue({ portName: r.podName });
                                        searchForm.setFieldsValue({ pol: r.pol, pod: r.pod });
                                    }}>
                                        {r.id}
                                    </span>
                                    )}
                                    <span style={{ display: freightHistorys.length > 0 ? '' : 'none' }} onClick={() => {
                                        localStorage.removeItem(freight_histor_key);
                                        setFreightHistorys([]);
                                    }}>清空记录</span>

                                    <span style={{ color: 'red' }} onClick={() => history.push('/me/business/searchHistory')}>
                                        查询历史
                                    </span>
                                </div>

                                <div className="tool-btn">
                                    <Button type="text" icon={<RedoOutlined />} size="small" htmlType="reset">
                                        重置
                                    </Button>
                                    {!isExpand ?
                                        <Button type="text" icon={<DownOutlined />} size="small" onClick={() => setIsExpand(true)}>展开</Button>
                                        :
                                        <Button type="text" icon={<UpOutlined />} size="small" onClick={() => setIsExpand(false)}>收起</Button>
                                    }
                                </div>
                            </div>
                        </Form>
                    </div>

                    <div className={listType == 'table' ? 'rate-list table-rate' : 'rate-list grid-rate'}>
                        <div className="rate-tools">
                            <div className="rate-filter">

                                {/* <Form layout="inline" form={filterForm} onFieldsChange={() => changeListData(originalDatas)}>
                                    <Form.Item name='beCabin'><Select placeholder="是否舱保" options={[{ label: '是', value: '是' }, { label: '否', value: '否' }]} allowClear style={{ width: '98px' }}></Select></Form.Item>

                                    <Form.Item name='rate'><Select placeholder="价格区间"></Select></Form.Item>
                                    <Form.Item name='boxType'><Box style={{ width: '180px' }} /></Form.Item>
                                </Form> */}

                                {
                                    current?.tenantId === '1' && <Space>
                                        <Button type="default" onClick={() => doExports()}>运价导出</Button>
                                        <Button type="primary" onClick={() => showModal("multi")} disabled={selectedRowKeys.length <= 1}>批量报价</Button>
                                    </Space>
                                }

                            </div>
                            <div className="rate-tools-btn">
                                <Space>
                                    <CompareBtn onClick={addNum} dataNum={selectedRows.length} />
                                    <Button type="text" icon={<BarsOutlined style={listType == 'table' ? { color: '#B4141B' } : {}} />} onClick={() => changeType('table')}></Button>
                                    <Button type="text" icon={<AppstoreOutlined style={listType == 'card' ? { color: '#B4141B' } : {}} />} onClick={() => changeType('card')}></Button>
                                </Space>
                            </div>
                        </div>

                        {/* 列表式 */}
                        {listType == 'table' ?
                            <>
                                <div ref={contentRef} className={`${isTouching ? 'rate-list-height' : ''}`}>
                                    {/* <div ref={contentRef} > */}
                                    <ConfigProvider renderEmpty={() => <NoData />} >
                                        <Grid gridName='offline-freight-table' title="线下运价" initColumns={rateColumns}
                                            pageNo={pageNo}
                                            pageSize={pageSize}
                                            request={(pageNo, pageSize) => searchOffline(pageNo, pageSize)}
                                            total={total}
                                            ref={gridRef}
                                            dataSource={freightPrices}
                                            optionsHide={{ "topTool": "none" }}
                                            onSelectedChanged={onSelectedChanged}
                                            height={'calc(100vh - 400px)'}
                                        />
                                    </ConfigProvider>
                                </div>
                            </>
                            :
                            <>
                                <div ref={contentRef} className='box'>
                                    <div className='card-header'>
                                        <div className='header-item' style={{ width: '360px', border: 0, textAlign: 'left' }}>
                                            <Checkbox onChange={(e) => { cardSelectedALl(e.target.checked) }}
                                                checked={selectedRowKeys.length > 0 && selectedRowKeys.length == freightPrices.length}
                                                indeterminate={selectedRowKeys.length > 0 && selectedRowKeys.length != freightPrices.length} />
                                            <span style={{ marginLeft: '16px' }}>基础信息</span>
                                        </div>
                                        <div className='header-item' style={{ width: '160px' }}>中转/直达</div>
                                        <div className='header-item' style={{ width: '120px' }}>20GP</div>
                                        <div className='header-item' style={{ width: '120px' }}>40GP</div>
                                        <div className='header-item' style={{ width: '120px' }}>40HC</div>
                                        <div className='header-item' style={{ width: '120px' }}>40NOR</div>
                                        <div className='header-item' style={{ width: '120px' }}>45HC</div>
                                        <div className='header-item' style={{ width: '20%', minWidth: '60px' }}>订舱备注</div>
                                        <div className='header-item' style={{ width: '160px' }}>操作</div>
                                    </div>

                                    {freightPrices.map((r) => <Badge.Ribbon text="底价" placement="start" style={r.beFloor ? { top: '-10px' } : { display: 'none' }}>
                                        <div className='card-list' key={r.id}>
                                            <div className='card-item-top'>
                                                <div className='card-item-checkbox'>
                                                    <Checkbox onChange={(e) => { cardSelected(e.target.checked, r) }} checked={r.checked} />
                                                </div>
                                                <div className='card-item-logo'>
                                                    <img src={shipmentIcon(r.shipCompany)} />
                                                </div>
                                                <div className='card-item-ports'>
                                                    <p>{r.shipCompany}</p>
                                                    <p style={{ color: '#999' }}>{r.polEnName} → {r.podEnName}</p>
                                                </div>
                                                <div className='card-item-transfer'>
                                                    <p>{r.ttOrDt + (r.ttOrDt === '中转' ? ' ' + r.ttPol : '')} </p>
                                                    <p style={{ color: '#999' }}>{dateFormat(r.etd, 'MM.dd') + '-' + dateFormat(r.eta, 'MM.dd') + ' (' + r.shipDays + '天)'}</p>
                                                </div>
                                                <div className='card-item-rate'>
                                                    {boxTypes.indexOf("20GP") > -1 && r.gp20Price ? '$' + r.gp20Price : '-'} {boxTypes.indexOf("20GP") > -1 && r.gp20Price ? rateTrendMap[r.gp20Trend] : ''}
                                                </div>
                                                <div className='card-item-rate'>
                                                    {boxTypes.indexOf("40GP") > -1 && r.gp40Price ? '$' + r.gp40Price : '-'} {boxTypes.indexOf("40GP") > -1 && r.gp40Price ? rateTrendMap[r.gp40Trend] : ''}
                                                </div>
                                                <div className='card-item-rate'>
                                                    {boxTypes.indexOf("40HC") > -1 && r.hc40Price ? '$' + r.hc40Price : '-'} {boxTypes.indexOf("40HC") > -1 && r.hc40Price ? rateTrendMap[r.hc40Trend] : ''}
                                                </div>
                                                <div className='card-item-rate'>
                                                    {boxTypes.indexOf("40NOR") > -1 && r.nor40Price ? '$' + r.nor40Price : '-'} {boxTypes.indexOf("40NOR") > -1 && r.nor40Price ? rateTrendMap[r.nor40Trend] : ''}
                                                </div>
                                                <div className='card-item-rate'>
                                                    {boxTypes.indexOf("45HC") > -1 && r.hc45Price ? '$' + r.hc45Price : '-'} {boxTypes.indexOf("45HC") > -1 && r.hc45Price ? rateTrendMap[r.hc45Trend] : ''}
                                                </div>
                                                <div className='card-item-note'>
                                                    {r.bookNote ? <Tooltip placement="bottomLeft" title={<p>{r.bookNote}<br />{r.weightLimitNote}</p>}><span>{r.bookNote} {r.weightLimitNote}</span></Tooltip> : ''}
                                                </div>
                                                <div className='card-item-option'>
                                                    <Space>
                                                        <Button size='small' type="text" danger onClick={() => showTrend(r)}>走势</Button>
                                                        {/* <Button size='small' type="primary" onClick={() => onBooking(r)}>下单</Button> */}
                                                        <Button size='small' type="primary" onClick={() => showModal(r)}>复制报价</Button>
                                                    </Space>
                                                </div>
                                            </div>
                                            <div className='card-item-bottom'>
                                                <p>
                                                    <span>航线：</span>{r.lineName}
                                                    <span>船期：</span>{dateFormat(r.etd)} {r.shipSchedule}
                                                    <span title={r.podContainerFreeDays}>箱使：{r.podContainerFreeDays?.slice(0, 10)}</span>
                                                    <span>有效期：</span>{r.effectiveDateStr} <span>更新人：</span>{r.updateUserCnName}  <span>更新时间：</span>{dateFormat(r.updateTime, 'yyyy-MM-dd hh:mm')}
                                                </p>
                                                {expandCards[r.id] ?
                                                    <>
                                                        <p><span>订舱备注：</span>{r.bookNote}</p>
                                                        <p><span title={r.weightLimitNote}>限重备注：{r.weightLimitNote?.slice(0, 20)}</span></p>
                                                        <Button type="text" icon={<UpOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>收起</Button>
                                                    </>
                                                    :
                                                    <Button type="text" icon={<DownOutlined />} size="small" className='card-expand-btn' onClick={() => toggleItem(r.id)}>展开</Button>}
                                            </div>
                                        </div>
                                    </Badge.Ribbon>)}

                                    {freightPrices.length == 0 && <NoData />}
                                </div>
                            </>
                        }
                    </div>

                    <Quote
                        open={isModalOpen}
                        onCancel={() => setIsModalOpen(false)}
                        data={rateData}
                        rateType='线下'
                    />


                    <Booking
                        current={rateData}
                        open={isBookingOpen}
                        onCancel={() => setIsBookingOpen(false)}
                        onSubmit={() => {
                            setIsBookingOpen(false);
                        }}
                    />

                </div>
            </Spin>
            <RateTrend
                open={trendOpen}
                current={rateData}
                onCancel={() => setTrendOpen(false)}
            />
        </>

    );
}

