import React from 'react';
import { Button } from 'antd';
import { MessageOutlined } from '@ant-design/icons';
import './index.less';

const ChatButton = () => {
  const openChat = () => {
    if (window.chatwootSDK) {
      // 打开聊天窗口
      window.chatwootSDK.toggle();
    } else {
      console.error('Chatwoot SDK not loaded');
    }
  };

  return (
    <div className="chat-button-container">
      <Button 
        type="primary" 
        shape="circle" 
        size="large"
        icon={<MessageOutlined />}
        onClick={openChat}
        className="chat-button"
        title="在线客服"
      />
    </div>
  );
};

export default ChatButton;
