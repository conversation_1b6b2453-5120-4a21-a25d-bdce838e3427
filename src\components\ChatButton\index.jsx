import React from 'react';
import { Button } from 'antd';
import { MessageOutlined } from '@ant-design/icons';
import './index.less';

const ChatButton = () => {
  const openChat = () => {
    console.log('Chat button clicked');

    // 等待一下确保SDK加载完成
    setTimeout(() => {
      if (window.chatwootSDK) {
        console.log('Chatwoot SDK found:', window.chatwootSDK);
        console.log('Available methods:', Object.keys(window.chatwootSDK));

        // 尝试不同的API方法来打开聊天窗口
        try {
          // 方法1: 尝试 toggle
          if (typeof window.chatwootSDK.toggle === 'function') {
            console.log('Using toggle method');
            window.chatwootSDK.toggle();
          }
          // 方法2: 尝试 toggleBubbleVisibility
          else if (typeof window.chatwootSDK.toggleBubbleVisibility === 'function') {
            console.log('Using toggleBubbleVisibility method');
            window.chatwootSDK.toggleBubbleVisibility();
          }
          // 方法3: 尝试 open
          else if (typeof window.chatwootSDK.open === 'function') {
            console.log('Using open method');
            window.chatwootSDK.open();
          }
          // 方法4: 查找并点击Chatwoot元素
          else {
            console.log('Trying to find Chatwoot elements...');
            const selectors = [
              '[data-widget="chatwoot"]',
              '.woot-widget-bubble',
              '#chatwoot_live_chat_widget',
              '.woot--bubble',
              '[id*="chatwoot"]',
              '[class*="chatwoot"]',
              '[class*="woot"]'
            ];

            let chatElement = null;
            for (const selector of selectors) {
              chatElement = document.querySelector(selector);
              if (chatElement) {
                console.log('Found chat element with selector:', selector);
                break;
              }
            }

            if (chatElement) {
              chatElement.click();
            } else {
              console.log('No chat elements found. Available methods:', Object.keys(window.chatwootSDK));
              // 尝试直接调用SDK的方法
              if (window.chatwootSDK.run) {
                console.log('Trying to re-run SDK...');
                // 可能需要重新初始化
              }
              alert('聊天功能暂时不可用，请稍后再试或刷新页面');
            }
          }
        } catch (error) {
          console.error('Error opening chat:', error);
          alert('打开聊天窗口时出错：' + error.message);
        }
      } else {
        console.error('Chatwoot SDK not loaded');
        alert('聊天功能正在加载中，请稍后再试或刷新页面');
      }
    }, 500);
  };

  return (
    <div className="chat-button-container">
      <Button 
        type="primary" 
        shape="circle" 
        size="large"
        icon={<MessageOutlined />}
        onClick={openChat}
        className="chat-button"
        title="在线客服"
      />
    </div>
  );
};

export default ChatButton;
