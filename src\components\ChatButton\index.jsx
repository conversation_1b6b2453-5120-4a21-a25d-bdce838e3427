import React from 'react';
import { Button } from 'antd';
import { MessageOutlined } from '@ant-design/icons';
import './index.less';

const ChatButton = () => {
  // 调试函数：检查页面上所有可能的Chatwoot元素
  const debugChatwootElements = () => {
    console.log('=== Chatwoot Debug Info ===');

    // 检查所有可能的Chatwoot相关元素
    const allElements = document.querySelectorAll('*');
    const chatwootElements = [];

    allElements.forEach(el => {
      if (el.id && (el.id.includes('chatwoot') || el.id.includes('woot'))) {
        chatwootElements.push({type: 'id', selector: `#${el.id}`, element: el});
      }
      if (el.className && (el.className.includes('chatwoot') || el.className.includes('woot'))) {
        chatwootElements.push({type: 'class', selector: `.${el.className}`, element: el});
      }
    });

    console.log('Found Chatwoot elements:', chatwootElements);

    // 检查iframe
    const iframes = document.querySelectorAll('iframe');
    console.log('All iframes:', iframes);

    return chatwootElements;
  };

  const openChat = () => {
    console.log('Chat button clicked');

    // 先运行调试
    const chatwootElements = debugChatwootElements();

    // 等待一下确保SDK加载完成
    setTimeout(() => {
      if (window.chatwootSDK) {
        console.log('Chatwoot SDK found:', window.chatwootSDK);
        console.log('Available methods:', Object.keys(window.chatwootSDK));

        // 尝试不同的API方法来打开聊天窗口
        try {
          // 方法1: 尝试 toggle
          if (typeof window.chatwootSDK.toggle === 'function') {
            console.log('Using toggle method');
            window.chatwootSDK.toggle();
          }
          // 方法2: 尝试 toggleBubbleVisibility
          else if (typeof window.chatwootSDK.toggleBubbleVisibility === 'function') {
            console.log('Using toggleBubbleVisibility method');
            window.chatwootSDK.toggleBubbleVisibility();
          }
          // 方法3: 尝试 open
          else if (typeof window.chatwootSDK.open === 'function') {
            console.log('Using open method');
            window.chatwootSDK.open();
          }
          // 方法4: 查找并点击Chatwoot元素
          else {
            console.log('Trying to find Chatwoot elements...');
            const selectors = [
              '[data-widget="chatwoot"]',
              '.woot-widget-bubble',
              '#chatwoot_live_chat_widget',
              '.woot--bubble',
              '[id*="chatwoot"]',
              '[class*="chatwoot"]',
              '[class*="woot"]'
            ];

            let chatElement = null;
            for (const selector of selectors) {
              chatElement = document.querySelector(selector);
              if (chatElement) {
                console.log('Found chat element with selector:', selector);
                break;
              }
            }

            if (chatElement) {
              console.log('Chat element found:', chatElement);
              console.log('Element style:', window.getComputedStyle(chatElement));
              console.log('Element innerHTML:', chatElement.innerHTML);

              // 尝试多种点击方式
              try {
                // 方式1: 普通点击
                chatElement.click();
                console.log('Clicked chat element');

                // 方式2: 如果普通点击不行，尝试触发事件
                setTimeout(() => {
                  const clickEvent = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                  });
                  chatElement.dispatchEvent(clickEvent);
                  console.log('Dispatched click event');
                }, 100);

                // 方式3: 查找内部的可点击元素
                const clickableChild = chatElement.querySelector('button, [role="button"], .clickable, [onclick]');
                if (clickableChild) {
                  console.log('Found clickable child:', clickableChild);
                  clickableChild.click();
                }

                // 方式4: 尝试改变元素的可见性或状态
                if (chatElement.style.display === 'none') {
                  chatElement.style.display = 'block';
                  console.log('Made chat element visible');
                }

              } catch (error) {
                console.error('Error clicking chat element:', error);
              }
            } else {
              console.log('No chat elements found. Available methods:', Object.keys(window.chatwootSDK));
              alert('聊天功能暂时不可用，请稍后再试或刷新页面');
            }
          }
        } catch (error) {
          console.error('Error opening chat:', error);
          alert('打开聊天窗口时出错：' + error.message);
        }
      } else {
        console.error('Chatwoot SDK not loaded');
        alert('聊天功能正在加载中，请稍后再试或刷新页面');
      }
    }, 500);
  };

  return (
    <div className="chat-button-container">
      <Button 
        type="primary" 
        shape="circle" 
        size="large"
        icon={<MessageOutlined />}
        onClick={openChat}
        className="chat-button"
        title="在线客服"
      />
    </div>
  );
};

export default ChatButton;
