import React, { useState, useEffect } from 'react';
import avatar from '@/assets/header.png';
import './index.less';
import { api, constant } from '@/common/utils';


export default (props) => {


    const [currentTenant, setCurrentTenant] = useState({});


    useEffect(() => {
        getCurrentTenant();
    }, [props.flag]);

    const getCurrentTenant = () => {
        const current = JSON.parse(sessionStorage.getItem(constant.KEY_CURRENT_USER));
        if (!current || !current.tenantId) {
            return
        }
        api.me.getTenant(current.tenantId).subscribe({
            next: (data) => {
                console.info(data)
                setCurrentTenant(data);
            }
        });
    };

    return (
        <div className='basic-header'>
            <div className='basic-avatar'>
                <img src={currentTenant?.logoUrl ? constant.FILE_URL + currentTenant?.logoUrl : avatar} />
            </div>
            <div className='basic-info'>
                <p className='manager'>{currentTenant.name}</p>
                {/* <p className='company'>青岛海尔智家科技有限公司</p> */}
            </div>
            <div className='basic-status'>
                {currentTenant.state === 'APPROVE' ?
                    <div className="verified"> 认证企业 </div>
                    :
                    <div className="no-verified"> 未 认 证 </div>
                }
            </div>
        </div>
    )
}