import React, { useState } from 'react';
import { Form, Row, Col, message, Button, Space, DatePicker, Input, Table, Pagination, Tag, Modal } from 'antd';
import './index.less';
import Menu from '../components/Menu';
import { api, formatMoney } from '@/common/utils';
import * as R from 'ramda';
import { DownOutlined, RightOutlined, } from '@ant-design/icons';


export default () => {
  const [searchForm] = Form.useForm();
  const [datas, setDatas] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [showView, setShowView] = useState(false);

  const tags = {
    'UNCOMMITED': { color: 'default', text: '未申请' },
    'APPROVED': { color: 'processing', text: '已申请' },
    'INVOICED': { color: 'success', text: '已开票' },
    'REJECTED': { color: 'warning', text: '已驳回' },
  };
  const columns = [
    { title: '状态', dataIndex: 'state', width: 75, render: (text, record) => <> <Tag bordered={false} color={tags[text]?.color}>{tags[text]?.text}</Tag></> },
    { title: '业务编号', dataIndex: 'code', width: 120},
    { title: '发票抬头', dataIndex: 'toTitle', width: 143, ellipsis: true, },
    { title: '应付RMB', dataIndex: 'totalRmb', width: 111, render: (text, record) => <>{formatMoney(text)}</> },
    { title: '应付USD', dataIndex: 'totalUsd', width: 111, render: (text, record) => <>{formatMoney(text)}</> },
    { title: '开票币别', dataIndex: 'invoiceCurrency', width: 100 },
    { title: '开票金额', dataIndex: 'invoiceMoney', width: 132, render: (text, record) => <>{formatMoney(text)}</> },
    {
      title: '操作', dataIndex: 'option', width: 100, align: 'center', render: (text, record) =>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', color: '#B4141B', cursor: 'pointer' }}>
          <div onClick={() => preview(record)}>预览</div>
          <div onClick={() => download(record.id)}>下载</div>
        </div>
    },
  ]

  const onSelectChange = (selectedKeys, selectedRows) => {
    setSelectedRowKeys(selectedKeys);
  };


  const onSearch = (pageNo, pageSize) => {
    setPageNo(pageNo);
    setPageSize(pageSize);
    setSearchLoading(true);
    let searchData = searchForm.getFieldValue();
    let param = { dto: searchData, pageNo: pageNo, pageSize: pageSize };
    api.invoiceApply.searchInvoice(param).subscribe({
      next: (data) => {
        setTotal(data.total);
        setDatas(data.data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };

  const onShowSizeChange = (current, pageSize) => {
    onSearch(current, pageSize);
  };


  const downloads = () => {
    if (selectedRowKeys.length === 0) {
      message.error("请至少选择一条数据!");
      return;
    }

  };

  const download = (id) => {
    console.log(id);
  };

  const preview = (id) => {
    setShowView(true);

  };


  return (
    <>
      <Menu selectKeys={["invoiceApply"]} openKeys={["finance"]} >

        <div className="invoice-search">
          <Form form={searchForm}>
            <Row wrap={false} gutter={20}>
              <Col flex={'auto'}>
                <Row>
                  <Col span={8}>
                    <Form.Item name="code" label="业务编号" labelCol={{ flex: '90px' }}>
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="blNo" label="主提单号" labelCol={{ flex: '90px' }}>
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="invoiceNo" label="发票号" labelCol={{ flex: '90px' }}>
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="applyDateStart" label="从申请日" labelCol={{ flex: '90px' }}>
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="applyDateEnd" label="到申请日" labelCol={{ flex: '90px' }}>
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col flex={'100px'}>
                <Space size={[10, 24]} wrap>
                  <Button type="primary" htmlType="submit" onClick={() => onSearch()} loading={searchLoading}> 查 询 </Button>
                  <Button type="default" htmlType="reset"> 重 置 </Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </div>
        <div className="invoice-tools">
          <Space>
            <Button type='primary' onClick={() => downloads()}>批量下载</Button>
            {/* <Button type='primary' onClick={() => downloads()}>申请开票</Button> */}
          </Space>

          <Table
            className='invoice-table'
            rowKey='id'
            pagination={false}
            style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
            rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
            columns={columns}
            dataSource={datas}
            expandable={{
              expandedRowRender: (record) => (
                <div className='bill-expand'>
                  <p style={{ marginLeft: '130px' }}><span>备注：</span>{record.note} </p>
                </div>
              ),
              expandIcon: ({ expanded, onExpand, record }) =>
                expanded ?
                  (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                  :
                  (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
            }}
          />
        </div>
        <div className='invoiceApply-pagination'>
          <Pagination
            pageSizeOptions={[10, 20, 50]}
            showSizeChanger
            onShowSizeChange={onShowSizeChange}
            onChange={onShowSizeChange}
            // defaultCurrent={p}
            total={total || 0}
            current={pageNo || 0}
            pageSize={pageSize || 10}
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`}
          />

        </div>

      </Menu>

      <Modal
        title="发票预览"
        open={showView}
        onOk={() => setShowView(false)}
        onCancel={() => setShowView(false)}
        width={1000}
        footer={null}
      >
        {/* <div style={{margin:'24px 0'}}>本发票已被打印 <span style={{color:'red'}}>1</span> 次，已被浏览 <span style={{color:'red'}}>4</span> 次</div> */}
        <iframe src="http://www.jzbz.com/group1/M00/02/67/wKgB9GdELcaELsUuAAAAANqUX30017.pdf" width="100%" height="600" style={{border:'0'}}></iframe>
        <div style={{margin:'24px 0',textAlign:'right'}}>
          <Space>
            <Button type='primary'>PDF下载</Button>
            <Button type='primary'>XML下载</Button>
            <Button type='primary'>OFD下载</Button>
          </Space>
        </div>
      </Modal>
    </>
  );
};
