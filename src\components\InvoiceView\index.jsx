import React, { useState } from 'react';
import { Button, Space, Modal } from 'antd';
import { api, constant } from '@/common/utils';



export default (props) => {
    const { visible, record, onClose } = props;
    const [loading, setLoading] = useState(false);

    //下载发票
    const download = (record) => {
        let fileName =
            record.invoiceNo + record.invoiceUrl?.substring(record.invoiceUrl?.lastIndexOf('.'));
        const params = {
            fileUrl: record.invoiceUrl,
            fileName: fileName
        };
        setLoading(true);
        api.base.download(params).subscribe({
            next: (res) => {
                console.log(res)
                const blob = new Blob([res], {
                    type: 'application/octet-stream',
                });
                if (window.navigator.msSaveOrOpenBlob) {
                    navigator.msSaveBlob(blob, fileName);
                } else {
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    // 释放内存
                    window.URL.revokeObjectURL(link.href);
                }
            }
        }).add(() => {
            setLoading(false);
        });
    };

    return (
        <>
            <Modal
                title="发票预览"
                open={visible}
                onOk={() => onClose()}
                onCancel={() => onClose()}
                width={1000}
                footer={null}
                loading={loading}
            >
                {/* <div style={{margin:'24px 0'}}>本发票已被打印 <span style={{color:'red'}}>1</span> 次，已被浏览 <span style={{color:'red'}}>4</span> 次</div> */}
                <iframe src={record?.invoiceUrl?.indexOf('group') > -1 ? constant.FDFS_PREFIX + record?.invoiceUrl : record?.invoiceUrl} width="100%" height="600" style={{ border: '0' }}></iframe>
                <div style={{ margin: '24px 0', textAlign: 'right' }}>
                    <Space>
                        <Button type='primary' disabled={!record?.pdfDownUrl} href={record?.pdfDownUrl}>PDF下载</Button>
                        <Button type='primary' disabled={!record?.xmlUrl} href={record?.xmlUrl}>XML下载</Button>
                        <Button type='primary' disabled={!record?.ofdUrl} href={record?.ofdUrl}>OFD下载</Button>
                    </Space>
                </div>
            </Modal>
        </>
    );
}
