import React, { useState, useEffect } from 'react';
import { Row, Col, Modal, Steps, Tabs, Timeline, Button, Drawer, Checkbox, Divider, Space, Form, Select, message, Alert, Badge } from 'antd';
import * as R from 'ramda';
const { Step } = Steps;
import moment from 'moment';
import { api, copyObject, dateFormat, constant } from '@/common/utils';
import './index.less';
import { ExclamationCircleFilled } from '@ant-design/icons';
import BadgeImg from './badgeImg';


export default (props) => {

    const [form] = Form.useForm();
    const { open, current, onCancel } = props;
    // 订阅异常
    const [errorOpen, setErrorOpen] = useState(false);
    // 选择结点
    const plainOptions = ['未提箱预警', '未还箱预警', '未进场提醒', '海放异常', '码放异常', 'ETA变更提醒', '开截港变动', '船名航次变动', '甩柜', '船公司操作', '配载异常', '码头变更', '无箱动态', '中转滞港']
    const [checkedList, setCheckedList] = useState([]);
    const checkAll = plainOptions.length === checkedList.length;
    const indeterminate = checkedList.length > 0 && checkedList.length < plainOptions.length;
    const [containers, setContainers] = useState([]);
    const [containerStates, setContainerStates] = useState([]);
    const [emails, setEmails] = useState([]);
    const [emailError, setEmailError] = useState(false);
    const [loading, setLoading] = useState(false);
    const [container, setContainer] = useState({});
    const [activeKey, setActiveKey] = useState("");


    // 十个固定节点
    const steps = [
        {
            title: '提空',
            icon0: <BadgeImg url={require('@/assets/track/_tk.png')} beWarn={current.warnName?.indexOf('未提箱预警') > -1} />,
            icon1: <BadgeImg url={require('@/assets/track/tk.png')} beWarn={current.warnName?.indexOf('未提箱预警') > -1} />,
            description: current.tkCount + '/' + current.containerTotal,
        },
        {
            title: '重返',
            icon0: <BadgeImg url={require('@/assets/track/_zf.png')} beWarn={current.warnName?.indexOf('未进场提醒') > -1} />,
            icon1: <BadgeImg url={require('@/assets/track/zf.png')} beWarn={current.warnName?.indexOf('未进场提醒') > -1} />,
            description: current.zfCount + '/' + current.containerTotal,
        },
        {
            title: '海放',
            icon0: <BadgeImg url={require('@/assets/track/_hf.png')} beWarn={current.warnName?.indexOf('海放异常') > -1} />,
            icon1: <BadgeImg url={require('@/assets/track/hf.png')} beWarn={current.warnName?.indexOf('海放异常') > -1} />,
            description: current.hfCount + '/' + current.containerTotal,
        },
        {
            title: '码放',
            icon0: <BadgeImg url={require('@/assets/track/_mf.png')} beWarn={current.warnName?.indexOf('码放异常') > -1} />,
            icon1: <BadgeImg url={require('@/assets/track/mf.png')} beWarn={current.warnName?.indexOf('码放异常') > -1} />,
            description: current.mfCount + '/' + current.containerTotal,
        },
        {
            title: '装船',
            icon0: <BadgeImg url={require('@/assets/track/_zc.png')} beWarn={current.warnName?.indexOf('甩柜') > -1} />,
            icon1: <BadgeImg url={require('@/assets/track/zc.png')} beWarn={current.warnName?.indexOf('甩柜') > -1} />,
            description: current.zcCount + '/' + current.containerTotal,
        },
        {
            title: '开航',
            icon0: <BadgeImg url={require('@/assets/track/_kh.png')} />,
            icon1: <BadgeImg url={require('@/assets/track/kh.png')} />,
            description: current.etd && current.etd.length > 10 ? current.etd.substring(0, 10) : current.etd
        },
        {
            title: '到港',
            icon0: <BadgeImg url={require('@/assets/track/_dg.png')} />,
            icon1: <BadgeImg url={require('@/assets/track/dg.png')} />,
            description: current.ata && current.ata.length > 10 ? current.ata.substring(0, 10) : current.ata
        },
        {
            title: '卸载',
            icon0: <BadgeImg url={require('@/assets/track/_xz.png')} />,
            icon1: <BadgeImg url={require('@/assets/track/xz.png')} />,
            description: current.xzCount + '/' + current.containerTotal,
        },
        {
            title: '提重',
            icon0: <BadgeImg url={require('@/assets/track/_tz.png')} />,
            icon1: <BadgeImg url={require('@/assets/track/tz.png')} />,
            description: current.tzCount + '/' + current.containerTotal,
        },
        {
            title: '还空',
            icon0: <BadgeImg url={require('@/assets/track/_hk.png')} beWarn={current.warnName?.indexOf('未还箱预警') > -1} />,
            icon1: <BadgeImg url={require('@/assets/track/hk.png')} beWarn={current.warnName?.indexOf('未还箱预警') > -1} />,
            description: current.hkCount + '/' + current.containerTotal,
        },
    ];

    useEffect(() => {
        form.resetFields();
        setActiveKey('');
        setContainer({});
        setContainerStates([]);
        setContainers([]);
        setCheckedList([]);
        if (open) {
            //集装箱
            if (current.containers && current.containers.length > 0) {
                let temp = [];
                R.forEach((v) => {
                    temp.push({
                        key: v.id,
                        label: v.containerNo,
                        containerStates: v.containerStates,
                        beTt: v.beTt,
                        tipMessage: v.tipMessage,
                        eventName: v.eventName,
                    });
                }, current.containers);
                setContainers(temp);
                changeContainer(temp[0].key, temp);
            }
            //订阅
            // let emailChecked = current.emailType ? (current.emailType instanceof Array ? current.emailType : current.emailType.split(',')) : [];
            // setCheckedList(emailChecked);
        }
    }, [current?.id, open]);

    const changeContainer = (e, containers) => {
        setActiveKey(e);
        let temp = [];
        R.forEach((v) => {
            if (v.key === e) {
                if (v.containerStates && v.containerStates.length > 0) {
                    R.forEach((s) => {
                        temp.push({
                            color: !s.stateTime || moment(s.stateTime) > moment() ? 'gray' : '#B4141B',
                            children: <Row className='order-track-ctnRow'>
                                <Col flex="200px"> {s.state} </Col>
                                <Col flex="210px"> {s.stateTime && s.stateTime.length > 16 ? s.stateTime.substring(0.16) : s.stateTime} </Col>
                                <Col flex="300px"> {s.statePlaceName} </Col>
                                <Col flex="auto"> {s.vessel ? (s.vessel + '/' + s.voyage) : '-'} </Col>
                            </Row>
                        });
                    }, v.containerStates);
                }
                setContainer(v);
                return;
            }
        }, containers);
        setContainerStates(temp);
    }

    const onChange = (list) => {
        setCheckedList(list);
    };
    const onCheckAllChange = (e) => {
        let checkedDatas = e.target.checked ? plainOptions : [];
        form.setFieldsValue({ items: checkedDatas });
        setCheckedList(checkedDatas);
    };

    const onFinish = (v) => {
        if (emailError) {
            message.error("请输入正确格式邮箱!");
            return;
        }
        let data = form.getFieldValue();
        if (!data.mailTo || data.mailTo.length <= 0) {
            message.error('请填写通知邮箱!');
            return;
        }
        if (!data.items || data.items.length === 0) {
            message.error('请选择预警方案!');
            return;
        }
        let param = { emailType: data.items.join(','), emailTo: data.mailTo.join(','), id: current.id };
        setLoading(true);
        api.order.bookException(param).subscribe({
            next: (data) => {
                message.success('订阅成功!');
                setErrorOpen(false);
            },
        }).add(() => {
            setLoading(false);
        });
    };

    const checkSendTo = (e) => {
        setEmailError(false);
        var re = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]|(\w|\-)+\.([a-zA-Z]{2,4})$/;
        R.forEach((v) => {
            if (!re.test(v)) {
                setEmailError(true);
                message.error("请输入正确格式邮箱!");
                return;
            }
        }, e)
        setEmails(e);
    };
    const bookException = (e) => {
        api.order.getTrackBookInfo(current.orderId).subscribe({
            next: (data) => {
                //订阅
                let checkedList = data.emailType ? (data.emailType instanceof Array ? data.emailType : data.emailType.split(',')) : [];
                setCheckedList(checkedList);
                form.setFieldsValue({ items: checkedList, mailTo: data.emailTo ? data.emailTo.split(",") : [] });
                setErrorOpen(true);
            },
        })

    }

    return <Modal
        title="运踪详情"
        centered
        open={open}
        width={1200}
        footer={null}
        onCancel={() => onCancel()}
        styles={{
            header: { borderBottom: '1px solid #f0f0f0' },
            body: {
                padding: '0',
                height: 'calc(100vh - 200px)',
                overflow: 'hidden'
            }
        }}
    >
        <div className='order-track-title'>
            <div className='order-track-title-info'>
                <Row>
                    <Col span={8}>提单号：{current.blNo}</Col>
                    <Col span={8}>船司：{current.shipCompany}</Col>
                    <Col span={8}>船名航次：{current.vessel} / {current.voyage}</Col>
                </Row>
                <Row >
                    <Col span={8}>装货港：{current.pol}</Col>
                    <Col span={8}>卸货港：{current.pod}</Col>
                </Row>
            </div>
            <div>
                <Button onClick={() => { bookException() }} danger type='primary'>异常订阅</Button>
            </div>
        </div>


        <Row>
            <Col span={24} style={{ padding: '8px 0 10px 0' }}>
                <Steps labelPlacement="vertical" size='small' current={current.stateCurrent} className='tarck-order-steps'>
                    {steps.map((item, i) => (
                        <Step
                            key={i}
                            flex={1}
                            icon={i < current.stateCurrent ? item.icon0 : item.icon1}
                            title={(<span style={{ fontSize: '16px', color: '333333' }}>{item.title}</span>)}
                            description={(<span style={{ fontSize: '12px', color: '#666666' }}>{item.description}</span>)}
                        />
                    ))}
                </Steps>
            </Col>
        </Row>
        <Tabs
            size='small'
            type="card"
            items={containers}
            activeKey={activeKey}
            onChange={(e) => changeContainer(e, containers)}
        />

        <div className='order-track-ctnDetail'>
            <Alert type="error" icon={<ExclamationCircleFilled />} message={<>
                <span style={{ color: '#FF4D4F' }}> {container.eventName}</span>
                <span> {container.tipMessage}</span>
            </>} banner className='order-track-alert' style={{ display: container.eventName ? '' : 'none' }} />
            <Row className='order-track-ctnRowHeader'>
                <Col flex="200px"> 动态 </Col>
                <Col flex="210px"> 时间</Col>
                <Col flex="300px"> 地点 </Col>
                <Col flex="auto"> 船名 / 航次 </Col>
            </Row>
            <Timeline items={containerStates} />
        </div>

        <Drawer
            title=""
            width="450"
            closeIcon={false}
            onClose={() => setErrorOpen(false)}
            open={errorOpen}
        >
            <Form
                onFinish={onFinish}
                form={form}
            >
                <Row>
                    <Col>
                        <h3>预警方案</h3>
                    </Col>
                </Row>
                <Row style={{ margin: '10px 0' }} >
                    <Col span={24}>
                        <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                            全选
                        </Checkbox>
                    </Col>
                </Row>
                <Divider style={{ margin: '10px 0' }} />
                <Form.Item label="" name="items">
                    <Checkbox.Group onChange={onChange} options={plainOptions} >
                        {/* <Row gutter={[20, 10]}> */}
                        {/* {plainOptions.map((item) => <Col span={12}><Checkbox key={item} checked={true}>{item}</Checkbox></Col>)} */}
                        {/* </Row> */}
                    </Checkbox.Group>
                </Form.Item>

                <Row>
                    <Col span={24}>
                        <Form.Item label="通知邮箱" name="mailTo">
                            <Select mode="tags" open={false} placeholder="请输入接收消息的邮箱" onChange={(e) => checkSendTo(e)} defaultValue={emails}></Select>
                        </Form.Item>
                    </Col>
                </Row>
                <Divider style={{ margin: '10px 0 20px 0' }} />
                <Row>
                    <Col span={24} style={{ textAlign: 'center' }}>
                        <Space>
                            <Button onClick={() => setErrorOpen(false)}>取消</Button>
                            <Button type="primary" htmlType="submit" loading={loading}> 保存 </Button>
                        </Space>
                    </Col>
                </Row>
            </Form>
        </Drawer >
    </Modal >
}