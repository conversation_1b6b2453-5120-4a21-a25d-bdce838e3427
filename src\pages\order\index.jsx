import React, { useState } from 'react';
import { Button, Row, Col, Form, Input, Space, Divider, Table, Modal, Checkbox, Pagination, Steps, message } from 'antd';
import { RedoOutlined, DownOutlined, UpOutlined, RightOutlined, AppstoreOutlined, BarsOutlined, } from '@ant-design/icons';
import Texty from 'rc-texty';
import QueueAnim from 'rc-queue-anim';
import AnimDate from '@/components/AnimDate';
import AnimPorts from '@/components/AnimPorts';
import AnimInput from '@/components/AnimInput';
import Port from '@/components/Port';
import ShipCompany from '@/components/ShipCompany';
import './index.less';
import { api, copyObject, dateFormat, constant } from '@/common/utils';
import { shipmentIcon } from '@/common/common';
import Detail from './detail';
import Track from './track';
import Position from '@/components/Position';
import NoData from '@/components/NoData';

export default () => {

  const [listType, setListType] = useState('card');
  const [isExpand, setIsExpand] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchForm] = Form.useForm();
  const [orders, setOrders] = useState([]);
  const [trackInfo, setTrackInfo] = useState({});
  const [showTrace, setShowTrace] = useState(false);
  const [showVessel, setShowVessel] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [vessel, setVessel] = useState();
  const [orderId, setOrderId] = useState('');

  // 十个固定节点
  const steps = [
    { title: '提空', },
    { title: '重返', },
    { title: '海放', },
    { title: '码放', },
    { title: '装船', },
    { title: '开航', },
    { title: '到港', },
    { title: '卸载', },
    { title: '提重', },
    { title: '还空', },
  ];


  const orderColumns = [
    { title: '提单号', dataIndex: 'blNo', width: 170, ellipsis: true },
    { title: '委托单位', dataIndex: 'entrustCustomer', width: 100, ellipsis: true },
    { title: '船名航次', dataIndex: 'vessel', width: 160, ellipsis: true, render: (text, record) => <span>{(record.vessel || '') + ' / ' + (record.voyage || '')}</span> },
    { title: '箱型箱量', dataIndex: 'boxInfo', align: 'center', width: 100 },
    { title: '件数包装', dataIndex: 'cargoPkgs', align: 'center', width: 120, ellipsis: true, render: (text, record) => <div ><span>{(text || '') + ' ' + (record.cargoUnits || '')}</span></div> },
    { title: '重量(KGS)', dataIndex: 'cargoWeight', align: 'center', width: 100 },
    { title: '尺寸(CBM)', dataIndex: 'cargoSize', align: 'center', width: 100 },
    {
      title: '操作栏', dataIndex: 'option', align: 'center', render: (text, record) =>
        <>
          <Button size='small' type='text' danger onClick={() => openDetail(record.id)}>业务信息</Button>
          <Button size='small' type='text' danger onClick={() => openTrace(record.id)}>运踪状态</Button>
          <Button size='small' type='text' danger onClick={() => openVessel(record.vessel)}>船舶轨迹</Button>
        </>
    },
  ]

  // 临时数据
  const getRandomInt = (max) => {
    return Math.floor(Math.random() * Math.floor(max));
  }
  const tempOrders = [];
  const carriers = ['MSK', 'CMA', 'MSC', 'ZIM', 'ONE', 'EMC', 'WAL']
  for (let i = 0; i < 20; i++) {
    tempOrders.push({
      id: i,
      shipCompany: carriers[getRandomInt(7)],
      line: "CUA32",
      week: "周三",
      ttOrDt: '中转',
      ttPol: "NINGBO",
      price: "1230.00",
      bookCode: 'BO241000001',
      boxInfo: '20GP*2',
      cargoPkgs: '220',
      cargoUnits: 'PACKAGES',
      cargoWeight: '13430.243',
      cargoSize: '60.233',
      state: getRandomInt(3),
      pol: "QINGDAO,CN 青岛｜中国",
      pod: "LOS ANGELES,USA 洛杉矶｜美国",
      vessel: 'CMS DANDLE',
      voyage: 'W0382',
      blNo: '177MCACAQ4265VFB',
      entrustCustomer: '深圳源信运通',
      stateCurrent: getRandomInt(10),
      lineCode: 'AXU3',
      etd: '2024-10-10',
      cutTime: '2024-10-10 12:00'
    });
  }

  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const onSearch = (pageNo, pageSize) => {
    setPageNo(pageNo);
    setPageSize(pageSize);
    setSearchLoading(true);
    let searchData = searchForm.getFieldValue();
    if (searchData.atdRange) {
      copyObject(searchData, { atdStart: searchData.atdRange[0], atdEnd: searchData.atdRange[1] });
    } else {
      copyObject(searchData, { atdStart: null, atdEnd: null });
    }
    let param = { dto: searchData, pageNo: pageNo, pageSize: pageSize };
    api.order.searchOrder(param).subscribe({
      next: (data) => {
        setTotal(data.total);
        setOrders(data.data);
      }
    }).add(() => {
      setSearchLoading(false);
    });
  };

  const onShowSizeChange = (current, pageSize) => {
    onSearch(current, pageSize);
  };

  // 打开运踪详情
  const openTrace = (id) => {
    api.order.getOrderTrack(id).subscribe({
      next: (data) => {
        if (!data.pol || !data.pod) {
          message.warning("目前尚未获取到运踪信息,请稍后再试!");
          return;
        }
        copyObject(data, { containerTotal: data.containers ? data.containers?.length : 0 });
        setTrackInfo(data);
        setShowTrace(true);
      }
    }).add(() => {

    });
  };

  // 打开船舶轨迹
  const openVessel = (value) => {
    if (!value) {
      message.warning("未获取到船名信息~");
      return;
    }
    setVessel(value);
    setShowVessel(true);
  };
  // 打开提单详情
  const openDetail = (id) => {
    setOrderId(id);
    setShowDetail(true);
  };


  return (
    <>
      <div className="za-content order">
        <div className="order-content">
          <div className="order-slogan">
            <div className="slogan-title">
              <Texty delay={1000}>台账管理</Texty>
            </div>
            <div className="slogan-sub-title">
              <Texty delay={2000} type="flash">实时关注提单状态、船舶轨迹，货物异常及时提醒。</Texty>
            </div>
          </div>

          <div className="order-search">
            <Form
              form={searchForm}
              name="searchRef"
              onFinish={() => onSearch(1, pageSize)}
            >
              <div className='search-default'>
                <div className="search-ports">
                  <div className='search-pol'>
                    <Form.Item name="blNo" >
                      <AnimInput label="提单号" />
                    </Form.Item>
                  </div>
                </div>
                <div className="search-ports" style={{ margin: '0 0 0 15px' }}>
                  <div className='search-pod'>
                    <Form.Item name="pod" >
                      <AnimPorts label="目的港" />
                    </Form.Item>
                  </div>
                </div>
                <div className="search-atd">
                  <Form.Item name="atdRange" >
                    <AnimDate label="船期" beRange={true} />
                  </Form.Item>
                </div>
                <Button htmlType="submit" className="search-btn" loading={searchLoading}>查 询</Button>
              </div>

              <QueueAnim className="search-expand">
                {isExpand ? [
                  <Row gutter={[16, 0]} key="a">
                    <Col span={6}>
                      <Form.Item name="pol" label="起运港" labelCol={{ flex: '80px' }}>
                        <Port placeholder="请选择" label="起运港" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item name="shipCompany" label="船公司" labelCol={{ flex: '80px' }}>
                        <ShipCompany placeholder="请选择" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item name="bookCode" label="订舱编号" labelCol={{ flex: '80px' }}>
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item name="vessel" label="船名" labelCol={{ flex: '80px' }}>
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>,
                  <Row gutter={[16, 0]} key="b">
                    <Col span={6}>
                      <Form.Item name="voyage" label="航次" labelCol={{ flex: '80px' }}>
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>
                ] : null}
              </QueueAnim>

              <div className='search-tool'>
                <div className="tool-btn">
                  <Button type="text" icon={<RedoOutlined />} size="small" htmlType="reset">
                    重置
                  </Button>
                  {!isExpand ?
                    <Button type="text" icon={<DownOutlined />} size="small" onClick={() => setIsExpand(true)}>展开</Button>
                    :
                    <Button type="text" icon={<UpOutlined />} size="small" onClick={() => setIsExpand(false)}>收起</Button>
                  }
                </div>
              </div>
            </Form>
            <Divider style={{ margin: '20px 0' }} />
            <div className='order-list'>
              <div className="order-tools">
                <div className="order-filter">
                  <Button type='primary' >导出</Button>
                </div>
                <div className="order-tools-btn">
                  <Space>
                    <Button type="text" icon={<BarsOutlined style={listType == 'table' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('table')}></Button>
                    <Button type="text" icon={<AppstoreOutlined style={listType == 'card' ? { color: '#B4141B' } : {}} />} onClick={() => setListType('card')}></Button>
                  </Space>
                </div>
              </div>

              {listType == 'table' ?
                <Table
                  className='order-table'
                  rowKey='id'
                  pagination={false}
                  style={{ marginTop: '16px', border: '1px solid #f0f0f0', borderRadius: '6px' }}
                  rowSelection={{ selectedRowKeys, onChange: onSelectChange }}
                  columns={orderColumns}
                  dataSource={orders}
                  expandable={{
                    expandedRowRender: (record) => (
                      <div className='order-expand'>
                        <p><span>船司：</span>{record.shipCompany}  <span>起运港：</span>{record.pol}  <span>目的港：</span>{record.pod} <span>ETD：</span>{dateFormat(record.etd)} </p>
                        <p><span>中转直达：</span>{record.ttOrDt === '中转' ? record.ttPol + ' 中转' : '直达'}   <span>ATD：</span>{dateFormat(record.atd)}
                          <span>截单时间：</span>{dateFormat(record.cot, 'yyyy-MM-dd hh:mm')} </p>
                      </div>
                    ),
                    expandIcon: ({ expanded, onExpand, record }) =>
                      expanded ?
                        (<DownOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                        :
                        (<RightOutlined style={{ color: '#cccccc' }} onClick={e => onExpand(record, e)} />)
                  }}
                />
                :
                <>
                  <div className='card-header'>
                    <div className='header-item' style={{ width: '180px', border: 0, textAlign: 'left' }}>
                      <Checkbox /> <span style={{ marginLeft: '16px' }}>委托单位</span>
                    </div>
                    <div className='header-item' style={{ width: '360px', textAlign: 'left' }}>基础信息</div>
                    <div className='header-item' style={{ width: '100px' }}>箱型箱量</div>
                    <div className='header-item' style={{ width: '120px' }}>件数</div>
                    <div className='header-item' style={{ width: '120px' }}>重量</div>
                    <div className='header-item' style={{ width: '120px' }}>尺寸</div>
                  </div>

                  {orders.map((r) => <div className='card-list'>
                    <div className='card-item-top'>
                      <div className='card-item-topLeft'>
                        <div className='card-item-checkbox'>
                          <Checkbox />
                        </div>
                        <div className='card-item-blNo'>
                          提单号<div className='blNo-text' title={r.blNo}>{r.blNo}</div><RightOutlined style={{ color: '#cccccc' }} onClick={() => openDetail(r.id)} />
                        </div>
                        <div className='card-item-vessel'>
                          船名航次<div className='vessel-text' title={r.vessel + ' / ' + r.voyage}>{r.vessel} / {r.voyage}</div><RightOutlined style={{ color: '#cccccc' }} onClick={() => openVessel(r.vessel)} />
                        </div>
                      </div>

                      <div className='card-item-topRight'>
                        <Steps progressDot labelPlacement="vertical" size='small' current={r.trackState}>
                          {steps.map((item, i) => (
                            <Steps.Step
                              key={i}
                              flex={1}
                              title={(<span style={{ fontSize: '12px' }}>{item.title}</span>)}
                            />
                          ))}
                        </Steps>
                      </div>
                    </div>
                    <div className='card-item-middle'>
                      <div className='card-item-checkbox'></div>
                      <div className='card-item-entrust'>
                        <p>{r.entrustCustomer}</p>
                      </div>
                      <div className='card-item-logo'>
                        <img src={shipmentIcon(r.shipCompany)} />
                      </div>
                      <div className='card-item-ports'>
                        <p>{r.shipCompany}</p>
                        <p style={{ color: '#999', fontSize: '12px' }}>{r.pol} → {r.pod}</p>
                      </div>
                      <div className='card-item-boxInfo'>
                        <span>{r.boxInfo}</span>
                      </div>

                      <div className='card-item-order' style={{ textAlign: 'left' }}>
                        <span>{r.cargoPkgs || ''}</span><span className='order-cargo'>{' ' + (r.cargoUnits || '')}</span>
                      </div>
                      <div className='card-item-order' style={{ textAlign: 'center' }}>
                        <span>{r.cargoWeight}</span><span className='order-cargo'>{' ' + 'KGS'}</span>
                      </div>
                      <div className='card-item-order' style={{ textAlign: 'right' }}>
                        <span>{r.cargoSize}</span><span className='order-cargo'>{' ' + 'CBM'}</span>
                      </div>
                    </div>
                    <div className='card-item-bottom'>
                      <div className='card-item-extend'>
                        <span>订舱编号：</span>{r.bookCode} <span>船名航次：</span>{r.vessel} /{r.voyage}  <span>ETD：{dateFormat(r.etd)}</span> <span>ATD：{dateFormat(r.atd)}</span>
                        <span>截单时间：</span>{dateFormat(r.cot, 'yyyy-MM-dd hh:mm')}
                      </div>
                      <div className='card-item-traceBtn'>
                        <Button size='small' type='primary' onClick={() => openTrace(r.id)}>运踪详情</Button>
                      </div>
                    </div>
                  </div>)}

                  {orders.length == 0 && <NoData />}

                </>}
            </div>
            <div className='order-pagination'>
              <Pagination
                pageSizeOptions={[10, 20, 50]}
                showSizeChanger
                onShowSizeChange={onShowSizeChange}
                onChange={onShowSizeChange}
                // defaultCurrent={p}
                total={total || 0}
                current={pageNo || 0}
                pageSize={pageSize || 10}
                showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`}
              />

            </div>
          </div>
        </div>
      </div>
      {/* 运踪 */}
      <Track
        open={showTrace}
        current={trackInfo}
        onCancel={() => setShowTrace(false)}
      />
      {/* 船舶轨迹 */}
      <Modal
        title="船舶轨迹"
        open={showVessel}
        width={1400}
        styles={{ body: { height: 'calc(100vh - 200px)' } }}
        centered
        footer={null}
        onCancel={() => setShowVessel(false)}
      >
        <Position vessel={vessel} />
      </Modal>
      {/* 提单详情 */}
      <Detail
        open={showDetail}
        orderId={orderId}
        onCancel={() => { setOrderId(''); setShowDetail(false) }}
      />
    </>
  );
};
