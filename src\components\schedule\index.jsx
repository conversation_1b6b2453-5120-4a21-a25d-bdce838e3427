import React, { useRef, useEffect, useState } from 'react';
import { Select } from 'antd';
import './index.less';
import { api } from '@/common/utils';
import debounce from 'lodash/debounce';


export default (props) => {
    // console.log(props, 'props')
    const [value, setValue] = useState();
    const delayedQuery = useRef(debounce((value, beOnline) => searchData(value, beOnline), 300)).current;
    const [points, setPoints] = useState([]);
    const shipSchedulesSelect = [{ key: 0, value: '周日' }, { key: 1, value: "周一" }, { key: 2, value: "周二" }, { key: 3, value: "周三" }, { key: 4, value: "周四" }, { key: 5, value: "周五" }, { key: 6, value: "周六" }];
    useEffect(() => {

        searchData('', props.beOnline);
    }, [props.beOnline]);
    const handleChange = (selectItem) => {
        console.log(selectItem, 'selectItem')
        if (selectItem) {
            let values = []
            selectItem.map((item) => {
                values.push(Number(item.key))
            })
            props.onChange(values || []);
        } else {
            props.onChange('');
        }
    };

    const searchData = () => {
        let data = [];
        data = shipSchedulesSelect.map((item) => {
            return {
                value: item.key,
                label: item.value
            }
        })
        setPoints(data)
    };


    return (
        <div className="anim-Carrier">
            <Select
                {...props}
                value={value}
                mode='multiple'
                maxTagCount={2}
                allowClear
                style={{ width: props.width || '100%', border: 'none', paddingLeft: '8px' }}
                showSearch
                labelInValue
                filterOption={false}
                onClear={() => { handleChange() }}
                onChange={(selectItem) => { handleChange(selectItem) }}
                onSearch={(v) => {
                    delayedQuery(v, props.beOnline);
                }}
            >
                {points.map((item) => (
                    <Select.Option key={item.value}>{item.label}</Select.Option>
                ))}
            </Select>
            <label>{props.label}</label>
        </div>
    );
}
