import React, { useRef, useEffect, useState } from 'react';
import { Select } from 'antd';
import './index.less';
import { api } from '@/common/utils';
import debounce from 'lodash/debounce';


export default (props) => {
    const [value, setValue] = useState();
    const delayedQuery = useRef(debounce((value) => searchData(value), 300)).current;
    const [points, setPoints] = useState([]);

    useEffect(() => {
        searchData('');
    }, []);

    const handleChange = (selectItem) => {
        if (selectItem) {
            console.log(123)
            const values = selectItem.value;
            props.onChange(values || '');
        } else {
            props.onChange('');
        }
    };

    const searchData = (keywords) => {
        api.base.listLine(keywords).subscribe({
            next: (body) => {
                let data = [];
                data = body.map((item) => ({
                    value: item.id,
                    label: item.cnName,
                }));
                setPoints(data);
            },
        });
    };


    return (
        <div className="anim-Carrier">
            <Select
                {...props}
                value={value}
                allowClear style={{ width: props.width || '100%', border: 'none', paddingLeft: '8px' }}
                showSearch
                labelInValue
                filterOption={false}
                onClear={() => { handleChange() }}
                onChange={(selectItem) => { handleChange(selectItem) }}
                onSearch={(v) => {
                    delayedQuery(v);
                }}
            >
                {points.map((item) => (
                    <Select.Option key={item.value}>{item.label}</Select.Option>
                ))}
            </Select>
            <label>{props.label}</label>
        </div>
    );
}
